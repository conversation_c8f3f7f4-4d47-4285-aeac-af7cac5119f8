/**
 * PerformanceMonitor - Performance monitoring for location detection
 * Tracks timing, success rates, and provides optimization insights
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            locationDetection: {
                totalAttempts: 0,
                successfulAttempts: 0,
                failedAttempts: 0,
                averageTime: 0,
                totalTime: 0,
                gpsAttempts: 0,
                gpsSuccesses: 0,
                ipAttempts: 0,
                ipSuccesses: 0,
                cacheHits: 0,
                cacheAttempts: 0,
                timeouts: 0,
                permissionDenials: 0,
            },
            apiPerformance: {
                gpsApiTimes: [],
                ipApiTimes: [],
                cacheAccessTimes: [],
            },
            userExperience: {
                blockingOperations: 0,
                nonBlockingOperations: 0,
                uiUpdateDelays: [],
            },
        };

        this.activeTimers = new Map();
        this.performanceThresholds = {
            maxLocationDetectionTime: 10000, // 10 seconds
            maxGpsTime: 8000, // 8 seconds
            maxIpTime: 5000, // 5 seconds
            maxCacheTime: 100, // 100ms
            maxUiUpdateDelay: 500, // 500ms
        };

        this.isEnabled = true;
        this.debugMode = false;
    }

    /**
     * Start timing a performance operation
     * @param {string} operationId - Unique identifier for the operation
     * @param {string} operationType - Type of operation (gps, ip, cache, etc.)
     * @param {Object} metadata - Additional metadata about the operation
     */
    startTiming(operationId, operationType, metadata = {}) {
        if (!this.isEnabled) return;

        const startTime = performance.now();
        this.activeTimers.set(operationId, {
            startTime,
            operationType,
            metadata,
        });

        if (this.debugMode) {
            console.log(`[PerformanceMonitor] Started timing ${operationType}:${operationId}`, metadata);
        }
    }

    /**
     * End timing and record the result
     * @param {string} operationId - Operation identifier
     * @param {boolean} success - Whether the operation was successful
     * @param {Object} result - Operation result data
     */
    endTiming(operationId, success = true, result = {}) {
        if (!this.isEnabled || !this.activeTimers.has(operationId)) return;

        const endTime = performance.now();
        const timerData = this.activeTimers.get(operationId);
        const duration = endTime - timerData.startTime;

        this.activeTimers.delete(operationId);

        // Record metrics based on operation type
        this.recordMetrics(timerData.operationType, duration, success, {
            ...timerData.metadata,
            ...result,
        });

        if (this.debugMode) {
            console.log(`[PerformanceMonitor] Completed ${timerData.operationType}:${operationId}`, {
                duration: `${duration.toFixed(2)}ms`,
                success,
                result,
            });
        }

        // Check for performance issues
        this.checkPerformanceThresholds(timerData.operationType, duration);

        return duration;
    }

    /**
     * Record metrics for different operation types
     * @private
     */
    recordMetrics(operationType, duration, success, metadata) {
        const metrics = this.metrics.locationDetection;

        switch (operationType) {
            case 'location-detection':
                metrics.totalAttempts++;
                metrics.totalTime += duration;
                metrics.averageTime = metrics.totalTime / metrics.totalAttempts;
                if (success) {
                    metrics.successfulAttempts++;
                } else {
                    metrics.failedAttempts++;
                }
                break;

            case 'gps':
                metrics.gpsAttempts++;
                this.metrics.apiPerformance.gpsApiTimes.push(duration);
                if (success) {
                    metrics.gpsSuccesses++;
                } else if (metadata.errorType === 'TIMEOUT') {
                    metrics.timeouts++;
                } else if (metadata.errorType === 'PERMISSION_DENIED') {
                    metrics.permissionDenials++;
                }
                break;

            case 'ip':
                metrics.ipAttempts++;
                this.metrics.apiPerformance.ipApiTimes.push(duration);
                if (success) {
                    metrics.ipSuccesses++;
                }
                break;

            case 'cache':
                metrics.cacheAttempts++;
                this.metrics.apiPerformance.cacheAccessTimes.push(duration);
                if (success) {
                    metrics.cacheHits++;
                }
                break;

            case 'ui-update':
                this.metrics.userExperience.uiUpdateDelays.push(duration);
                if (metadata.blocking) {
                    this.metrics.userExperience.blockingOperations++;
                } else {
                    this.metrics.userExperience.nonBlockingOperations++;
                }
                break;
        }

        // Keep API time arrays manageable
        this.trimApiTimeArrays();
    }

    /**
     * Check if operation exceeded performance thresholds
     * @private
     */
    checkPerformanceThresholds(operationType, duration) {
        const thresholds = this.performanceThresholds;
        let threshold;

        switch (operationType) {
            case 'location-detection':
                threshold = thresholds.maxLocationDetectionTime;
                break;
            case 'gps':
                threshold = thresholds.maxGpsTime;
                break;
            case 'ip':
                threshold = thresholds.maxIpTime;
                break;
            case 'cache':
                threshold = thresholds.maxCacheTime;
                break;
            case 'ui-update':
                threshold = thresholds.maxUiUpdateDelay;
                break;
            default:
                return;
        }

        if (duration > threshold) {
            console.warn(`[PerformanceMonitor] ${operationType} operation exceeded threshold`, {
                duration: `${duration.toFixed(2)}ms`,
                threshold: `${threshold}ms`,
                exceedBy: `${(duration - threshold).toFixed(2)}ms`,
            });
        }
    }

    /**
     * Trim API time arrays to prevent memory leaks
     * @private
     */
    trimApiTimeArrays() {
        const maxEntries = 100;
        const apiPerf = this.metrics.apiPerformance;

        if (apiPerf.gpsApiTimes.length > maxEntries) {
            apiPerf.gpsApiTimes = apiPerf.gpsApiTimes.slice(-maxEntries);
        }
        if (apiPerf.ipApiTimes.length > maxEntries) {
            apiPerf.ipApiTimes = apiPerf.ipApiTimes.slice(-maxEntries);
        }
        if (apiPerf.cacheAccessTimes.length > maxEntries) {
            apiPerf.cacheAccessTimes = apiPerf.cacheAccessTimes.slice(-maxEntries);
        }
    }

    /**
     * Get comprehensive performance report
     * @returns {Object} Performance metrics and analysis
     */
    getPerformanceReport() {
        const metrics = this.metrics.locationDetection;
        const apiPerf = this.metrics.apiPerformance;
        const uxMetrics = this.metrics.userExperience;

        return {
            summary: {
                totalAttempts: metrics.totalAttempts,
                successRate: metrics.totalAttempts > 0 ? (metrics.successfulAttempts / metrics.totalAttempts) * 100 : 0,
                averageTime: metrics.averageTime,
                cacheHitRate: metrics.cacheAttempts > 0 ? (metrics.cacheHits / metrics.cacheAttempts) * 100 : 0,
            },
            detailedMetrics: {
                gps: {
                    attempts: metrics.gpsAttempts,
                    successRate: metrics.gpsAttempts > 0 ? (metrics.gpsSuccesses / metrics.gpsAttempts) * 100 : 0,
                    averageTime: this.calculateAverage(apiPerf.gpsApiTimes),
                    timeouts: metrics.timeouts,
                    permissionDenials: metrics.permissionDenials,
                },
                ip: {
                    attempts: metrics.ipAttempts,
                    successRate: metrics.ipAttempts > 0 ? (metrics.ipSuccesses / metrics.ipAttempts) * 100 : 0,
                    averageTime: this.calculateAverage(apiPerf.ipApiTimes),
                },
                cache: {
                    attempts: metrics.cacheAttempts,
                    hitRate: metrics.cacheAttempts > 0 ? (metrics.cacheHits / metrics.cacheAttempts) * 100 : 0,
                    averageTime: this.calculateAverage(apiPerf.cacheAccessTimes),
                },
            },
            userExperience: {
                blockingOperations: uxMetrics.blockingOperations,
                nonBlockingOperations: uxMetrics.nonBlockingOperations,
                averageUiUpdateDelay: this.calculateAverage(uxMetrics.uiUpdateDelays),
                blockingRatio: uxMetrics.blockingOperations + uxMetrics.nonBlockingOperations > 0 ? (uxMetrics.blockingOperations / (uxMetrics.blockingOperations + uxMetrics.nonBlockingOperations)) * 100 : 0,
            },
            recommendations: this.generateRecommendations(),
        };
    }

    /**
     * Generate performance optimization recommendations
     * @private
     */
    generateRecommendations() {
        const recommendations = [];
        const metrics = this.metrics.locationDetection;
        const apiPerf = this.metrics.apiPerformance;

        // Check GPS performance
        if (metrics.gpsAttempts > 0) {
            const gpsSuccessRate = (metrics.gpsSuccesses / metrics.gpsAttempts) * 100;
            if (gpsSuccessRate < 50) {
                recommendations.push({
                    type: 'gps',
                    priority: 'high',
                    message: 'GPS success rate is low. Consider adjusting timeout or fallback strategy.',
                    metric: `${gpsSuccessRate.toFixed(1)}% success rate`,
                });
            }

            const avgGpsTime = this.calculateAverage(apiPerf.gpsApiTimes);
            if (avgGpsTime > this.performanceThresholds.maxGpsTime) {
                recommendations.push({
                    type: 'gps',
                    priority: 'medium',
                    message: 'GPS detection is slow. Consider reducing timeout or improving fallback.',
                    metric: `${avgGpsTime.toFixed(0)}ms average time`,
                });
            }
        }

        // Check cache performance
        if (metrics.cacheAttempts > 0) {
            const cacheHitRate = (metrics.cacheHits / metrics.cacheAttempts) * 100;
            if (cacheHitRate < 30) {
                recommendations.push({
                    type: 'cache',
                    priority: 'medium',
                    message: 'Cache hit rate is low. Consider increasing cache duration or improving cache strategy.',
                    metric: `${cacheHitRate.toFixed(1)}% hit rate`,
                });
            }
        }

        // Check overall performance
        if (metrics.averageTime > this.performanceThresholds.maxLocationDetectionTime) {
            recommendations.push({
                type: 'overall',
                priority: 'high',
                message: 'Location detection is taking too long. Consider optimizing timeout values.',
                metric: `${metrics.averageTime.toFixed(0)}ms average time`,
            });
        }

        // Check user experience
        const uxMetrics = this.metrics.userExperience;
        if (uxMetrics.blockingOperations + uxMetrics.nonBlockingOperations > 0) {
            const blockingRatio = (uxMetrics.blockingOperations / (uxMetrics.blockingOperations + uxMetrics.nonBlockingOperations)) * 100;
            if (blockingRatio > 20) {
                recommendations.push({
                    type: 'ux',
                    priority: 'high',
                    message: 'Too many blocking operations detected. Ensure location detection is non-blocking.',
                    metric: `${blockingRatio.toFixed(1)}% blocking operations`,
                });
            }
        }

        return recommendations;
    }

    /**
     * Calculate average from array of numbers
     * @private
     */
    calculateAverage(numbers) {
        if (!numbers || numbers.length === 0) return 0;
        return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
    }

    /**
     * Reset all metrics
     */
    resetMetrics() {
        this.metrics = {
            locationDetection: {
                totalAttempts: 0,
                successfulAttempts: 0,
                failedAttempts: 0,
                averageTime: 0,
                totalTime: 0,
                gpsAttempts: 0,
                gpsSuccesses: 0,
                ipAttempts: 0,
                ipSuccesses: 0,
                cacheHits: 0,
                cacheAttempts: 0,
                timeouts: 0,
                permissionDenials: 0,
            },
            apiPerformance: {
                gpsApiTimes: [],
                ipApiTimes: [],
                cacheAccessTimes: [],
            },
            userExperience: {
                blockingOperations: 0,
                nonBlockingOperations: 0,
                uiUpdateDelays: [],
            },
        };
        this.activeTimers.clear();
    }

    /**
     * Enable or disable performance monitoring
     * @param {boolean} enabled - Whether to enable monitoring
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled) {
            this.activeTimers.clear();
        }
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} debug - Whether to enable debug logging
     */
    setDebugMode(debug) {
        this.debugMode = debug;
    }

    /**
     * Update performance thresholds
     * @param {Object} thresholds - New threshold values
     */
    updateThresholds(thresholds) {
        this.performanceThresholds = { ...this.performanceThresholds, ...thresholds };
    }

    /**
     * Get current performance status
     * @returns {Object} Current performance status
     */
    getStatus() {
        return {
            enabled: this.isEnabled,
            debugMode: this.debugMode,
            activeTimers: this.activeTimers.size,
            thresholds: this.performanceThresholds,
        };
    }
}

export default PerformanceMonitor;
