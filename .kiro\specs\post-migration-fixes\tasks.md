# Implementation Plan

-   [ ] 1. Create video request service utility

    -   Create `src/utils/videoRequest.js` file with dedicated axios instance for video backend
    -   Configure hardcoded baseURL to `http://192.168.3.62:18080`
    -   Copy all interceptors, error handling, and authentication logic from main request service
    -   Implement identical download function for video-related file downloads
    -   _Requirements: 1.1, 3.1_

-   [ ] 2. Update video API modules to use video request service

    -   [ ] 2.1 Update core video API files (Part 1)

        -   Modify `src/api/video/device.js` to import videoRequest instead of request
        -   Modify `src/api/video/platform.js` to import videoRequest instead of request
        -   Modify `src/api/video/user.js` to import videoRequest instead of request
        -   Modify `src/api/video/server.js` to import videoRequest instead of request
        -   Test that all function signatures remain identical
        -   _Requirements: 1.1, 3.1_

    -   [ ] 2.2 Update streaming and playback API files (Part 2)

        -   Modify `src/api/video/play.js` to import videoRequest instead of request
        -   Modify `src/api/video/playback.js` to import videoRequest instead of request
        -   Modify `src/api/video/streamProxy.js` to import videoRequest instead of request
        -   Modify `src/api/video/streamPush.js` to import videoRequest instead of request
        -   Verify streaming functionality uses correct backend endpoint
        -   _Requirements: 1.1, 3.1_

    -   [ ] 2.3 Update channel and group management API files (Part 3)

        -   Modify `src/api/video/commonChannel.js` to import videoRequest instead of request
        -   Modify `src/api/video/group.js` to import videoRequest instead of request
        -   Modify `src/api/video/region.js` to import videoRequest instead of request
        -   Modify `src/api/video/role.js` to import videoRequest instead of request
        -   Test channel and group operations with video backend
        -   _Requirements: 1.1, 3.1_

    -   [ ] 2.4 Update recording and logging API files (Part 4)

        -   Modify `src/api/video/cloudRecord.js` to import videoRequest instead of request
        -   Modify `src/api/video/gbRecord.js` to import videoRequest instead of request
        -   Modify `src/api/video/recordPlan.js` to import videoRequest instead of request
        -   Modify `src/api/video/log.js` to import videoRequest instead of request
        -   Verify recording functionality connects to video backend
        -   _Requirements: 1.1, 3.1_

    -   [ ] 2.5 Update remaining video API files (Part 5)
        -   Modify `src/api/video/frontEnd.js` to import videoRequest instead of request
        -   Modify `src/api/video/table.js` to import videoRequest instead of request
        -   Modify `src/api/video/userApiKey.js` to import videoRequest instead of request
        -   Ensure all video API modules are updated consistently
        -   _Requirements: 1.1, 3.1_

-   [ ] 3. Verify dual backend configuration

    -   Test that non-video API calls continue using original backend service
    -   Test that video API calls use the new video backend service (http://192.168.3.62:18080)
    -   Verify authentication works correctly with both backend services
    -   Confirm .env.development file remains unchanged
    -   _Requirements: 2.1, 2.2, 3.2_

-   [ ] 4. Create configuration documentation
    -   Document which APIs use which backend service in code comments
    -   Add clear comments in videoRequest.js explaining the dual backend setup
    -   Document the video backend URL configuration for future maintenance
    -   _Requirements: 4.1, 4.2_
