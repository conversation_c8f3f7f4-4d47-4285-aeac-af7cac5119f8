# Design Document

## Overview

The system requires a dual backend service architecture where video-related modules use a dedicated backend service (http://************:18080) while all other modules continue using the existing backend configuration. This design implements a clean separation of concerns without modifying the existing .env.development configuration.

## Architecture

### Current Architecture

-   Single axios instance (`src/utils/request.js`) with `baseURL: process.env.VUE_APP_BASE_API`
-   All API calls use the same backend service configured in environment variables
-   Video APIs located in `src/api/video/` directory use the same request utility as other modules

### Target Architecture

-   **Primary Request Service**: Existing axios instance for non-video modules (unchanged)
-   **Video Request Service**: New dedicated axios instance for video modules only
-   **API Layer Separation**: Video APIs will use the dedicated service, other APIs remain unchanged
-   **Environment Configuration**: .env.development remains untouched, video backend URL configured separately

## Components and Interfaces

### 1. Video Request Service (`src/utils/videoRequest.js`)

-   New axios instance specifically for video API calls
-   Hardcoded baseURL: `http://************:18080`
-   Same interceptors and error handling as the main request service
-   Same authentication and token handling

### 2. Video API Module Updates

All files in `src/api/video/` directory need to be updated to import and use the new video request service:

-   `src/api/video/cloudRecord.js`
-   `src/api/video/commonChannel.js`
-   `src/api/video/device.js`
-   `src/api/video/frontEnd.js`
-   `src/api/video/gbRecord.js`
-   `src/api/video/group.js`
-   `src/api/video/log.js`
-   `src/api/video/platform.js`
-   `src/api/video/play.js`
-   `src/api/video/playback.js`
-   `src/api/video/recordPlan.js`
-   `src/api/video/region.js`
-   `src/api/video/role.js`
-   `src/api/video/server.js`
-   `src/api/video/streamProxy.js`
-   `src/api/video/streamPush.js`
-   `src/api/video/table.js`
-   `src/api/video/user.js`
-   `src/api/video/userApiKey.js`

### 3. Configuration Management

-   Video backend URL will be defined as a constant in the video request service
-   Future environment-based configuration can be added without affecting existing setup
-   Clear documentation of which APIs use which backend service

## Data Models

### Request Configuration

```javascript
// Main system requests (unchanged)
{
  baseURL: process.env.VUE_APP_BASE_API, // '/dev-api'
  timeout: 20000,
  // ... existing configuration
}

// Video system requests (new)
{
  baseURL: 'http://************:18080',
  timeout: 20000,
  // ... same configuration as main system
}
```

### API Import Pattern

```javascript
// Before (all APIs)
import request from '@/utils/request';

// After (video APIs only)
import request from '@/utils/videoRequest';

// After (non-video APIs - unchanged)
import request from '@/utils/request';
```

## Error Handling

### Consistent Error Handling

-   Video request service will implement identical error handling to main request service
-   Same authentication flow and token management
-   Same retry logic and timeout handling
-   Consistent error messages and user experience

### Service-Specific Error Context

-   Error messages will clearly indicate which backend service failed
-   Debugging information will include service endpoint for troubleshooting
-   Network errors will be differentiated between main and video services

## Testing Strategy

### Unit Testing

-   Test video request service configuration and interceptors
-   Verify API calls use correct backend endpoints
-   Test error handling scenarios for both services

### Integration Testing

-   Verify video modules connect to video backend service
-   Verify non-video modules continue using main backend service
-   Test authentication flow works with both services

### Manual Testing

-   Login functionality with main backend service
-   Video module functionality with dedicated backend service
-   Cross-module functionality to ensure no interference

## Implementation Approach

### Phase 1: Create Video Request Service

1. Create `src/utils/videoRequest.js` based on existing `src/utils/request.js`
2. Configure hardcoded video backend URL
3. Ensure identical functionality to main request service

### Phase 2: Update Video API Modules

1. Update all video API files to use new video request service
2. Maintain identical API interfaces and function signatures
3. Test each video API module individually

### Phase 3: Validation and Testing

1. Verify system startup and login functionality
2. Test video module functionality with new backend
3. Ensure non-video modules remain unaffected

## Security Considerations

-   Both request services will use identical authentication mechanisms
-   Token handling will be consistent across both services
-   CORS configuration may need to be verified for the new video backend
-   Same security headers and request validation for both services
