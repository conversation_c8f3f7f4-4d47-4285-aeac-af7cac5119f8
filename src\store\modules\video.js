import server from './video/server';
import device from './video/device';
import play from './video/play';
import user from './video/user';
import app from './video/app';
import platform from './video/platform';
import streamProxy from './video/streamProxy';
import streamPush from './video/streamPush';
import gbRecord from './video/gbRecord';
import cloudRecord from './video/cloudRecord';
import playback from './video/playback';
import recordPlan from './video/recordPlan';
import log from './video/log';
import role from './video/role';
import userApiKeys from './video/userApiKeys';
import region from './video/region';
import group from './video/group';
import commonChanel from './video/commonChanel';
import frontEnd from './video/frontEnd';
import settings from './video/settings';
import tagsView from './video/tagsView';
import department from './video/department';

const state = {
    // 视频相关状态
    currentStream: null,
    playingChannels: [],
    deviceList: [],
    channelList: [],
};

const mutations = {
    SET_CURRENT_STREAM(state, stream) {
        state.currentStream = stream;
    },
    ADD_PLAYING_CHANNEL(state, channel) {
        state.playingChannels.push(channel);
    },
    REMOVE_PLAYING_CHANNEL(state, channelId) {
        state.playingChannels = state.playingChannels.filter((ch) => ch.id !== channelId);
    },
    SET_DEVICE_LIST(state, list) {
        state.deviceList = list;
    },
    SET_CHANNEL_LIST(state, list) {
        state.channelList = list;
    },
};

const actions = {
    // 播放通道
    playChannel({ commit }, channelId) {
        return new Promise((resolve, reject) => {
            // 这里应该调用实际的API
            console.log('播放通道:', channelId);

            // 模拟API响应
            const streamInfo = {
                stream: 'stream_' + channelId,
                flv: 'http://example.com/stream.flv',
                hls: 'http://example.com/stream.m3u8',
                rtmp: 'rtmp://example.com/stream',
            };

            commit('SET_CURRENT_STREAM', streamInfo);
            resolve(streamInfo);
        });
    },

    // 停止播放
    stopPlay({ commit }, channelIds) {
        return new Promise((resolve, reject) => {
            console.log('停止播放:', channelIds);

            channelIds.forEach((id) => {
                commit('REMOVE_PLAYING_CHANNEL', id);
            });

            resolve();
        });
    },

    // 获取设备列表
    getDeviceList({ commit }, params) {
        return new Promise((resolve, reject) => {
            console.log('获取设备列表:', params);

            // 模拟数据
            const mockData = {
                list: [],
                total: 0,
            };

            commit('SET_DEVICE_LIST', mockData.list);
            resolve(mockData);
        });
    },

    // 获取通道列表
    getChannelList({ commit }, params) {
        return new Promise((resolve, reject) => {
            console.log('获取通道列表:', params);

            // 模拟数据
            const mockData = {
                list: [],
                total: 0,
            };

            commit('SET_CHANNEL_LIST', mockData.list);
            resolve(mockData);
        });
    },
};

const getters = {
    currentStream: (state) => state.currentStream,
    playingChannels: (state) => state.playingChannels,
    deviceList: (state) => state.deviceList,
    channelList: (state) => state.channelList,
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
    modules: {
        server,
        device,
        play,
        user,
        app,
        platform,
        streamProxy,
        streamPush,
        gbRecord,
        cloudRecord,
        playback,
        recordPlan,
        log,
        role,
        userApiKeys,
        region,
        group,
        commonChanel,
        frontEnd,
        settings,
        tagsView,
        department,
    },
};
