/**
 * Configuration Validator for Location Detection
 * Validates environment variables and configuration settings
 */

class ConfigValidator {
    /**
     * Validate all location detection configuration
     * @param {Object} config - Configuration object to validate
     * @returns {Object} Validation result with errors and warnings
     */
    static validateConfiguration(config) {
        const result = {
            isValid: true,
            errors: [],
            warnings: [],
            recommendations: [],
        };

        // Validate feature flags
        this.validateFeatureFlags(config, result);

        // Validate timeout settings
        this.validateTimeouts(config, result);

        // Validate API configuration
        this.validateApiConfiguration(config, result);

        // Validate coordinates
        this.validateCoordinates(config, result);

        // Validate cache settings
        this.validateCacheSettings(config, result);

        // Check for potential issues
        this.checkPotentialIssues(config, result);

        result.isValid = result.errors.length === 0;
        return result;
    }

    /**
     * Validate feature flag settings
     * @param {Object} config - Configuration object
     * @param {Object} result - Validation result object
     */
    static validateFeatureFlags(config, result) {
        // Check if location detection is enabled but no methods are available
        if (config.enabled && !config.enableIPFallback) {
            if (!this.isGeolocationAvailable()) {
                result.errors.push('Location detection is enabled but GPS is not available and IP fallback is disabled');
            }
        }

        // Warn if high accuracy is enabled but may impact performance
        if (config.enableHighAccuracy) {
            result.warnings.push('High accuracy GPS is enabled - this may impact battery life and performance');
        }

        // Check if debug logging is enabled in production
        if (config.enableDebugLogging && process.env.NODE_ENV === 'production') {
            result.warnings.push('Debug logging is enabled in production environment');
        }
    }

    /**
     * Validate timeout settings
     * @param {Object} config - Configuration object
     * @param {Object} result - Validation result object
     */
    static validateTimeouts(config, result) {
        const timeouts = ['timeout', 'gpsTimeout', 'ipTimeout'];

        timeouts.forEach((timeoutKey) => {
            const value = config[timeoutKey];

            if (typeof value !== 'number' || value <= 0) {
                result.errors.push(`${timeoutKey} must be a positive number`);
            } else if (value < 1000) {
                result.warnings.push(`${timeoutKey} is very short (${value}ms) - may cause premature timeouts`);
            } else if (value > 30000) {
                result.warnings.push(`${timeoutKey} is very long (${value}ms) - may impact user experience`);
            }
        });

        // Check timeout relationships
        if (config.gpsTimeout > config.timeout) {
            result.warnings.push('GPS timeout is longer than total timeout');
        }

        if (config.ipTimeout > config.timeout) {
            result.warnings.push('IP timeout is longer than total timeout');
        }
    }

    /**
     * Validate API configuration
     * @param {Object} config - Configuration object
     * @param {Object} result - Validation result object
     */
    static validateApiConfiguration(config, result) {
        if (!config.ipApiEndpoints || !Array.isArray(config.ipApiEndpoints)) {
            result.errors.push('IP API endpoints must be an array');
            return;
        }

        if (config.enableIPFallback && config.ipApiEndpoints.length === 0) {
            result.errors.push('IP fallback is enabled but no API endpoints are configured');
        }

        // Validate each endpoint
        config.ipApiEndpoints.forEach((endpoint, index) => {
            if (!endpoint.url || typeof endpoint.url !== 'string') {
                result.errors.push(`Endpoint ${index}: URL is required and must be a string`);
            } else {
                try {
                    new URL(endpoint.url);
                } catch (error) {
                    result.errors.push(`Endpoint ${index}: Invalid URL format`);
                }

                // Check for HTTPS in production
                if (process.env.NODE_ENV === 'production' && !endpoint.url.startsWith('https://')) {
                    result.warnings.push(`Endpoint ${index}: Using HTTP in production may cause security issues`);
                }
            }

            if (endpoint.requiresKey && !config.ipApiKey) {
                result.warnings.push(`Endpoint ${index}: Requires API key but none is configured`);
            }

            if (typeof endpoint.timeout === 'number' && endpoint.timeout <= 0) {
                result.errors.push(`Endpoint ${index}: Timeout must be positive`);
            }
        });

        // Check for duplicate endpoints
        const urls = config.ipApiEndpoints.map((e) => e.url);
        const duplicates = urls.filter((url, index) => urls.indexOf(url) !== index);
        if (duplicates.length > 0) {
            result.warnings.push(`Duplicate API endpoints detected: ${duplicates.join(', ')}`);
        }
    }

    /**
     * Validate coordinate settings
     * @param {Object} config - Configuration object
     * @param {Object} result - Validation result object
     */
    static validateCoordinates(config, result) {
        const { defaultCenter } = config;

        if (!defaultCenter || typeof defaultCenter !== 'object') {
            result.errors.push('Default center coordinates are required');
            return;
        }

        const { lng, lat } = defaultCenter;

        if (typeof lng !== 'number' || lng < -180 || lng > 180) {
            result.errors.push('Default longitude must be a number between -180 and 180');
        }

        if (typeof lat !== 'number' || lat < -90 || lat > 90) {
            result.errors.push('Default latitude must be a number between -90 and 90');
        }

        // Check if coordinates are at null island (0,0)
        if (lng === 0 && lat === 0) {
            result.warnings.push('Default coordinates are set to (0,0) - this may not be intended');
        }
    }

    /**
     * Validate cache settings
     * @param {Object} config - Configuration object
     * @param {Object} result - Validation result object
     */
    static validateCacheSettings(config, result) {
        if (config.enableCache) {
            if (typeof config.cacheMaxAge !== 'number' || config.cacheMaxAge <= 0) {
                result.errors.push('Cache max age must be a positive number');
            } else if (config.cacheMaxAge < 60000) {
                // Less than 1 minute
                result.warnings.push('Cache max age is very short - may cause frequent location requests');
            }

            if (!config.sessionKey || typeof config.sessionKey !== 'string') {
                result.errors.push('Session key is required for caching');
            }

            // Check if sessionStorage is available
            if (typeof sessionStorage === 'undefined') {
                result.warnings.push('sessionStorage is not available - caching will not work');
            }
        }
    }

    /**
     * Check for potential configuration issues
     * @param {Object} config - Configuration object
     * @param {Object} result - Validation result object
     */
    static checkPotentialIssues(config, result) {
        // Check browser compatibility
        if (!this.isGeolocationAvailable()) {
            result.warnings.push('Geolocation API is not available in this browser');
        }

        if (!this.isSecureContext()) {
            result.warnings.push('Geolocation requires HTTPS or localhost - GPS detection may not work');
        }

        if (typeof fetch === 'undefined') {
            result.errors.push('Fetch API is not available - IP location detection will not work');
        }

        // Performance recommendations
        if (config.enabled && config.enableIPFallback && config.ipApiEndpoints.length > 3) {
            result.recommendations.push('Consider reducing the number of IP API endpoints to improve performance');
        }

        if (config.timeout > 15000) {
            result.recommendations.push('Consider reducing timeout to improve user experience');
        }

        if (!config.enableCache) {
            result.recommendations.push('Consider enabling caching to reduce API calls and improve performance');
        }
    }

    /**
     * Check if geolocation is available
     * @returns {boolean}
     */
    static isGeolocationAvailable() {
        return typeof navigator !== 'undefined' && 'geolocation' in navigator;
    }

    /**
     * Check if running in secure context
     * @returns {boolean}
     */
    static isSecureContext() {
        if (typeof window === 'undefined') return true; // Server-side
        return window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
    }

    /**
     * Generate configuration report
     * @param {Object} config - Configuration object
     * @returns {string} Human-readable configuration report
     */
    static generateReport(config) {
        const validation = this.validateConfiguration(config);

        let report = '=== Location Detection Configuration Report ===\n\n';

        // Status
        report += `Status: ${validation.isValid ? '✅ Valid' : '❌ Invalid'}\n\n`;

        // Errors
        if (validation.errors.length > 0) {
            report += '🚨 Errors:\n';
            validation.errors.forEach((error) => {
                report += `  - ${error}\n`;
            });
            report += '\n';
        }

        // Warnings
        if (validation.warnings.length > 0) {
            report += '⚠️  Warnings:\n';
            validation.warnings.forEach((warning) => {
                report += `  - ${warning}\n`;
            });
            report += '\n';
        }

        // Recommendations
        if (validation.recommendations.length > 0) {
            report += '💡 Recommendations:\n';
            validation.recommendations.forEach((rec) => {
                report += `  - ${rec}\n`;
            });
            report += '\n';
        }

        // Configuration summary
        report += '📋 Configuration Summary:\n';
        report += `  - Location Detection: ${config.enabled ? 'Enabled' : 'Disabled'}\n`;
        report += `  - GPS Detection: ${this.isGeolocationAvailable() ? 'Available' : 'Not Available'}\n`;
        report += `  - IP Fallback: ${config.enableIPFallback ? 'Enabled' : 'Disabled'}\n`;
        report += `  - Caching: ${config.enableCache ? 'Enabled' : 'Disabled'}\n`;
        report += `  - API Endpoints: ${config.ipApiEndpoints?.length || 0}\n`;
        report += `  - Total Timeout: ${config.timeout}ms\n`;

        return report;
    }
}

export default ConfigValidator;
