<template>
    <div style="padding: 6px">
        <tool-form
            :queryParamsProp="queryParams"
            :myGroupList="myGroupList"
            :showSearch.sync="showSearch"
            :showType="showType"
            @search="handleQuery"
            @reset="resetQuery"
            @queryTable="getList"
            @changeShowType="handleChangeShowType"
            @command="handleCommand"
            @export="handleExport"
            @batchDelete="handleBatchDelete"
        ></tool-form>

        <el-card style="padding-bottom: 100px" v-if="showType == 'list'">
            <el-table v-loading="loading" :data="deviceList" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column label="编号" align="center" header-align="center" prop="deviceId" width="50" />
                <el-table-column label="设备名称" align="center" header-align="center" prop="deviceName" min-width="100" />
                <el-table-column label="设备编号" align="center" prop="serialNumber" min-width="100" />
                <el-table-column label="所属产品" align="center" prop="productName" min-width="100" />
                <el-table-column label="电池电量" align="center" prop="batteryVoltage" min-width="100" />
                <el-table-column label="电池低电量报警" align="center" prop="batteryState" min-width="100" />
                <el-table-column label="信号值" align="center" prop="signal" min-width="100" />
                <el-table-column label="设备状态" align="center" prop="errorStatus" min-width="100" />
                <el-table-column label="浸水状态" align="center" prop="waterState" min-width="100" />
                <el-table-column label="经纬度" align="center" prop="lonAndLat" min-width="100" />
                <el-table-column label="状态" align="center" prop="status" width="80">
                    <template slot-scope="scope">
                        <el-tag size="small" type="info" v-if="scope.row.status === 1">未激活</el-tag>
                        <el-tag size="small" type="warning" v-else-if="scope.row.status === 2">禁用</el-tag>
                        <el-tag size="small" type="success" v-else-if="scope.row.status === 3">在线</el-tag>
                        <el-tag size="small" type="danger" v-else-if="scope.row.status === 4">离线</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="信号" align="center" prop="rssi" width="60">
                    <template slot-scope="scope">
                        <svg-icon v-if="scope.row.status == 3 && scope.row.rssi >= '-55'" icon-class="wifi_4" />
                        <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-70' && scope.row.rssi < '-55'" icon-class="wifi_3" />
                        <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-85' && scope.row.rssi < '-70'" icon-class="wifi_2" />
                        <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-100' && scope.row.rssi < '-85'" icon-class="wifi_1" />
                        <svg-icon v-else icon-class="wifi_0" />
                    </template>
                </el-table-column>

                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                    <template slot-scope="scope">
                        <el-button type="danger" size="small" style="padding: 5px" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['iot:device:remove']">删除</el-button>
                        <el-button type="success" size="mini" style="padding: 5px 15px" icon="el-icon-odometer" @click="handleRunDevice(scope.row)" v-hasPermi="['iot:device:query']">运行状态</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" :pageSizes="[12, 24, 36, 60]" @pagination="getList" />
        </el-card>

        <!-- 卡片视图美化 -->
        <el-card style="padding-bottom: 100px" v-if="showType == 'card'">
            <el-row :gutter="20" v-loading="loading">
                <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8" v-for="(item, index) in deviceList" :key="index" style="margin-bottom: 20px">
                    <el-card :body-style="{ padding: '15px' }" shadow="hover" class="card-item grid-card">
                        <div class="card-content">
                            <!-- 头部区域 -->
                            <div class="card-header">
                                <el-checkbox :value="selectedIds.includes(item.deviceId)" @change="(val) => handleCardSelection(val, item.deviceId)"></el-checkbox>
                                <el-link type="" :underline="false" @click="handleDeviceDetail(item)" class="device-name">
                                    <el-tooltip class="item" effect="dark" content="分享的设备" placement="top-start">
                                        <svg-icon icon-class="share" class="device-icon" v-if="item.isOwner != 1" />
                                    </el-tooltip>
                                    <svg-icon icon-class="device" class="device-icon" v-if="item.isOwner == 1" />
                                    <span class="device-title">{{ item.deviceName }}</span>
                                </el-link>
                                <svg-icon icon-class="qrcode" @click="openSummaryDialog(item)" class="qrcode-icon" v-hasPermi="['iot:device:query']" />
                            </div>

                            <!-- 分割线 -->
                            <el-divider class="card-divider"></el-divider>

                            <!-- 状态区域 -->
                            <div class="status-section">
                                <div class="status-tags">
                                    <div v-if="item.status === 1" style="display: inline-block">
                                        <el-tag size="mini" type="info">未激活</el-tag>
                                    </div>
                                    <div v-else-if="item.status === 2" style="display: inline-block">
                                        <el-tag size="mini" type="warning">禁用</el-tag>
                                    </div>
                                    <div v-else-if="item.status === 3" style="display: inline-block">
                                        <el-tag size="mini" type="success">在线</el-tag>
                                    </div>
                                    <div v-else-if="item.status === 4" style="display: inline-block">
                                        <el-tag size="mini" type="danger">离线</el-tag>
                                    </div>
                                    <span class="protocol-tag">
                                        <el-tag type="primary" size="mini" v-if="item.protocolCode">{{ item.protocolCode }}</el-tag>
                                    </span>
                                    <el-tag type="primary" size="mini" v-if="item.transport">{{ item.transport }}</el-tag>
                                </div>

                                <!-- 将序列号移到状态区域并居中显示 -->
                                <div class="serial-number">
                                    {{ item.serialNumber }}
                                </div>

                                <div class="wifi-icon">
                                    <svg-icon v-if="item.status == 3 && item.rssi >= '-55'" icon-class="wifi_4" />
                                    <svg-icon v-else-if="item.status == 3 && item.rssi >= '-70' && item.rssi < '-55'" icon-class="wifi_3" />
                                    <svg-icon v-else-if="item.status == 3 && item.rssi >= '-85' && item.rssi < '-70'" icon-class="wifi_2" />
                                    <svg-icon v-else-if="item.status == 3 && item.rssi >= '-100' && item.rssi < '-85'" icon-class="wifi_1" />
                                    <svg-icon v-else icon-class="wifi_0" />
                                </div>
                            </div>

                            <!-- 分割线 -->
                            <el-divider class="info-divider"></el-divider>

                            <!-- 信息区域 - 修改为3×3九宫格 -->
                            <!-- <div class="info-section">
                                <div class="info-grid grid-3x3">

                                    <div v-for="(info, idx) in deviceInfoItems(item)" :key="idx" class="info-item grid-item">
                                        <span class="info-label">{{ info.label }}：</span>
                                        <span class="info-value">{{ info.value }}</span>
                                    </div>
                                </div> -->

                            <!-- 信息区域 - 修改为3×3九宫格 -->
                            <div class="info-section">
                                <div class="info-grid grid-3x3">
                                    <!-- 使用循环渲染信息项 -->
                                    <div v-for="(info, idx) in deviceInfoItems(item).slice(0, 9)" :key="idx" class="info-item grid-item highlight-item">
                                        <span class="info-label">{{ info.label }}</span>
                                        <span class="info-value">{{ info.value }}</span>
                                    </div>
                                </div>

                                <!-- 单独显示更新时间 -->
                                <div class="update-time">
                                    <span class="info-label">更新时间：</span>
                                    <span class="info-value">{{ item.lastTime || '--' }}</span>
                                </div>
                            </div>

                            <!-- 图片区域 - 缩小并居中 -->
                            <!-- <div class="image-section small-image">
                                    <el-image
                                        style="width: 100%; height: 80px; margin-top: 50%; border-radius: 8px; object-fit: contain"
                                        lazy
                                        :preview-src-list="[baseUrl + item.imgUrl]"
                                        :src="baseUrl + item.imgUrl"
                                        fit="contain"
                                        v-if="item.imgUrl != null && item.imgUrl != ''"
                                    ></el-image>
                                    <el-image
                                        style="width: 100%; height: 80px; margin-top: 50%; border-radius: 8px; object-fit: contain"
                                        :preview-src-list="[require('@/assets/images/gateway.png')]"
                                        :src="require('@/assets/images/gateway.png')"
                                        fit="contain"
                                        v-else-if="item.deviceType == 2"
                                    ></el-image>
                                    <el-image
                                        style="width: 100%; height: 80px; margin-top: 50%; border-radius: 8px; object-fit: contain"
                                        :preview-src-list="[require('@/assets/images/video.png')]"
                                        :src="require('@/assets/images/video.png')"
                                        fit="contain"
                                        v-else-if="item.deviceType == 3"
                                    ></el-image>
                                    <el-image
                                        style="width: 100%; height: 80px; margin-top: 50%; border-radius: 8px; object-fit: contain"
                                        :preview-src-list="[require('@/assets/images/product.png')]"
                                        :src="require('@/assets/images/product.png')"
                                        fit="contain"
                                        v-else
                                    ></el-image>
                                </div> -->
                            <!-- </div> -->

                            <!-- 分割线 -->
                            <el-divider class="footer-divider"></el-divider>

                            <!-- 底部操作按钮 -->
                            <div class="card-footer">
                                <el-button-group>
                                    <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDelete(item)" v-hasPermi="['iot:device:remove']">删除</el-button>
                                    <el-button type="primary" size="mini" icon="el-icon-view" @click="handleEditDevice(item, 'basic')" v-hasPermi="['iot:device:query']">查看</el-button>
                                    <el-button type="success" size="mini" icon="el-icon-odometer" @click="handleRunDevice(item)" v-hasPermi="['iot:device:query']">运行状态</el-button>
                                </el-button-group>
                            </div>
                        </div>
                    </el-card>
                </el-col>
            </el-row>
            <el-empty description="暂无数据，请添加设备" v-if="total == 0"></el-empty>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" :pageSizes="[12, 24, 36, 60]" @pagination="getList" />
        </el-card>

        <!-- 二维码 -->
        <el-dialog :close-on-click-modal="false" :visible.sync="openSummary" width="300px" append-to-body>
            <div style="border: 1px solid #ccc; width: 220px; text-align: center; margin: 0 auto; margin-top: -15px">
                <vue-qr :text="qrText" :size="200"></vue-qr>
                <div style="padding-bottom: 10px">设备二维码</div>
            </div>
        </el-dialog>
        <!-- 导入分配 -->
        <allotImport ref="allotImport" @save="saveAllotDialog"></allotImport>
    </div>
</template>

<script>
import vueQr from 'vue-qr';
import { listDeviceShort, delDevice, listTestDeviceShort } from '@/api/iot/device'; // 导入新的API方法
import { listGroup } from '@/api/iot/group';
import { delSipDeviceBySipId } from '@/api/iot/sipdevice';
import auth from '@/plugins/auth';
import ToolForm from './components/toolForm';
import Pagination from '@/components/Pagination';
import allotImport from '../../iot/device/allot-import-dialog';

export default {
    name: 'Device',
    dicts: ['iot_device_status', 'iot_is_enable', 'iot_location_way', 'iot_transport_type'],
    components: {
        vueQr,
        ToolForm,
        Pagination,
        allotImport,
    },
    data() {
        return {
            // 新增状态保存字段
            preservedState: null,
            // 二维码内容
            qrText: 'seefy',
            // 打开设备配置对话框
            openSummary: false,
            // 显示搜索条件
            showSearch: true,
            // 展示方式
            showType: 'list',
            // 遮罩层
            loading: true,
            // 总条数
            total: 0,
            // 设备列表数据
            deviceList: [],
            // 我的分组列表数据
            myGroupList: [],
            // 根路径
            baseUrl: process.env.VUE_APP_BASE_API,
            // 定时刷新定时器
            timer: null,
            // 选中的设备ID数组
            selectedIds: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 12,
                showChild: true,
                deviceName: null,
                productId: null,
                groupId: null,
                productName: null,
                userId: null,
                userName: null,
                tenantId: null,
                tenantName: null,
                serialNumber: null,
                status: null,
                networkAddress: null,
                activeTime: null,
            },
            dict: {
                type: {
                    iot_device_status: [],
                    iot_is_enable: [],
                    iot_location_way: [],
                    iot_transport_type: [],
                },
            },
        };
    },
    // 新增：离开页面时保存状态
    beforeRouteLeave(to, from, next) {
        const state = {
            queryParams: this.queryParams,
            deviceList: this.deviceList,
            total: this.total,
            showType: this.showType,
            selectedIds: this.selectedIds,
            pageNum: this.queryParams.pageNum,
            pageSize: this.queryParams.pageSize,
        };
        sessionStorage.setItem('deviceListState', JSON.stringify(state));
        next();
    },
    created() {
        // 产品筛选
        let productId = this.$route.query.productId;
        if (productId != null) {
            this.queryParams.productId = Number(productId);
            this.queryParams.groupId = null;
            this.queryParams.serialNumber = null;
        }
        // 分组筛选
        let groupId = this.$route.query.groupId;
        if (groupId != null) {
            this.queryParams.groupId = Number(groupId);
            this.queryParams.productId = null;
            this.queryParams.serialNumber = null;
        }
        // 设备编号筛选
        let sn = this.$route.query.sn;
        if (sn != null) {
            this.queryParams.serialNumber = sn;
            this.queryParams.productId = null;
            this.queryParams.groupId = null;
        }
        this.getGroupList();
        this.connectMqtt();
        // 添加可见性变化监听
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
    },
    mounted() {
        // 启动定时刷新
        this.startRefreshTimer();
    },
    beforeDestroy() {
        // 清理定时器和事件监听
        this.stopRefreshTimer();
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    },
    activated() {
        const savedState = sessionStorage.getItem('deviceListState');
        if (savedState) {
            const state = JSON.parse(savedState);
            this.queryParams = { ...state.queryParams };
            this.deviceList = state.deviceList;
            this.total = state.total;
            this.showType = state.showType;
            this.selectedIds = state.selectedIds;
            this.queryParams.pageNum = state.pageNum;
            this.queryParams.pageSize = state.pageSize;

            // 清除保存的状态避免重复使用
            sessionStorage.removeItem('deviceListState');

            // 重启定时刷新
            this.startRefreshTimer();
        } else if (time != null && time != this.uniqueId) {
            // ...原有参数处理逻辑保持不变...
            if (time != null && time != this.uniqueId) {
                this.uniqueId = time;
                // 页码筛选
                let pageNum = this.$route.query.pageNum;
                if (pageNum != null) {
                    this.queryParams.pageNum = Number(pageNum);
                }
                // 产品筛选
                let productId = this.$route.query.productId;
                if (productId != null) {
                    this.queryParams.productId = Number(productId);
                    this.queryParams.groupId = null;
                    this.queryParams.serialNumber = null;
                }
                // 分组筛选
                let groupId = this.$route.query.groupId;
                if (groupId != null) {
                    this.queryParams.groupId = Number(groupId);
                    this.queryParams.productId = null;
                    this.queryParams.serialNumber = null;
                }
                // 设备编号筛选
                let sn = this.$route.query.sn;
                if (sn != null) {
                    this.queryParams.serialNumber = sn;
                    this.queryParams.productId = null;
                    this.queryParams.groupId = null;
                }
            }

            this.getList();
            // 重启定时刷新
            this.startRefreshTimer();
        }
    },
    deactivated() {
        // 当组件被缓存时停止定时器
        this.stopRefreshTimer();
    },
    methods: {
        /* 启动定时刷新 */
        startRefreshTimer() {
            // 先清除现有定时器
            this.stopRefreshTimer();
            // 创建新定时器，每5秒刷新一次
            this.timer = setInterval(() => {
                // 只有在页面可见时才执行刷新
                if (!document.hidden) {
                    this.refreshList();
                }
            }, 5000);
        },
        /* 停止定时刷新 */
        stopRefreshTimer() {
            if (this.timer) {
                clearInterval(this.timer);
                this.timer = null;
            }
        },
        /* 处理页面可见性变化 */
        handleVisibilityChange() {
            if (document.hidden) {
                // 页面不可见时停止定时器
                this.stopRefreshTimer();
            } else {
                // 页面可见时重启定时器
                this.startRefreshTimer();
                // 立即刷新一次数据
                this.refreshList();
            }
        },
        /* 刷新列表数据 */
        refreshList() {
            // 不显示加载动画，静默刷新
            this.queryParams.params = {};
            // 使用新的测试设备接口
            listTestDeviceShort(this.queryParams).then((response) => {
                // 对比数据是否有变化
                const newData = response.rows;
                const oldData = this.deviceList;
                let hasChanged = false;

                // 简单对比数据长度或关键状态
                if (newData.length !== oldData.length) {
                    hasChanged = true;
                } else {
                    // 更详细的比较，检查每个设备的关键属性
                    for (let i = 0; i < newData.length; i++) {
                        if (
                            newData[i].status !== oldData[i].status ||
                            newData[i].rssi !== oldData[i].rssi ||
                            newData[i].batteryVoltage !== oldData[i].batteryVoltage ||
                            newData[i].waterState !== oldData[i].waterState ||
                            newData[i].ch4Val !== oldData[i].ch4Val ||
                            newData[i].errorCode !== oldData[i].errorCode ||
                            newData[i].lastTime !== oldData[i].lastTime
                        ) {
                            hasChanged = true;
                            break;
                        }
                    }
                }

                // 只有数据发生变化时才更新DOM
                if (hasChanged) {
                    this.deviceList = newData;
                    this.total = response.total;
                    // 订阅消息
                    if (this.deviceList && this.deviceList.length > 0) {
                        this.mqttSubscribe(this.deviceList);
                    }
                }
            });
        },
        /* 连接Mqtt消息服务器 */
        async connectMqtt() {
            if (this.$mqttTool.client == null) {
                await this.$mqttTool.connect();
            }
            this.mqttCallback();
            const conditions = JSON.parse(localStorage.getItem('queryConditions1'));
            const results = JSON.parse(localStorage.getItem('queryResults1'));
            if (conditions) {
                this.queryParams = conditions; // 假设有一个方法用于根据条件获取数据，如fetchData(conditions) {}。确保该方法存在并正确实现。
                if (results) {
                    // 如果需要恢复结果到UI，可以在这里操作。例如更新列表等。
                    this.getList(); // 如果没有保存的查询条件，则执行默认的查询。
                }
            } else {
                this.getList(); // 如果没有保存的查询条件，则执行默认的查询。
            }
        },
        /* Mqtt回调处理  */
        mqttCallback() {
            this.$mqttTool.client.on('message', (topic, message, buffer) => {
                let topics = topic.split('/');
                let productId = topics[1];
                let deviceNum = topics[2];
                message = JSON.parse(message.toString());
                if (!message) {
                    return;
                }
                if (topics[3] == 'status') {
                    console.log('接收到【设备状态】主题：', topic);
                    console.log('接收到【设备状态】内容：', message);
                    // 更新列表中设备的状态
                    for (let i = 0; i < this.deviceList.length; i++) {
                        if (this.deviceList[i].serialNumber == deviceNum) {
                            this.deviceList[i].status = message.status;
                            this.deviceList[i].isShadow = message.isShadow;
                            this.deviceList[i].rssi = message.rssi;
                            // 更新测试设备特有的字段
                            if (message.batteryVoltage) {
                                this.deviceList[i].batteryVoltage = message.batteryVoltage;
                            }
                            if (message.batteryState) {
                                this.deviceList[i].batteryState = message.batteryState;
                            }
                            if (message.waterState) {
                                this.deviceList[i].waterState = message.waterState;
                            }
                            if (message.ch4Val) {
                                this.deviceList[i].ch4Val = message.ch4Val;
                            }
                            if (message.errorCode) {
                                this.deviceList[i].errorCode = message.errorCode;
                            }
                            if (message.errorStatus) {
                                this.deviceList[i].errorStatus = message.errorStatus;
                            }
                            if (message.lonAndLat) {
                                this.deviceList[i].lonAndLat = message.lonAndLat;
                            }
                            if (message.lastTime) {
                                this.deviceList[i].lastTime = message.lastTime;
                            }
                            return;
                        }
                    }
                }
            });
        },
        // 新增设备操作触发
        handleCommand(command) {
            switch (command) {
                case 'handleSelectAllot':
                    this.handleSelectAllot();
                    break;
                case 'handleImportAllot':
                    this.handleImportAllot();
                    break;
                default:
                    break;
            }
        },
        //跳转选择分配设备页面
        handleSelectAllot() {
            this.$router.push({
                path: '/iot/device-select-allot',
            });
        },
        saveAllotDialog() {
            this.getList();
        },
        //导入分配设备
        handleImportAllot() {
            this.$refs.allotImport.upload.importAllotDialog = true;
            this.$refs.allotImport.allotForm.productId = null;
            this.$refs.allotImport.allotForm.deptId = null;
        },
        openSummaryDialog(row) {
            let json = {
                type: 1, // 1=扫码关联设备
                deviceNumber: row.serialNumber,
                productId: row.productId,
                productName: row.productName,
            };
            this.qrText = JSON.stringify(json);
            this.openSummary = true;
        },
        /* 订阅消息 */
        mqttSubscribe(list) {
            // 订阅当前页面设备状态和实时监测
            let topics = [];
            for (let i = 0; i < list.length; i++) {
                let topicStatus = '/' + '+' + '/' + list[i].serialNumber + '/status/post';
                topics.push(topicStatus);
            }
            this.$mqttTool.subscribe(topics);
        },
        /** 查询设备分组列表 */
        getGroupList() {
            this.loading = true;
            let queryParams = {
                pageSize: 30,
                pageNum: 1,
                userId: this.$store.state.user.userId,
            };
            listGroup(queryParams).then((response) => {
                this.myGroupList = response.rows;
            });
        },
        /** 查询所有简短设备列表 */
        getList() {
            this.loading = true;
            this.queryParams.params = {};
            // 使用新的测试设备接口
            listTestDeviceShort(this.queryParams).then((response) => {
                this.deviceList = response.rows;
                this.total = response.total;
                // 订阅消息
                if (this.deviceList && this.deviceList.length > 0) {
                    this.mqttSubscribe(this.deviceList);
                }
                this.saveQueryConditions(this.queryParams);
                this.saveQueryResults(this.deviceList);
                this.loading = false;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
            // 条件变更后重新启动定时器
            this.startRefreshTimer();
        },
        saveQueryConditions(conditions) {
            localStorage.setItem('queryConditions1', JSON.stringify(conditions));
        },
        saveQueryResults(results) {
            localStorage.setItem('queryResults1', JSON.stringify(results));
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.queryParams.productId = null;
            this.queryParams.groupId = null;
            this.queryParams.serialNumber = null;
            this.resetForm('queryForm');
            this.handleQuery();
            // 条件变更后重新启动定时器
            this.startRefreshTimer();
        },
        /** 切换显示方式 */
        handleChangeShowType() {
            this.showType = this.showType == 'card' ? 'list' : 'card';
        },
        // 点击名称查看
        handleDeviceDetail(item) {
            if (auth.hasPermi('iot:device:query')) {
                this.handleEditDevice(item);
            }
        },
        /** 修改按钮操作 */
        handleEditDevice(row, activeName) {
            let deviceId = 0;
            let isSubDev = 0;
            if (row != 0) {
                deviceId = row.deviceId || this.ids;
                isSubDev = row.subDeviceCount > 0 ? 1 : 0;
            }
            this.$router.push({
                path: '/test/device-edit',
                query: {
                    deviceId: deviceId,
                    isSubDev: isSubDev,
                    pageNum: this.queryParams.pageNum,
                    activeName: activeName,
                },
            });
        },
        /** 运行状态按钮操作 */
        handleRunDevice(row) {
            let deviceId = 0;
            let isSubDev = 0;
            if (row != 0) {
                deviceId = row.deviceId || this.ids;
                isSubDev = row.subDeviceCount > 0 ? 1 : 0;
            }
            const activeName = row.deviceType === 3 ? 'sipChannel' : 'runningStatus';
            this.$router.push({
                path: '/test/device-edit',
                query: {
                    deviceId,
                    isSubDev,
                    pageNum: this.queryParams.pageNum,
                    activeName,
                },
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const deviceIds = row.deviceId || this.ids;
            this.$modal
                .confirm('是否确认删除设备编号为"' + deviceIds + '"的数据项？')
                .then(function () {
                    if (row.deviceType === 3) {
                        delSipDeviceBySipId(row.serialNumber);
                    }
                    return delDevice(deviceIds);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 表格多选框选中数据 */
        handleSelectionChange(selection) {
            this.selectedIds = selection.map((item) => item.deviceId);
        },
        /** 卡片视图选择设备 */
        handleCardSelection(val, deviceId) {
            if (val) {
                // 如果选中，将deviceId添加到selectedIds数组中
                if (!this.selectedIds.includes(deviceId)) {
                    this.selectedIds.push(deviceId);
                }
            } else {
                // 如果取消选中，从selectedIds数组中移除deviceId
                this.selectedIds = this.selectedIds.filter((id) => id !== deviceId);
            }
        },
        /** 批量删除按钮操作 */
        handleBatchDelete() {
            if (this.selectedIds.length === 0) {
                this.$modal.msgError('请至少选择一条记录');
                return;
            }

            const deviceIds = this.selectedIds.join(',');
            this.$modal
                .confirm('是否确认批量删除选中的' + this.selectedIds.length + '条数据？')
                .then(function () {
                    return delDevice(deviceIds);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('批量删除成功');
                    this.selectedIds = [];
                })
                .catch(() => {});
        },
        handleExport() {
            this.download(
                '/iot/device/testExport',
                {
                    ...this.queryParams,
                },
                `device_${new Date().getTime()}.xlsx`
            );
        },

        // 新增方法：组织设备信息项
        // 新增方法：组织设备信息项（3×3九宫格）
        deviceInfoItems(item) {
            return [
                { label: '电池电压', value: item.batteryVoltage },
                {
                    label: '电池低电量报警',
                    value: item.batteryState,
                },
                {
                    label: '浸水报警',
                    value: item.waterState,
                },
                { label: '甲烷浓度', value: item.ch4Val },
                { label: '故障类型', value: item.errorCode || '--' },
                {
                    label: '故障报警状态',
                    value: item.errorStatus,
                },
                { label: '经纬度', value: item.lonAndLat || '无' },
                { label: '温度', value: item.temperature }, // 新增温度字段
                { label: '信号值', value: `${item.signal || '--'}` }, // 新增温度字段
                { label: '更新时间', value: item.lastTime || '--' }, // 新增更新时间字段
            ];
        },
    },
};
</script>

<!-- <style scoped>
.card-item {
    border-radius: 12px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card-item:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.card-header {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
}

.device-name {
    flex: 1;
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.device-icon {
    font-size: 18px;
    margin-right: 5px;
}

.device-title {
    font-weight: bold;
    font-size: 16px;
    color: #333;
}

.qrcode-icon {
    font-size: 20px;
    color: #999;
    cursor: pointer;
}

.qrcode-icon:hover {
    color: #409EFF;
}

.card-body {
    flex: 1;
}

.status-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.status-tags {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.protocol-tag {
    margin: 0 5px;
}

.wifi-icon {
    font-size: 24px;
    color: #999;
}

.info-section {
    display: flex;
    gap: 15px;
}

.info-grid {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 12px;
    color: #999;
    margin-bottom: 2px;
}

.info-value {
    font-size: 13px;
    color: #333;
}

.image-section {
    width: 35%;
}

.card-footer {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #eee;
    text-align: center;
}

::v-deep .el-dropdown-menu__item {
    font-size: 12px;
}

::v-deep .el-upload-dragger {
    width: 510px;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-section {
        flex-direction: column;
    }

    .image-section {
        width: 100%;
    }
}
</style> -->

<!-- <style scoped>
/* 卡片整体样式 */
.grid-card {
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;
    transition: all 0.3s ease;
    background: linear-gradient(to bottom, #ffffff, #f9fafc);
}

.grid-card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    border-color: #dcdfe6;
}

.card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 头部区域 */
.card-header {
    display: flex;
    align-items: center;
    padding: 0 0 10px 0;
}

.device-name {
    flex: 1;
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-weight: 600;
}

.device-icon {
    font-size: 18px;
    margin-right: 8px;
    color: #409eff;
}

.device-title {
    font-size: 16px;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.qrcode-icon {
    font-size: 20px;
    color: #909399;
    cursor: pointer;
    transition: all 0.2s;
}

.qrcode-icon:hover {
    color: #409eff;
    transform: scale(1.1);
}

/* 分割线样式 */
.card-divider,
.info-divider,
.footer-divider {
    margin: 8px 0;
}

.card-divider {
    background-color: #ebeef5;
}

.info-divider {
    background-color: #f0f2f5;
}

.footer-divider {
    background-color: #ebeef5;
}

/* 状态区域 */
.status-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0px;
}

.status-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.protocol-tag {
    margin: 0 5px;
}

.wifi-icon {
    font-size: 24px;
    color: #67c23a;
}

/* 信息区域 */
.info-section {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.info-grid {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #ebeef5;
}

.grid-item {
    display: flex;
    flex-direction: column;
    padding: 8px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f2f5;
    transition: all 0.2s;
}

.grid-item:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    border-color: #dcdfe6;
}

.info-label {
    font-size: 14px;
    color: #909399;
    margin-bottom: 4px;
    font-weight: 500;
}

.info-value {
    font-size: 15px;
    color: #303133;
    font-weight: 600;
}

/* 图片区域 */
.image-section {
    width: 35%;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 底部按钮 */
.card-footer {
    margin-top: 10px;
    padding-top: 10px;
    text-align: center;
}

.el-button-group {
    display: flex;
    justify-content: center;
    gap: 8px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .info-section {
        flex-direction: column;
    }

    .image-section {
        width: 100%;
        margin-top: 15px;
    }
}

@media (max-width: 768px) {
    .card-header {
        flex-wrap: wrap;
    }

    .qrcode-icon {
        margin-left: auto;
    }

    .device-title {
        max-width: 150px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.el-col {
    animation: fadeIn 0.3s ease forwards;
}

.el-col:nth-child(1) {
    animation-delay: 0.05s;
}
.el-col:nth-child(2) {
    animation-delay: 0.1s;
}
.el-col:nth-child(3) {
    animation-delay: 0.15s;
}
.el-col:nth-child(4) {
    animation-delay: 0.2s;
}
.el-col:nth-child(5) {
    animation-delay: 0.25s;
}
.el-col:nth-child(6) {
    animation-delay: 0.3s;
}
</style> -->

<style scoped>
/* 卡片整体样式 */
.grid-card {
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;
    transition: all 0.3s ease;
    background: linear-gradient(to bottom, #ffffff, #f9fafc);
}

.grid-card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
    border-color: #dcdfe6;
}

.card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 头部区域 */
.card-header {
    display: flex;
    align-items: center;
    padding: 0 0 10px 0;
}

.device-name {
    flex: 1;
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-weight: 600;
}

.device-icon {
    font-size: 18px;
    margin-right: 8px;
    color: #409eff;
}

.device-title {
    font-size: 16px;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.qrcode-icon {
    font-size: 20px;
    color: #909399;
    cursor: pointer;
    transition: all 0.2s;
}

.qrcode-icon:hover {
    color: #409eff;
    transform: scale(1.1);
}

/* 分割线样式 */
.card-divider,
.info-divider,
.footer-divider {
    margin: 8px 0;
}

.card-divider {
    background-color: #ebeef5;
}

.info-divider {
    background-color: #f0f2f5;
}

.footer-divider {
    background-color: #ebeef5;
}

/* 状态区域 */
.status-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0px;
}

.status-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.protocol-tag {
    margin: 0 5px;
}

.wifi-icon {
    font-size: 24px;
    color: #67c23a;
}

/* 3x3网格布局 */
.grid-3x3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 15px;
}

/* 突出显示的信息块 */
.highlight-item {
    background: linear-gradient(to bottom, #ffffff, #f8fafc);
    border: 1px solid #e6ebf5;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: center;
}

.highlight-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    border-color: #c0d3ff;
    background: linear-gradient(to bottom, #ffffff, #f0f7ff);
}

/* 信息标签样式 */
.info-label {
    display: block;
    font-size: 18px;
    color: #606266;
    margin-bottom: 5px;
    font-weight: 500;
}

/* 信息值样式 - 更突出 */
.info-value {
    display: block;
    font-size: 21px;
    color: #303133;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 特殊值高亮 */
.info-value:not([title='--']) {
    color: #409eff;
}

/* 更新时间区域 */
.update-time {
    background: #f8fafc;
    border-radius: 8px;
    padding: 10px 15px;
    text-align: center;
    border: 1px dashed #e4e7ed;
}

.update-time .info-label {
    display: inline;
    font-size: 18px;
    margin-right: 5px;
}

.update-time .info-value {
    display: inline;
    font-size: 20px;
    font-weight: bold;
    color: #e6a23c;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .grid-3x3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .grid-3x3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .grid-3x3 {
        grid-template-columns: 1fr;
    }
}

/* 底部按钮 */
.card-footer {
    margin-top: 10px;
    padding-top: 1rpx;
    text-align: center;
}

.el-button-group {
    display: flex;
    justify-content: center;
    gap: 8px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .info-section {
        flex-direction: column;
    }

    .image-section {
        width: 100%;
        margin-top: 15px;
    }
}

@media (max-width: 768px) {
    .card-header {
        flex-wrap: wrap;
    }

    .qrcode-icon {
        margin-left: auto;
    }

    .device-title {
        max-width: 150px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.el-col {
    animation: fadeIn 0.3s ease forwards;
}

.el-col:nth-child(1) {
    animation-delay: 0.05s;
}
.el-col:nth-child(2) {
    animation-delay: 0.1s;
}
.el-col:nth-child(3) {
    animation-delay: 0.15s;
}
.el-col:nth-child(4) {
    animation-delay: 0.2s;
}
.el-col:nth-child(5) {
    animation-delay: 0.25s;
}
.el-col:nth-child(6) {
    animation-delay: 0.3s;
}
</style>
