import LocationErrorHandler from './LocationErrorHandler.js';

/**
 * IP-based location detection provider
 * Provides fallback location detection using multiple IP geolocation APIs
 */
class IPLocationProvider {
    constructor(options = {}) {
        this.timeout = options.timeout || 10000;
        this.apiKey = options.apiKey || '';
        this.errorHandler = new LocationErrorHandler(options.errorHandling || { logLevel: 'info' });

        // Use provided endpoints or default ones
        this.apiEndpoints = this.buildApiEndpoints(options.endpoints);
    }

    /**
     * Build API endpoints configuration
     * @param {Array} customEndpoints - Custom endpoint configurations
     * @returns {Array} Configured API endpoints
     */
    buildApiEndpoints(customEndpoints) {
        const defaultEndpoints = [
            {
                url: 'https://ipapi.co/json/',
                parser: this.parseIpapiResponse.bind(this),
                requiresKey: false,
                priority: 1,
            },
            {
                url: 'https://ip-api.com/json/',
                parser: this.parseIpApiResponse.bind(this),
                requiresKey: false,
                priority: 2,
            },
            {
                url: 'https://ipinfo.io/json',
                parser: this.parseIpinfoResponse.bind(this),
                requiresKey: true,
                priority: 3,
            },
        ];

        if (customEndpoints && Array.isArray(customEndpoints)) {
            return customEndpoints
                .map((endpoint) => ({
                    ...endpoint,
                    parser: this.getParserForEndpoint(endpoint.url),
                }))
                .sort((a, b) => (a.priority || 999) - (b.priority || 999));
        }

        // Filter out endpoints that require API key if no key is provided
        return defaultEndpoints
            .filter((endpoint) => {
                if (endpoint.requiresKey && !this.apiKey) {
                    this.errorHandler.log('debug', `Skipping ${endpoint.url} - requires API key`);
                    return false;
                }
                return true;
            })
            .sort((a, b) => a.priority - b.priority);
    }

    /**
     * Get appropriate parser for endpoint URL
     * @param {string} url - Endpoint URL
     * @returns {Function} Parser function
     */
    getParserForEndpoint(url) {
        if (url.includes('ipapi.co')) {
            return this.parseIpapiResponse.bind(this);
        } else if (url.includes('ip-api.com')) {
            return this.parseIpApiResponse.bind(this);
        } else if (url.includes('ipinfo.io')) {
            return this.parseIpinfoResponse.bind(this);
        } else {
            // Generic parser for unknown endpoints
            return this.parseGenericResponse.bind(this);
        }
    }

    /**
     * Get user location based on IP address
     * Tries multiple API endpoints with fallback logic
     * @returns {Promise<Object>} Location data object
     */
    async getLocationByIP() {
        const errors = [];

        for (const endpoint of this.apiEndpoints) {
            try {
                this.errorHandler.log('debug', `Attempting IP location detection`, {
                    endpoint: endpoint.url,
                });

                const locationData = await this.fetchFromEndpoint(endpoint);

                if (this.isValidLocationData(locationData)) {
                    this.errorHandler.log('info', 'IP location detection successful', {
                        provider: locationData.provider,
                        city: locationData.city,
                        country: locationData.country,
                    });
                    return locationData;
                } else {
                    this.errorHandler.log('warn', 'Invalid location data received', {
                        endpoint: endpoint.url,
                        data: locationData,
                    });
                }
            } catch (error) {
                const errorResult = this.errorHandler.handle(error, 'ip-api');
                this.errorHandler.log('warn', `IP location API failed`, {
                    endpoint: endpoint.url,
                    error: errorResult.error.message,
                    type: errorResult.error.type,
                });

                errors.push({
                    endpoint: endpoint.url,
                    error: error.message,
                    type: error.type || 'UNKNOWN_ERROR',
                });
            }
        }

        // All endpoints failed
        const aggregateError = new Error(`All IP location services failed. Tried ${this.apiEndpoints.length} endpoints.`);
        aggregateError.type = 'NETWORK_ERROR';
        aggregateError.details = errors;

        const errorResult = this.errorHandler.handle(aggregateError, 'ip-fallback');
        this.errorHandler.log('error', 'Complete IP location detection failure', {
            totalEndpoints: this.apiEndpoints.length,
            errors: errors,
        });

        throw aggregateError;
    }

    /**
     * Fetch location data from a specific endpoint
     * @param {Object} endpoint - API endpoint configuration
     * @returns {Promise<Object>} Parsed location data
     */
    async fetchFromEndpoint(endpoint) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), endpoint.timeout || this.timeout);

        try {
            this.errorHandler.log('debug', 'Starting IP location API request', {
                url: endpoint.url,
                timeout: endpoint.timeout || this.timeout,
            });

            // Build URL with API key if required
            const requestUrl = this.buildRequestUrl(endpoint);

            const response = await fetch(requestUrl, {
                method: 'GET',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                    ...this.getAuthHeaders(endpoint),
                },
                signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const httpError = new Error(`HTTP ${response.status}: ${response.statusText}`);
                httpError.type = 'NETWORK_ERROR';
                httpError.status = response.status;
                throw httpError;
            }

            const data = await response.json();
            const parsedData = endpoint.parser(data);

            this.errorHandler.log('debug', 'IP location API response parsed successfully', {
                url: endpoint.url,
                city: parsedData.city,
            });

            return parsedData;
        } catch (error) {
            clearTimeout(timeoutId);

            if (error.name === 'AbortError') {
                const timeoutError = new Error(`Request timeout after ${this.timeout}ms`);
                timeoutError.type = 'TIMEOUT';
                this.errorHandler.log('warn', 'IP location request timed out', {
                    url: endpoint.url,
                    timeout: this.timeout,
                });
                throw timeoutError;
            }

            // Log the specific error before re-throwing
            this.errorHandler.log('warn', 'IP location API request failed', {
                url: endpoint.url,
                error: error.message,
                type: error.type || 'NETWORK_ERROR',
            });

            const networkError = new Error(`Network error: ${error.message}`);
            networkError.type = error.type || 'NETWORK_ERROR';
            networkError.originalError = error;
            throw networkError;
        }
    }

    /**
     * Parse response from ipapi.co
     * @param {Object} data - Raw API response
     * @returns {Object} Standardized location data
     */
    parseIpapiResponse(data) {
        if (data.error) {
            throw new Error(`API Error: ${data.reason || 'Unknown error'}`);
        }

        return {
            latitude: parseFloat(data.latitude),
            longitude: parseFloat(data.longitude),
            city: data.city,
            region: data.region,
            country: data.country_name,
            source: 'ip',
            provider: 'ipapi.co',
            timestamp: Date.now(),
        };
    }

    /**
     * Parse response from ip-api.com
     * @param {Object} data - Raw API response
     * @returns {Object} Standardized location data
     */
    parseIpApiResponse(data) {
        if (data.status === 'fail') {
            throw new Error(`API Error: ${data.message || 'Unknown error'}`);
        }

        return {
            latitude: parseFloat(data.lat),
            longitude: parseFloat(data.lon),
            city: data.city,
            region: data.regionName,
            country: data.country,
            source: 'ip',
            provider: 'ip-api.com',
            timestamp: Date.now(),
        };
    }

    /**
     * Parse response from ipinfo.io
     * @param {Object} data - Raw API response
     * @returns {Object} Standardized location data
     */
    parseIpinfoResponse(data) {
        if (data.error) {
            throw new Error(`API Error: ${data.error.message || 'Unknown error'}`);
        }

        const [latitude, longitude] = (data.loc || '0,0').split(',').map(parseFloat);

        return {
            latitude,
            longitude,
            city: data.city,
            region: data.region,
            country: data.country,
            source: 'ip',
            provider: 'ipinfo.io',
            timestamp: Date.now(),
        };
    }

    /**
     * Validate location data completeness
     * @param {Object} locationData - Location data to validate
     * @returns {boolean} True if data is valid
     */
    isValidLocationData(locationData) {
        if (!locationData) {
            return false;
        }

        return (
            typeof locationData.latitude === 'number' &&
            typeof locationData.longitude === 'number' &&
            !isNaN(locationData.latitude) &&
            !isNaN(locationData.longitude) &&
            locationData.latitude >= -90 &&
            locationData.latitude <= 90 &&
            locationData.longitude >= -180 &&
            locationData.longitude <= 180 &&
            locationData.city !== undefined &&
            locationData.city !== null &&
            typeof locationData.city === 'string' &&
            locationData.city.trim().length > 0
        );
    }

    /**
     * Parse generic response for unknown endpoints
     * @param {Object} data - Raw API response
     * @returns {Object} Standardized location data
     */
    parseGenericResponse(data) {
        // Try to extract common fields from unknown API responses
        const latitude = data.latitude || data.lat || data.location?.latitude;
        const longitude = data.longitude || data.lng || data.lon || data.location?.longitude;
        const city = data.city || data.location?.city;
        const region = data.region || data.state || data.location?.region;
        const country = data.country || data.country_name || data.location?.country;

        if (!latitude || !longitude || !city) {
            throw new Error('Incomplete location data from API');
        }

        return {
            latitude: parseFloat(latitude),
            longitude: parseFloat(longitude),
            city,
            region,
            country,
            source: 'ip',
            provider: 'generic',
            timestamp: Date.now(),
        };
    }

    /**
     * Build request URL with API key if needed
     * @param {Object} endpoint - Endpoint configuration
     * @returns {string} Complete request URL
     */
    buildRequestUrl(endpoint) {
        let url = endpoint.url;

        if (endpoint.requiresKey && this.apiKey) {
            const separator = url.includes('?') ? '&' : '?';
            if (url.includes('ipinfo.io')) {
                url += `${separator}token=${this.apiKey}`;
            } else {
                url += `${separator}key=${this.apiKey}`;
            }
        }

        return url;
    }

    /**
     * Get authentication headers for endpoint
     * @param {Object} endpoint - Endpoint configuration
     * @returns {Object} Headers object
     */
    getAuthHeaders(endpoint) {
        const headers = {};

        if (endpoint.requiresKey && this.apiKey) {
            if (endpoint.authType === 'bearer') {
                headers.Authorization = `Bearer ${this.apiKey}`;
            } else if (endpoint.authType === 'header') {
                headers['X-API-Key'] = this.apiKey;
            }
        }

        return headers;
    }

    /**
     * Get available endpoints count
     * @returns {number} Number of configured endpoints
     */
    getEndpointCount() {
        return this.apiEndpoints.length;
    }

    /**
     * Get endpoint configuration for debugging
     * @returns {Array} Sanitized endpoint configurations
     */
    getEndpointInfo() {
        return this.apiEndpoints.map((endpoint) => ({
            url: endpoint.url,
            requiresKey: endpoint.requiresKey,
            priority: endpoint.priority,
            hasApiKey: endpoint.requiresKey ? !!this.apiKey : 'N/A',
        }));
    }

    /**
     * Update API key
     * @param {string} apiKey - New API key
     */
    updateApiKey(apiKey) {
        this.apiKey = apiKey;
        // Rebuild endpoints to include/exclude key-required endpoints
        this.apiEndpoints = this.buildApiEndpoints();
    }

    /**
     * Check if IP location detection is available
     * @returns {boolean} True if fetch API is available
     */
    static isAvailable() {
        return typeof fetch !== 'undefined';
    }
}

export default IPLocationProvider;
