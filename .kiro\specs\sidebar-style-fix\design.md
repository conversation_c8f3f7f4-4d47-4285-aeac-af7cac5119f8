# 设计文档

## 概述

左侧菜单样式不一致的问题是由于视频模块引入了独立的样式文件导致的。视频模块在 `src/assets/styles/video/` 目录下有自己的样式系统，包括 `sidebar.scss` 和 `variables.scss`，这些样式与主系统的样式产生了冲突，导致部分菜单项显示不一致的背景色。

## 架构

### 当前架构问题

-   **主系统样式**: `src/assets/styles/sidebar.scss` 使用 `$base-menu-background` 等变量
-   **视频模块样式**: `src/assets/styles/video/sidebar.scss` 使用 `$menuBg` 等不同的变量名
-   **样式冲突**: 两套样式系统同时作用于同一个 DOM 结构，导致样式覆盖和不一致

### 目标架构

-   **统一样式系统**: 移除视频模块的独立样式文件，使用主系统的样式规则
-   **保持功能完整**: 确保视频模块的所有功能正常工作
-   **样式一致性**: 所有菜单项使用相同的样式规则和变量

## 组件和接口

### 1. 样式文件结构

**当前结构:**

```
src/assets/styles/
├── index.scss (主系统样式入口)
├── variables.scss (主系统变量)
├── sidebar.scss (主系统侧边栏样式)
└── video/
    ├── index.scss (视频模块样式入口)
    ├── variables.scss (视频模块变量)
    └── sidebar.scss (视频模块侧边栏样式)
```

**目标结构:**

```
src/assets/styles/
├── index.scss (统一样式入口)
├── variables.scss (统一变量)
├── sidebar.scss (统一侧边栏样式)
└── video/
    ├── iconfont.scss (保留图标样式)
    └── element-ui.scss (保留Element UI覆盖样式)
```

### 2. 变量映射

**主系统变量 → 视频模块变量映射:**

-   `$base-menu-background` → `$menuBg`
-   `$base-menu-color` → `$menuText`
-   `$base-menu-color-active` → `$menuActiveText`
-   `$base-sub-menu-background` → `$subMenuBg`
-   `$base-sub-menu-hover` → `$subMenuHover`
-   `$base-sidebar-width` → `$sideBarWidth`

### 3. 侧边栏组件

**组件文件:**

-   `src/layout/components/Sidebar/index.vue` - 主侧边栏容器
-   `src/layout/components/Sidebar/SidebarItem.vue` - 菜单项组件

**样式绑定:**

-   使用主系统的 `variables.scss` 中的变量
-   移除对视频模块样式变量的依赖

## 数据模型

### 样式变量配置

```scss
// 主系统变量 (src/assets/styles/variables.scss)
$base-menu-color: #bfcbd9;
$base-menu-color-active: #f4f4f5;
$base-menu-background: #304156;
$base-menu-light-color: rgba(0, 0, 0, 0.7);
$base-menu-light-background: #ffffff;
$base-sub-menu-background: #1f2d3d;
$base-sub-menu-hover: #001528;
$base-sidebar-width: 200px;
```

### 组件样式绑定

```vue
<!-- Sidebar/index.vue -->
<template>
    <div
        :style="{
            backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground,
        }"
    >
        <el-menu
            :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
            :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
        ></el-menu>
    </div>
</template>
```

## 错误处理

### 样式冲突检测

-   检查是否有多个样式文件定义相同的 CSS 选择器
-   验证 CSS 优先级和特异性规则
-   确保 `!important` 声明的合理使用

### 主题切换兼容性

-   验证深色/浅色主题切换功能
-   确保所有菜单项在主题切换时正确更新样式
-   测试菜单折叠/展开状态下的样式表现

## 测试策略

### 单元测试

-   测试样式变量的正确导入和使用
-   验证组件样式绑定的正确性
-   检查 CSS 类名和选择器的一致性

### 集成测试

-   测试整个菜单系统的样式一致性
-   验证主题切换功能的正确性
-   测试菜单交互（悬停、点击、展开）的样式效果

### 视觉回归测试

-   对比修复前后的菜单外观
-   验证所有菜单项的样式一致性
-   测试不同浏览器下的样式表现

## 实施方法

### 阶段 1: 移除冲突样式

1. 识别并移除视频模块中与主系统冲突的样式文件
2. 更新样式导入路径，统一使用主系统样式
3. 清理未使用的样式变量和规则

### 阶段 2: 统一样式变量

1. 将视频模块的样式变量映射到主系统变量
2. 更新所有引用视频模块变量的地方
3. 验证样式变量的正确应用

### 阶段 3: 验证和测试

1. 测试菜单的所有交互功能
2. 验证主题切换和响应式布局
3. 进行跨浏览器兼容性测试

## 安全考虑

-   确保样式修改不会影响系统的安全功能
-   验证菜单权限控制仍然正常工作
-   检查样式修改不会暴露敏感信息

## 性能影响

-   移除重复的样式文件可以减少 CSS 文件大小
-   统一样式系统可以提高样式加载效率
-   减少样式冲突可以改善渲染性能

## 兼容性考虑

-   确保修改后的样式在所有支持的浏览器中正常显示
-   验证移动端响应式布局的正确性
-   测试不同屏幕分辨率下的菜单显示效果
