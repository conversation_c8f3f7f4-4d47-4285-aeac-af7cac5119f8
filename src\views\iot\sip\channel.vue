<template>
    <div style="padding-left: 20px">
        <div v-if="!editId" style="height: 100%">
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="warning" plain icon="el-icon-refresh" size="mini" @click="getList">刷新</el-button>
                </el-col>
            </el-row>
            <!-- <el-table v-loading="loading" :data="channelList" size="mini">
            <el-table-column label="设备ID" align="center" prop="dataDeviceId" />
            <el-table-column label="通道ID" align="center" prop="createTime" />
            <el-table-column label="快照" min-width="120" align="center">
                <template v-slot:default="scope">
                    <el-image v-if="isVideoChannel(scope.row)" :src="getSnap(scope.row)" :preview-src-list="getBigSnap(scope.row)" :fit="'contain'" style="width: 60px">
                        <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                        </div>
                    </el-image>
                </template>
            </el-table-column>
            <el-table-column label="通道名称" align="center" prop="gbErrCode" />
            <el-table-column label="产品型号" align="center" prop="gbIpAddress" />

            <el-table-column label="状态" align="center" prop="gbLongitude" width="80">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.sip_gen_status" :value="scope.row.status" size="mini" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="small" type="success" icon="el-icon-video-play" style="padding: 5px" :disabled="scope.row.status !== 2" @click="sendDevicePush(scope.row)">查看直播</el-button>
                </template>
            </el-table-column>
        </el-table> -->

            <el-table v-loading="loading" :data="channelList" height="calc(100% - 64px)" style="width: 100%; font-size: 12px" header-row-class-name="table-header">
                <el-table-column prop="gbName" label="名称" min-width="180" />
                <el-table-column prop="gbDeviceId" label="编号" min-width="180" />
                <el-table-column prop="gbManufacturer" label="厂家" min-width="100" />
                <el-table-column label="类型" min-width="100">
                    <template v-slot:default="scope">
                        <div slot="reference" class="name-wrapper">
                            <el-tag size="medium" effect="plain" type="success" :style="$channelTypeList[scope.row.dataType].style">{{ $channelTypeList[scope.row.dataType].name }}</el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="位置信息" min-width="150">
                    <template v-slot:default="scope">
                        <span v-if="scope.row.gbLongitude && scope.row.gbLatitude">
                            {{ scope.row.gbLongitude }}
                            <br />
                            {{ scope.row.gbLatitude }}
                        </span>
                        <span v-if="!scope.row.gbLongitude || !scope.row.gbLatitude">无</span>
                    </template>
                </el-table-column>
                <el-table-column prop="ptzType" label="云台类型" min-width="100">
                    <template v-slot:default="scope">
                        <div>{{ scope.row.ptzTypeText }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="状态" min-width="100">
                    <template v-slot:default="scope">
                        <div slot="reference" class="name-wrapper">
                            <el-tag v-if="scope.row.gbStatus === 'ON'" size="medium">在线</el-tag>
                            <el-tag v-if="scope.row.gbStatus !== 'ON'" size="medium" type="info">离线</el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="210" fixed="right">
                    <template v-slot:default="scope">
                        <el-button size="medium" :disabled="scope.row.gbStatus !== 'ON'" icon="el-icon-video-play" type="text" :loading="scope.row.playLoading" @click="sendDevicePush(scope.row)">播放</el-button>
                        <el-button
                            v-if="!!scope.row.streamId"
                            size="medium"
                            :disabled="device == null || device.online === 0"
                            icon="el-icon-switch-button"
                            type="text"
                            style="color: #f56c6c"
                            @click="stopDevicePush(scope.row)"
                        >
                            停止
                        </el-button>
                        <el-divider direction="vertical" />
                        <el-button size="medium" type="text" icon="el-icon-edit" v-if="$store.getters.authority !== 2" @click="handleEdit(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
        <devicePlayer ref="devicePlayer" />
        <channel-edit v-if="editId" :id="editId" :close-edit="closeEdit" />
    </div>
</template>

<script>
import { listChannel, getChannel, delChannel } from '@/api/iot/channel';
import devicePlayer from '../video/common/channelPlayer/index.vue';
import Edit from '../video/channel/edit.vue';
export default {
    name: 'Channel',
    dicts: ['sip_gen_status', 'video_type', 'channel_type'],
    components: {
        devicePlayer,
        ChannelEdit: Edit,
    },
    props: {
        device: {
            type: Object,
            // default: null,
            required: true, // 可选：确保必须传递
        },
    },
    watch: {
        // 获取到父组件传递的device后
        // device: function (newVal) {
        //     console.log('父组件传递的device:', newVal);
        //     this.deviceInfo = newVal;
        //     if (this.deviceInfo && this.deviceInfo.deviceId != 0) {
        //         this.queryParams.deviceSipId = this.deviceInfo.serialNumber;
        //     }
        // },

        // 深度监听 device 对象变化
        device: {
            deep: true, // 添加深度监听
            immediate: true, // 立即执行一次
            handler(newVal) {
                if (!newVal || !newVal.deviceId) return;

                this.deviceInfo = newVal;

                // 仅在 deviceId 有效且 serialNumber 变化时更新查询参数
                if (newVal.deviceId != 0 && newVal.serialNumber !== this.queryParams.deviceSipId) {
                    this.queryParams.deviceSipId = newVal.serialNumber;
                    this.getList(); // 重新获取列表
                }
            },
        },
    },
    data() {
        return {
            device: null,
            loadSnap: {},
            // 设备信息
            deviceInfo: {},
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 监控设备通道信息表格数据
            channelList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            //编辑
            editId: null,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                deviceSipId: null,
            },
            // 表单参数
            form: {},
        };
    },
    created() {
        if (this.device && this.device.serialNumber) {
            this.queryParams.deviceSipId = this.device.serialNumber;
            this.getList();
        }
    },
    methods: {
        //通知设备上传媒体流
        sendDevicePush: function (itemData) {
            itemData.playLoading = true;
            this.$store
                .dispatch('video/commonChanel/playChannel', itemData.gbId)
                .then((data) => {
                    itemData.streamId = data.stream;
                    this.$refs.devicePlayer.openDialog('media', itemData.gbId, {
                        streamInfo: data,
                        hasAudio: itemData.hasAudio,
                    });
                    setTimeout(() => {
                        this.initData();
                    }, 1000);
                })
                .finally(() => {
                    itemData.playLoading = false;
                });
        },
        /** 查询监控设备通道信息列表 */
        getList() {
            this.loading = true;

            this.$store
                .dispatch('video/commonChanel/getList', {
                    page: 1,
                    count: 15,
                    query: this.queryParams.deviceSipId,
                })
                .then((data) => {
                    this.total = data.total;
                    this.channelList = data.list;
                    this.channelList.forEach((e) => {
                        e.ptzType = e.ptzType + '';
                        this.$set(e, 'playLoading', false);
                    });

                    this.loading = false;
                });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                channelId: null,
                channelSipId: null,
                deviceSipId: null,
                channelName: null,
                manufacture: null,
                model: null,
                owner: null,
                civilcode: null,
                block: null,
                address: null,
                parentid: null,
                ipaddress: null,
                port: null,
                password: null,
                ptztype: null,
                ptztypetext: null,
                status: 0,
                longitude: null,
                latitude: null,
                streamid: null,
                subcount: null,
                parental: 1,
                hasaudio: 1,
            };
            this.resetForm('form');
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const channelId = row.channelId || this.ids;
            getChannel(channelId).then((response) => {
                this.form = response.data;
                this.open = true;
                this.title = '修改监控设备通道信息';
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const channelIds = row.channelId || this.ids;
            this.$modal
                .confirm('是否确认删除监控设备通道信息编号为"' + channelIds + '"的数据项？')
                .then(function () {
                    return delChannel(channelIds);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        // 编辑
        handleEdit(row) {
            console.log(row);
            this.editId = row.gbId;
        },
        // 结束编辑
        closeEdit: function () {
            this.editId = null;
            this.getChannelList();
        },
        getSnap: function (row) {
            console.log('getSnap:' + process.env.VUE_APP_BASE_API + '/profile/snap/' + row.deviceSipId + '_' + row.channelSipId + '.jpg');
            return process.env.VUE_APP_BASE_API + '/profile/snap/' + row.deviceSipId + '_' + row.channelSipId + '.jpg';
        },
        getBigSnap: function (row) {
            return [this.getSnap(row)];
        },
        isVideoChannel: function (row) {
            // let channelType = row.channelSipId.substring(10, 13);
            // // 111-DVR编码;112-视频服务器编码;118-网络视频录像机(NVR)编码;131-摄像机编码;132-网络摄像机(IPC)编码
            // return !(channelType !== '111' && channelType !== '112' && channelType !== '118' && channelType !== '131' && channelType !== '132');
        },
    },
};
</script>
