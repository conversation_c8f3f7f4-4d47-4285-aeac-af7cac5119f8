/**
 * Example usage of the Location Detection Service Infrastructure
 * This demonstrates how to use the location detection services
 */

import LocationDetectionService from './LocationDetectionService.js';

// Example 1: Basic usage with default configuration
async function basicLocationDetection() {
    const locationService = new LocationDetectionService();

    try {
        const location = await locationService.detectUserLocation();
        console.log('Detected location:', location);

        // Location object will contain:
        // {
        //   latitude: number,
        //   longitude: number,
        //   city: string,
        //   source: 'gps' | 'ip' | 'default',
        //   timestamp: number,
        //   accuracy?: number (for GPS)
        // }

        return location;
    } catch (error) {
        console.error('Location detection failed:', error);
        return locationService.getDefaultLocation();
    }
}

// Example 2: Custom configuration
async function customLocationDetection() {
    const locationService = new LocationDetectionService({
        timeout: 5000, // 5 second timeout
        enableIPFallback: true, // Enable IP-based fallback
        enableCache: true, // Enable session caching
        enableHighAccuracy: false, // Don't require high GPS accuracy
    });

    const location = await locationService.detectUserLocation();
    return location;
}

// Example 3: Using cached location
async function cachedLocationExample() {
    const locationService = new LocationDetectionService();

    // First call - will detect location
    const location1 = await locationService.detectUserLocation();
    console.log('First call:', location1);

    // Second call - will use cached location
    const location2 = await locationService.detectUserLocation();
    console.log('Second call (cached):', location2);

    // Clear cache if needed
    locationService.clearCache();
}

// Example 4: Configuration updates
async function dynamicConfigExample() {
    const locationService = new LocationDetectionService();

    // Update configuration at runtime
    locationService.updateConfig({
        timeout: 15000,
        enableIPFallback: false,
    });

    const location = await locationService.detectUserLocation();
    return location;
}

// Export examples for use in other parts of the application
export { basicLocationDetection, customLocationDetection, cachedLocationExample, dynamicConfigExample };
