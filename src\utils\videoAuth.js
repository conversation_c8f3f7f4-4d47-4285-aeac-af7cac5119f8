import { login } from '@/api/video/user';
import crypto from 'crypto';

const VIDEO_TOKEN_KEY = 'video_access_token';

// 获取视频模块token
export function getVideoToken() {
    return localStorage.getItem(VIDEO_TOKEN_KEY);
}

// 设置视频模块token
export function setVideoToken(token) {
    return localStorage.setItem(VIDEO_TOKEN_KEY, token);
}

// 移除视频模块token
export function removeVideoToken() {
    return localStorage.removeItem(VIDEO_TOKEN_KEY);
}

// 视频模块自动登录
export function videoAutoLogin() {
    return new Promise((resolve, reject) => {
        // 使用固定的用户名密码进行登录，密码需要MD5加密
        const password = 'admin';
        const encryptedPassword = crypto.createHash('md5').update(password, 'utf8').digest('hex');

        const loginParams = {
            username: 'admin',
            password: encryptedPassword,
        };

        console.log('开始视频模块自动登录...', loginParams);

        login(loginParams)
            .then((response) => {
                console.log('视频模块登录响应:', response);

                // 尝试多种可能的token字段
                let token = null;
                if (response) {
                    // 检查常见的token字段名
                    if (response.token) {
                        token = response.token;
                    } else if (response.data && response.data.token) {
                        token = response.data.token;
                    } else if (response.access_token) {
                        token = response.access_token;
                    } else if (response.data && response.data.access_token) {
                        token = response.data.access_token;
                    } else if (response.accessToken) {
                        token = response.accessToken;
                    } else if (response.data && response.data.accessToken) {
                        token = response.data.accessToken;
                    } else if (response['access-token']) {
                        token = response['access-token'];
                    } else if (response.data && response.data['access-token']) {
                        token = response.data['access-token'];
                    }
                }

                if (token) {
                    console.log('视频模块登录成功，获取到token:', token);
                    setVideoToken(token);
                    resolve(token);
                } else {
                    console.error('登录响应中未找到token字段:', response);
                    console.error('响应结构:', JSON.stringify(response, null, 2));
                    reject(new Error('登录响应格式错误，未找到token'));
                }
            })
            .catch((error) => {
                console.error('视频模块自动登录失败:', error);
                reject(error);
            });
    });
}

// 清除视频模块认证信息
export function clearVideoAuth() {
    removeVideoToken();
}
