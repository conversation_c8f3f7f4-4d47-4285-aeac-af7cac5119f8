/**
 * 中国主要城市坐标映射表
 * 用于根据城市名称获取对应的经纬度坐标
 * 在线拾取地址： https://geojson.cn/data/atlas/china
 */

export const cityCoordinatesMap = {
    // 直辖市
    北京: [116.405285, 39.904989],
    天津: [117.190182, 39.125596],
    上海: [121.472644, 31.231706],
    重庆: [106.504962, 29.533155],

    // 省会城市
    合肥: [117.283042, 31.86119],
    石家庄: [114.502461, 38.045474],
    太原: [112.549248, 37.857014],
    呼和浩特: [111.670801, 40.818311],
    沈阳: [123.429096, 41.796767],
    长春: [125.3245, 43.886841],
    哈尔滨: [126.642464, 45.756967],
    南京: [118.767413, 32.041544],
    杭州: [120.153576, 30.287459],
    福州: [119.306239, 26.075302],
    南昌: [115.892151, 28.676493],
    济南: [117.000923, 36.675807],
    郑州: [113.665412, 34.757975],
    武汉: [114.298572, 30.584355],
    长沙: [112.982279, 28.19409],
    广州: [113.280637, 23.125178],
    南宁: [108.320004, 22.82402],
    海口: [110.35044, 20.031971],
    成都: [104.065735, 30.659462],
    贵阳: [106.713478, 26.578343],
    昆明: [102.712251, 25.040609],
    拉萨: [91.132212, 29.660361],
    西安: [108.948024, 34.263161],
    兰州: [103.823557, 36.058039],
    西宁: [101.778916, 36.623178],
    银川: [106.278179, 38.46637],
    乌鲁木齐: [87.617733, 43.792818],

    // 内蒙古主要城市
    呼伦贝尔: [119.77845, 49.166536],
    乌海: [106.825563, 39.673734],
    包头: [109.840405, 40.658168],
    鄂尔多斯: [109.781327, 39.608266],
    赤峰: [118.87263, 42.275317],
    通辽: [122.263119, 43.617429],
    巴彦淖尔: [107.416959, 40.757402],
    乌兰察布: [113.114543, 41.034134],
    兴安盟: [122.037775, 46.082403],
    锡林郭勒: [116.047387, 43.933212],
    呼和浩特: [111.748814, 40.842127],
    阿拉善: [105.729139, 38.851559],

    // 安徽省主要城市
    芜湖: [118.376451, 31.326319],
    蚌埠: [117.363228, 32.929499],
    淮南: [117.018329, 32.647574],
    马鞍山: [118.507906, 31.689362],
    淮北: [116.794664, 33.971707],
    铜陵: [117.816576, 30.929935],
    安庆: [117.043551, 30.50883],
    黄山: [118.317325, 29.709239],
    滁州: [118.316264, 32.317351],
    阜阳: [115.819729, 32.896969],
    宿州: [116.984084, 33.633891],
    六安: [116.507676, 31.752889],
    亳州: [115.782939, 33.844582],
    池州: [117.489157, 30.656037],
    宣城: [118.758816, 30.945667],

    // 其他重要城市
    深圳: [114.085947, 22.547],
    青岛: [120.355173, 36.082982],
    大连: [121.618622, 38.91459],
    宁波: [121.549792, 29.868388],
    厦门: [118.11022, 24.490474],
    苏州: [120.619585, 31.299379],
    无锡: [120.301663, 31.574729],
    常州: [119.946973, 31.772752],
    南通: [120.864608, 32.016212],
    徐州: [117.184811, 34.261792],
    扬州: [119.421003, 32.393159],
    盐城: [120.139998, 33.377631],
    镇江: [119.452753, 32.204402],
    泰州: [119.915176, 32.484882],
    宿迁: [118.275162, 33.963008],
    温州: [120.672111, 28.000575],
    嘉兴: [120.750865, 30.762653],
    湖州: [120.102398, 30.867198],
    绍兴: [120.582112, 29.997117],
    金华: [119.649506, 29.089524],
    衢州: [118.87263, 28.941708],
    舟山: [122.207216, 29.985295],
    台州: [121.420757, 28.656386],
    丽水: [119.921786, 28.451993],
    东莞: [113.746262, 23.046237],
    佛山: [113.122717, 23.028762],
    中山: [113.382391, 22.521113],
    珠海: [113.553986, 22.224979],
    江门: [113.094942, 22.590431],
    湛江: [110.364977, 21.274898],
    茂名: [110.88536, 21.659751],
    肇庆: [112.472529, 23.051546],
    惠州: [114.412599, 23.079404],
    梅州: [116.117582, 24.299112],
    汕尾: [115.364238, 22.774485],
    河源: [114.697802, 23.746266],
    阳江: [111.975107, 21.859222],
    清远: [113.051227, 23.685022],
    潮州: [116.632301, 23.661701],
    揭阳: [116.355733, 23.543778],
    云浮: [112.044439, 22.929801],
};

/**
 * 根据城市名称获取坐标
 * @param {string} cityName 城市名称
 * @returns {Array|null} 返回[经度, 纬度]数组，如果未找到返回null
 */
export function getCityCoordinates(cityName) {
    if (!cityName) {
        return null;
    }

    // 直接匹配
    if (cityCoordinatesMap[cityName]) {
        return cityCoordinatesMap[cityName];
    }

    // 去掉"市"字后缀再匹配
    const cityNameWithoutSuffix = cityName.replace(/市$/, '');
    if (cityCoordinatesMap[cityNameWithoutSuffix]) {
        return cityCoordinatesMap[cityNameWithoutSuffix];
    }

    // 模糊匹配（包含关系）
    for (const [key, coordinates] of Object.entries(cityCoordinatesMap)) {
        if (key.includes(cityNameWithoutSuffix) || cityNameWithoutSuffix.includes(key)) {
            return coordinates;
        }
    }

    return null;
}

/**
 * 获取默认坐标（合肥）
 * @returns {Array} 返回[经度, 纬度]数组
 */
export function getDefaultCoordinates() {
    return cityCoordinatesMap['合肥'];
}
