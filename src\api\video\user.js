import request from '@/utils/videoRequest';
import loginRequest from '@/utils/loginRequest';

export function login(params) {
    // 登录接口使用专门的loginRequest，避免循环调用
    return loginRequest({
        url: '/dev-api/user/login', // 修正URL路径
        method: 'post',
        data: params, // 使用data而不是params，POST请求参数放在请求体中
    });
}

export function logout() {
    return request({
        url: '/api/user/logout',
        method: 'get',
    });
}
export function getUserInfo() {
    return request({
        method: 'post',
        url: '/api/user/userInfo',
    });
}

export function changePushKey(params) {
    const { pushKey, userId } = params;
    return request({
        method: 'post',
        url: '/api/user/changePushKey',
        params: {
            pushKey: pushKey,
            userId: userId,
        },
    });
}

export function queryList(params) {
    const { page, count } = params;
    return request({
        method: 'get',
        url: `/api/user/users`,
        params: {
            page: page,
            count: count,
        },
    });
}

export function removeById(id) {
    return request({
        method: 'delete',
        url: `/api/user/delete?id=${id}`,
    });
}

export function add(params) {
    const { username, password, roleId } = params;
    return request({
        method: 'post',
        url: '/api/user/add',
        params: {
            username: username,
            password: password,
            roleId: roleId,
        },
    });
}

export function changePassword(params) {
    const { oldPassword, password } = params;
    return request({
        method: 'post',
        url: '/api/user/changePassword',
        params: {
            oldPassword: oldPassword,
            password: password,
        },
    });
}

export function changePasswordForAdmin(params) {
    const { password, userId } = params;
    return request({
        method: 'post',
        url: '/api/user/changePasswordForAdmin',
        params: {
            password: password,
            userId: userId,
        },
    });
}
