# 页面标题
VUE_APP_TITLE = 辉采科技物联网云平台

NODE_ENV = production

# 测试环境配置
ENV = 'staging'

# 测试环境
VUE_APP_BASE_API = '/stage-api'

# 视频服务地址 - 测试环境
VUE_APP_VIDEO_SERVER_URL = 'http://************:19090'

# Mqtt消息服务器连接地址
VUE_APP_MQTT_SERVER_URL = 'ws://************:8083/mqtt'

# 百度地图AK
VUE_APP_BAI_DU_AK = 'nAtaBg9FYzav6c8P9rF9qzsWZXXXXXX'

# 位置检测配置
VUE_APP_ENABLE_LOCATION_DETECTION = true
VUE_APP_LOCATION_TIMEOUT = 10000
VUE_APP_ENABLE_LOCATION_CACHE = true
VUE_APP_ENABLE_IP_FALLBACK = true
VUE_APP_LOCATION_HIGH_ACCURACY = false
VUE_APP_IP_LOCATION_API_KEY = ''

