<template>
    <div class="navbar">
        <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

        <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav" />
        <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />

        <div class="right-menu">
            <template v-if="device !== 'mobile'">
                <!-- 新增的电脑图标 -->
                <el-tooltip content="数据大屏" effect="dark" placement="bottom" v-if="user.userName == 'admin'">
                    <i class="el-icon-monitor right-menu-item hover-effect computer-icon" @click="goToConsole" />
                </el-tooltip>

                <search id="header-search" class="right-menu-item" />

                <!-- <el-tooltip content="源码地址" effect="dark" placement="bottom">
                    <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
                </el-tooltip>

                <el-tooltip content="文档地址" effect="dark" placement="bottom">
                    <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
                </el-tooltip> -->

                <screenfull id="screenfull" class="right-menu-item hover-effect" />

                <el-tooltip content="布局大小" effect="dark" placement="bottom">
                    <size-select id="size-select" class="right-menu-item hover-effect" />
                </el-tooltip>
            </template>

            <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
                <div class="avatar-wrapper">
                    <img :src="avatar" class="user-avatar" />
                    <i class="el-icon-caret-bottom" />
                </div>
                <el-dropdown-menu slot="dropdown">
                    <router-link to="/user/profile">
                        <el-dropdown-item>个人中心</el-dropdown-item>
                    </router-link>
                    <el-dropdown-item @click.native="setting = true">
                        <span>布局设置</span>
                    </el-dropdown-item>
                    <el-dropdown-item divided @click.native="logout">
                        <span>退出登录</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Breadcrumb from '@/components/Breadcrumb';
import TopNav from '@/components/TopNav';
import Hamburger from '@/components/Hamburger';
import Screenfull from '@/components/Screenfull';
import SizeSelect from '@/components/SizeSelect';
import Search from '@/components/HeaderSearch';
import RuoYiGit from '@/components/RuoYi/Git';
import RuoYiDoc from '@/components/RuoYi/Doc';
import { getUserProfile } from '@/api/system/user';

export default {
    components: {
        Breadcrumb,
        TopNav,
        Hamburger,
        Screenfull,
        SizeSelect,
        Search,
        RuoYiGit,
        RuoYiDoc,
    },
    data() {
        return {
            user: {},
        };
    },
    computed: {
        ...mapGetters(['sidebar', 'avatar', 'device', 'userInfo']), // 添加 userInfo 到计算属性
        setting: {
            get() {
                return this.$store.state.settings.showSettings;
            },
            set(val) {
                this.$store.dispatch('settings/changeSetting', {
                    key: 'showSettings',
                    value: val,
                });
            },
        },
        topNav: {
            get() {
                return this.$store.state.settings.topNav;
            },
        },
    },

    created() {
        this.getUser();
    },

    methods: {
        toggleSideBar() {
            this.$store.dispatch('app/toggleSideBar');
        },
        async logout() {
            this.$confirm('确定注销并退出系统吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    this.$store.dispatch('LogOut').then(() => {
                        location.href = '/index';
                    });
                })
                .catch(() => {});
        },
        // 新增方法：跳转到控制台
        goToConsole() {
            window.open('http://www.seefyiot.com:9092/', '_blank');
        },
        getUser() {
            getUserProfile().then((response) => {
                this.user = response.data;
                console.log('当前用户信息:', this.user);
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .hamburger-container {
        line-height: 46px;
        height: 100%;
        float: left;
        cursor: pointer;
        transition: background 0.3s;
        -webkit-tap-highlight-color: transparent;

        &:hover {
            background: rgba(0, 0, 0, 0.025);
        }
    }

    .breadcrumb-container {
        float: left;
    }

    .topmenu-container {
        position: absolute;
        left: 50px;
    }

    .errLog-container {
        display: inline-block;
        vertical-align: top;
    }

    .right-menu {
        float: right;
        height: 100%;
        line-height: 50px;

        &:focus {
            outline: none;
        }

        .right-menu-item {
            display: inline-block;
            padding: 0 8px;
            height: 100%;
            font-size: 18px;
            color: #5a5e66;
            vertical-align: middle;

            &.hover-effect {
                cursor: pointer;
                transition: background 0.3s;

                &:hover {
                    background: rgba(0, 0, 0, 0.025);
                }
            }
        }

        // 新增电脑图标样式
        .computer-icon {
            color: #1a3e8c; // 深蓝色
            font-size: 18px; // 与搜索图标相同大小
            vertical-align: middle;
            margin-right: 4px;
            position: relative; // 为突出效果做准备

            // 确保与搜索图标同高度
            line-height: 50px;
            height: 50px;
            display: inline-flex;
            align-items: center;

            // 突出显示效果
            &:hover {
                color: #0d6efd; // 悬停时变为更亮的蓝色
                transform: scale(1.1); // 轻微放大
                transition: all 0.2s ease;
            }

            // 添加背景高亮效果
            &::after {
                content: '';
                position: absolute;
                bottom: 8px;
                left: 50%;
                transform: translateX(-50%);
                width: 24px;
                height: 2px;
                background-color: #1a3e8c;
                opacity: 0;
                transition: opacity 0.2s ease;
            }

            &:hover::after {
                opacity: 1; // 悬停时显示下划线
            }
        }

        .avatar-container {
            margin-right: 30px;

            .avatar-wrapper {
                margin-top: 5px;
                position: relative;

                .user-avatar {
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    border-radius: 10px;
                }

                .el-icon-caret-bottom {
                    cursor: pointer;
                    position: absolute;
                    right: -20px;
                    top: 25px;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
