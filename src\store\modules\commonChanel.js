import {
    update,
    add,
    reset,
    queryOne,
    addDeviceToGroup,
    deleteDeviceFromGroup,
    addDeviceToRegion,
    deleteDeviceFromRegion,
    getCivilCodeList,
    getParentList,
    getUnusualParentList,
    clearUnusualParentList,
    getUnusualCivilCodeList,
    clearUnusualCivilCodeList,
    getIndustryList,
    getTypeList,
    getNetworkIdentificationList,
    playChannel,
    addToRegion,
    deleteFromRegion,
    addToGroup,
    deleteFromGroup,
    getList,
    addPointForCruise,
    addPreset,
    auxiliary,
    callPreset,
    deletePointForCruise,
    deletePreset,
    focus,
    iris,
    ptz,
    queryPreset,
    setCruiseSpeed,
    setCruiseTime,
    setLeftForScan,
    setRightForScan,
    setSpeedForScan,
    startCruise,
    startScan,
    stopCruise,
    stopScan,
    wiper,
    getAllForMap,
} from '@/api/video/commonChannel';

const state = {
    channelList: [],
    total: 0,
};

const mutations = {
    SET_CHANNEL_LIST(state, { list, total }) {
        state.channelList = list;
        state.total = total;
    },
};

const actions = {
    // 获取通道列表
    getList({ commit }, params) {
        return new Promise((resolve, reject) => {
            getList(params)
                .then((response) => {
                    const { data } = response;
                    commit('SET_CHANNEL_LIST', data);
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 获取行政区划通道列表
    getCivilCodeList({ commit }, params) {
        return new Promise((resolve, reject) => {
            getCivilCodeList(params)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 播放通道
    playChannel({ commit }, channelId) {
        return new Promise((resolve, reject) => {
            playChannel(channelId)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 添加通道到区域
    addToRegion({ commit }, params) {
        return new Promise((resolve, reject) => {
            addToRegion(params)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 从区域删除通道
    deleteFromRegion({ commit }, channelIds) {
        return new Promise((resolve, reject) => {
            deleteFromRegion(channelIds)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 更新通道
    update({ commit }, formData) {
        return new Promise((resolve, reject) => {
            update(formData)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 添加通道
    add({ commit }, formData) {
        return new Promise((resolve, reject) => {
            add(formData)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 重置通道
    reset({ commit }, id) {
        return new Promise((resolve, reject) => {
            reset(id)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 查询单个通道
    queryOne({ commit }, id) {
        return new Promise((resolve, reject) => {
            queryOne(id)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },
};

const getters = {
    channelList: (state) => state.channelList,
    total: (state) => state.total,
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
};
