# 页面标题
VUE_APP_TITLE = 辉采科技物联网云平台

# 测试环境配置
ENV = 'test'

# 测试环境
VUE_APP_BASE_API = '/dev-api'
VIDEO_BASE_API = '/video-api'

# 视频服务地址 - 测试环境
VUE_APP_VIDEO_SERVER_URL = 'http://************:19090'

# 后端接口地址
VUE_APP_SERVER_API_URL = 'https://************/prod-api/'

# Mqtt消息服务器连接地址
VUE_APP_MQTT_SERVER_URL = 'wss://************/websocket-iot/mqtt'

# 百度地图AK
VUE_APP_BAI_DU_AK = 'ZxCQ6XAWHJTWinn4HuJJPg04cIve2G4t'

# 天地图API KEY，需要去天地图开发者中心申请
VUE_APP_TIANDITU_KEY=a139a28d19e779d05cd34fb7fd28db08

# 位置检测配置 - 测试环境禁用位置检测
VUE_APP_ENABLE_LOCATION_DETECTION = false
VUE_APP_LOCATION_TIMEOUT = 5000
VUE_APP_ENABLE_LOCATION_CACHE = false
VUE_APP_ENABLE_IP_FALLBACK = false
VUE_APP_LOCATION_HIGH_ACCURACY = false
VUE_APP_IP_LOCATION_API_KEY = ''
