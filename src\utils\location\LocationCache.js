/**
 * LocationCache - Session-based caching for user location data
 * Stores location information in sessionStorage with timestamp validation
 * Optimized to reduce repeated API calls with smart cache management
 */
class LocationCache {
    constructor() {
        this.sessionKey = 'user_location_cache';
        this.maxAge = 30 * 60 * 1000; // 30 minutes default cache duration
        this.backgroundRefreshThreshold = 0.8; // Refresh when 80% of cache time has passed
        this.lastAccessTime = 0;
        this.accessCount = 0;
        this.hitCount = 0;
    }

    /**
     * Retrieve cached location data
     * @returns {Object|null} Cached location data or null if not found/expired
     */
    get() {
        this.accessCount++;
        this.lastAccessTime = Date.now();

        try {
            const cachedData = sessionStorage.getItem(this.sessionKey);
            if (!cachedData) {
                return null;
            }

            const parsed = JSON.parse(cachedData);

            // Validate cache structure
            if (!this._isValidCacheStructure(parsed)) {
                this.clear();
                return null;
            }

            // Check if cache is still valid
            if (!this.isValid(parsed)) {
                this.clear();
                return null;
            }

            this.hitCount++;

            // Add background refresh hint if cache is getting old
            const locationData = parsed.locationData;
            if (this.shouldBackgroundRefresh(parsed)) {
                locationData._backgroundRefreshSuggested = true;
            }

            return locationData;
        } catch (error) {
            console.warn('Error reading location cache:', error);
            this.clear();
            return null;
        }
    }

    /**
     * Store location data in cache with timestamp
     * @param {Object} locationData - Location data to cache
     */
    set(locationData) {
        try {
            if (!locationData || typeof locationData !== 'object') {
                throw new Error('Invalid location data provided');
            }

            const cacheEntry = {
                locationData: {
                    ...locationData,
                    timestamp: Date.now(),
                },
                cachedAt: Date.now(),
            };

            sessionStorage.setItem(this.sessionKey, JSON.stringify(cacheEntry));
        } catch (error) {
            console.warn('Error storing location cache:', error);
        }
    }

    /**
     * Check if cached data is still valid
     * @param {Object} cacheEntry - Cache entry to validate
     * @returns {boolean} True if cache is valid
     */
    isValid(cacheEntry = null) {
        try {
            const entry = cacheEntry || this._getCacheEntry();
            if (!entry || !this._isValidCacheStructure(entry)) {
                return false;
            }

            const now = Date.now();
            const cacheAge = now - entry.cachedAt;

            return cacheAge < this.maxAge;
        } catch (error) {
            console.warn('Error validating cache:', error);
            return false;
        }
    }

    /**
     * Clear cached location data
     */
    clear() {
        try {
            sessionStorage.removeItem(this.sessionKey);
            // Reset statistics but keep access patterns for analysis
            this.hitCount = 0;
            this.accessCount = 0;
        } catch (error) {
            console.warn('Error clearing location cache:', error);
        }
    }

    /**
     * Check if cache should be refreshed in background
     * @private
     */
    shouldBackgroundRefresh(cacheEntry) {
        const now = Date.now();
        const age = now - cacheEntry.cachedAt;
        const refreshThreshold = this.maxAge * this.backgroundRefreshThreshold;

        return age > refreshThreshold;
    }

    /**
     * Get cache hit rate
     * @returns {number} Hit rate as percentage
     */
    getHitRate() {
        if (this.accessCount === 0) return 0;
        return (this.hitCount / this.accessCount) * 100;
    }

    /**
     * Get cache statistics for debugging and optimization
     * @returns {Object} Cache statistics
     */
    getStats() {
        try {
            // Try to get raw data directly to catch storage errors
            const cachedData = sessionStorage.getItem(this.sessionKey);
            if (!cachedData) {
                return {
                    exists: false,
                    accessCount: this.accessCount,
                    hitCount: this.hitCount,
                    hitRate: this.getHitRate(),
                };
            }

            const entry = JSON.parse(cachedData);
            if (!entry) {
                return {
                    exists: false,
                    accessCount: this.accessCount,
                    hitCount: this.hitCount,
                    hitRate: this.getHitRate(),
                };
            }

            const now = Date.now();
            const age = now - entry.cachedAt;
            const remainingTime = Math.max(0, this.maxAge - age);
            const shouldRefresh = this.shouldBackgroundRefresh(entry);

            return {
                exists: true,
                age: age,
                remainingTime: remainingTime,
                isValid: this.isValid(entry),
                source: entry.locationData?.source || 'unknown',
                accessCount: this.accessCount,
                hitCount: this.hitCount,
                hitRate: this.getHitRate(),
                shouldBackgroundRefresh: shouldRefresh,
                maxAge: this.maxAge,
                lastAccessTime: this.lastAccessTime,
            };
        } catch (error) {
            return {
                exists: false,
                error: error.message,
                accessCount: this.accessCount,
                hitCount: this.hitCount,
                hitRate: this.getHitRate(),
            };
        }
    }

    /**
     * Set custom cache duration
     * @param {number} maxAge - Maximum cache age in milliseconds
     */
    setMaxAge(maxAge) {
        if (typeof maxAge === 'number' && maxAge > 0) {
            this.maxAge = maxAge;
        }
    }

    /**
     * Get raw cache entry from sessionStorage
     * @private
     * @returns {Object|null} Raw cache entry
     */
    _getCacheEntry() {
        try {
            const cachedData = sessionStorage.getItem(this.sessionKey);
            return cachedData ? JSON.parse(cachedData) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Validate cache entry structure
     * @private
     * @param {Object} entry - Cache entry to validate
     * @returns {boolean} True if structure is valid
     */
    _isValidCacheStructure(entry) {
        if (!entry || typeof entry !== 'object') {
            return false;
        }
        if (!entry.locationData || typeof entry.locationData !== 'object') {
            return false;
        }
        if (typeof entry.cachedAt !== 'number' || entry.cachedAt <= 0) {
            return false;
        }
        return true;
    }
}

export default LocationCache;
