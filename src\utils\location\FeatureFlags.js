/**
 * Feature Flags for Location Detection
 * Provides centralized feature flag management for location detection functionality
 */

import LocationConfig from './LocationConfig';

class FeatureFlags {
    constructor() {
        this.config = LocationConfig;
    }

    /**
     * Check if location detection feature is enabled
     * @returns {boolean}
     */
    isLocationDetectionEnabled() {
        return this.config.isEnabled();
    }

    /**
     * Check if GPS location detection is enabled
     * @returns {boolean}
     */
    isGPSDetectionEnabled() {
        return this.config.isEnabled() && this.isGeolocationSupported();
    }

    /**
     * Check if IP-based location detection is enabled
     * @returns {boolean}
     */
    isIPDetectionEnabled() {
        return this.config.isEnabled() && this.config.isIPFallbackEnabled();
    }

    /**
     * Check if location caching is enabled
     * @returns {boolean}
     */
    isLocationCacheEnabled() {
        return this.config.isEnabled() && this.config.isCacheEnabled();
    }

    /**
     * Check if high accuracy GPS is enabled
     * @returns {boolean}
     */
    isHighAccuracyEnabled() {
        return this.config.get('enableHighAccuracy', false);
    }

    /**
     * Check if debug logging is enabled
     * @returns {boolean}
     */
    isDebugLoggingEnabled() {
        return this.config.get('enableDebugLogging', false);
    }

    /**
     * Check if performance monitoring is enabled
     * @returns {boolean}
     */
    isPerformanceMonitoringEnabled() {
        return this.config.get('enablePerformanceMonitoring', false);
    }

    /**
     * Check if browser supports geolocation
     * @returns {boolean}
     */
    isGeolocationSupported() {
        return typeof navigator !== 'undefined' && 'geolocation' in navigator;
    }

    /**
     * Check if running in secure context (required for geolocation)
     * @returns {boolean}
     */
    isSecureContext() {
        if (typeof window !== 'undefined' && window.isSecureContext) return true;
        if (typeof location !== 'undefined') {
            return location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
        }
        return true; // Assume secure context if we can't determine
    }

    /**
     * Check if all requirements for GPS detection are met
     * @returns {boolean}
     */
    canUseGPSDetection() {
        return this.isGPSDetectionEnabled() && this.isGeolocationSupported() && this.isSecureContext();
    }

    /**
     * Check if all requirements for IP detection are met
     * @returns {boolean}
     */
    canUseIPDetection() {
        return this.isIPDetectionEnabled() && this.config.getIPApiEndpoints().length > 0;
    }

    /**
     * Get feature flag status summary
     * @returns {Object} Summary of all feature flags
     */
    getFeatureFlagStatus() {
        return {
            locationDetection: this.isLocationDetectionEnabled(),
            gpsDetection: this.canUseGPSDetection(),
            ipDetection: this.canUseIPDetection(),
            locationCache: this.isLocationCacheEnabled(),
            highAccuracy: this.isHighAccuracyEnabled(),
            debugLogging: this.isDebugLoggingEnabled(),
            performanceMonitoring: this.isPerformanceMonitoringEnabled(),
            browserSupport: {
                geolocation: this.isGeolocationSupported(),
                secureContext: this.isSecureContext(),
            },
        };
    }

    /**
     * Log feature flag status (for debugging)
     */
    logFeatureFlags() {
        if (this.isDebugLoggingEnabled()) {
            console.group('Location Detection Feature Flags');
            const status = this.getFeatureFlagStatus();
            Object.entries(status).forEach(([key, value]) => {
                if (typeof value === 'object') {
                    console.group(key);
                    Object.entries(value).forEach(([subKey, subValue]) => {
                        console.log(`${subKey}:`, subValue);
                    });
                    console.groupEnd();
                } else {
                    console.log(`${key}:`, value);
                }
            });
            console.groupEnd();
        }
    }

    /**
     * Get runtime configuration for location detection
     * @returns {Object} Runtime configuration object
     */
    getRuntimeConfig() {
        if (!this.isLocationDetectionEnabled()) {
            return {
                enabled: false,
                reason: 'Location detection is disabled via feature flag',
            };
        }

        const config = {
            enabled: true,
            features: {
                gps: this.canUseGPSDetection(),
                ip: this.canUseIPDetection(),
                cache: this.isLocationCacheEnabled(),
                highAccuracy: this.isHighAccuracyEnabled(),
            },
            timeouts: {
                total: this.config.getTimeout('total'),
                gps: this.config.getTimeout('gps'),
                ip: this.config.getTimeout('ip'),
            },
            options: this.config.getGeolocationOptions(),
        };

        // Add warnings for disabled features
        const warnings = [];
        if (!this.canUseGPSDetection()) {
            if (!this.isGeolocationSupported()) {
                warnings.push('GPS detection disabled: Geolocation API not supported');
            } else if (!this.isSecureContext()) {
                warnings.push('GPS detection disabled: Requires HTTPS or localhost');
            }
        }

        if (!this.canUseIPDetection()) {
            warnings.push('IP detection disabled: No API endpoints configured');
        }

        if (warnings.length > 0) {
            config.warnings = warnings;
        }

        return config;
    }
}

// Create singleton instance
const featureFlags = new FeatureFlags();

// Log feature flags in development
if (process.env.NODE_ENV === 'development') {
    featureFlags.logFeatureFlags();
}

export default featureFlags;
