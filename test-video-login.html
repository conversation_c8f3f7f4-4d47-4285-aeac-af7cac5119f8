<!DOCTYPE html>
<html>
<head>
    <title>测试视频登录</title>
</head>
<body>
    <h1>视频登录测试</h1>
    <button onclick="testLogin()">测试登录</button>
    <div id="result"></div>

    <script>
        function testLogin() {
            const username = 'admin';
            const password = '21232f297a57a5a743894a0e4a801fc3'; // admin的MD5
            
            // 测试不同的URL格式
            const testUrls = [
                `/video-api/api/user/login?username=${username}&password=${password}`,
                `/video-api/dev-api/api/user/login?username=${username}&password=${password}`,
                `http://************:18080/api/user/login?username=${username}&password=${password}`,
                `http://localhost:3528/dev-api/api/user/login?username=${username}&password=${password}`
            ];
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<h3>测试结果:</h3>';
            
            testUrls.forEach((url, index) => {
                fetch(url, {
                    method: 'GET',
                    mode: 'cors'
                })
                .then(response => {
                    resultDiv.innerHTML += `<p>URL ${index + 1}: ${url}</p>`;
                    resultDiv.innerHTML += `<p>状态: ${response.status} ${response.statusText}</p>`;
                    return response.text();
                })
                .then(data => {
                    resultDiv.innerHTML += `<p>响应: ${data}</p><hr>`;
                })
                .catch(error => {
                    resultDiv.innerHTML += `<p>URL ${index + 1}: ${url}</p>`;
                    resultDiv.innerHTML += `<p>错误: ${error.message}</p><hr>`;
                });
            });
        }
    </script>
</body>
</html>
