/**
 * LocationErrorHandler - Centralized error management for location detection
 * Handles all types of location-related errors with appropriate fallback strategies
 */
class LocationErrorHandler {
    constructor(options = {}) {
        this.enableLogging = options.enableLogging !== false;
        this.logLevel = options.logLevel || 'warn'; // 'error', 'warn', 'info', 'debug'
        this.fallbackStrategies = {
            PERMISSION_DENIED: 'ip',
            POSITION_UNAVAILABLE: 'ip',
            TIMEOUT: 'ip',
            NETWORK_ERROR: 'default',
            UNKNOWN_ERROR: 'default',
        };
    }

    /**
     * Handle location detection errors with appropriate fallback strategy
     * @param {Error|Object} error - Error object or standardized error
     * @param {string} context - Context where error occurred ('gps', 'ip', 'cache')
     * @param {Object} options - Additional options for error handling
     * @returns {Object} Error handling result with fallback strategy
     */
    handle(error, context = 'unknown', options = {}) {
        const standardizedError = this.standardizeError(error, context);
        const fallbackStrategy = this.determineFallbackStrategy(standardizedError, options);

        // Log the error based on severity
        this.logError(standardizedError, context, fallbackStrategy);

        // Track error for analytics/monitoring
        this.trackError(standardizedError, context);

        return {
            error: standardizedError,
            fallback: fallbackStrategy,
            shouldRetry: this.shouldRetry(standardizedError, options),
            context,
        };
    }

    /**
     * Standardize different error formats into consistent structure
     * @param {Error|Object} error - Raw error object
     * @param {string} context - Context where error occurred
     * @returns {Object} Standardized error object
     */
    standardizeError(error, context) {
        // If already standardized, return as-is
        if (error && error.type && error.message) {
            return {
                ...error,
                context,
                timestamp: Date.now(),
            };
        }

        // Handle native Error objects
        if (error instanceof Error) {
            return {
                type: this.classifyError(error, context),
                message: error.message,
                originalError: error,
                context,
                timestamp: Date.now(),
            };
        }

        // Handle geolocation API errors
        if (error && typeof error.code === 'number') {
            return this.standardizeGeolocationError(error, context);
        }

        // Handle network/fetch errors
        if (error && error.name) {
            return this.standardizeNetworkError(error, context);
        }

        // Fallback for unknown error formats
        return {
            type: 'UNKNOWN_ERROR',
            message: error?.message || 'An unknown error occurred',
            originalError: error,
            context,
            timestamp: Date.now(),
        };
    }

    /**
     * Classify generic errors based on context and error properties
     * @param {Error} error - Native Error object
     * @param {string} context - Context where error occurred
     * @returns {string} Error type classification
     */
    classifyError(error, context) {
        const message = error.message.toLowerCase();

        if (message.includes('permission') || message.includes('denied')) {
            return 'PERMISSION_DENIED';
        }

        if (message.includes('timeout') || message.includes('timed out')) {
            return 'TIMEOUT';
        }

        if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
            return 'NETWORK_ERROR';
        }

        if (message.includes('unavailable') || message.includes('not available')) {
            return 'POSITION_UNAVAILABLE';
        }

        return 'UNKNOWN_ERROR';
    }

    /**
     * Standardize geolocation API errors
     * @param {GeolocationPositionError} error - Geolocation error
     * @param {string} context - Context where error occurred
     * @returns {Object} Standardized error object
     */
    standardizeGeolocationError(error, context) {
        const errorTypes = {
            1: 'PERMISSION_DENIED',
            2: 'POSITION_UNAVAILABLE',
            3: 'TIMEOUT',
        };

        const errorMessages = {
            1: 'User denied the request for geolocation',
            2: 'Location information is unavailable',
            3: 'The request to get user location timed out',
        };

        return {
            type: errorTypes[error.code] || 'UNKNOWN_ERROR',
            message: errorMessages[error.code] || 'An unknown geolocation error occurred',
            code: error.code,
            originalError: error,
            context,
            timestamp: Date.now(),
        };
    }

    /**
     * Standardize network/fetch errors
     * @param {Error} error - Network error
     * @param {string} context - Context where error occurred
     * @returns {Object} Standardized error object
     */
    standardizeNetworkError(error, context) {
        if (error.name === 'AbortError') {
            return {
                type: 'TIMEOUT',
                message: 'Request was aborted due to timeout',
                originalError: error,
                context,
                timestamp: Date.now(),
            };
        }

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return {
                type: 'NETWORK_ERROR',
                message: 'Network request failed',
                originalError: error,
                context,
                timestamp: Date.now(),
            };
        }

        return {
            type: 'NETWORK_ERROR',
            message: error.message || 'Network error occurred',
            originalError: error,
            context,
            timestamp: Date.now(),
        };
    }

    /**
     * Determine appropriate fallback strategy based on error type
     * @param {Object} error - Standardized error object
     * @param {Object} options - Additional options
     * @returns {string} Fallback strategy ('ip', 'default', 'retry', 'none')
     */
    determineFallbackStrategy(error, options = {}) {
        // Check for custom fallback strategy
        if (options.customFallback) {
            return options.customFallback;
        }

        // Use predefined fallback strategies
        const strategy = this.fallbackStrategies[error.type] || 'default';

        // Special cases based on context
        if (error.context === 'ip' && strategy === 'ip') {
            return 'default'; // Can't fallback to IP if IP already failed
        }

        if (error.context === 'cache') {
            return 'gps'; // If cache fails, try GPS
        }

        return strategy;
    }

    /**
     * Determine if operation should be retried
     * @param {Object} error - Standardized error object
     * @param {Object} options - Additional options
     * @returns {boolean} True if should retry
     */
    shouldRetry(error, options = {}) {
        // Don't retry permission denied errors
        if (error.type === 'PERMISSION_DENIED') {
            return false;
        }

        // Don't retry if max retries reached
        if (options.retryCount >= (options.maxRetries || 0)) {
            return false;
        }

        // Retry network errors and timeouts
        return ['NETWORK_ERROR', 'TIMEOUT', 'POSITION_UNAVAILABLE'].includes(error.type);
    }

    /**
     * Log error with appropriate level and context
     * @param {Object} error - Standardized error object
     * @param {string} context - Context where error occurred
     * @param {string} fallbackStrategy - Chosen fallback strategy
     */
    logError(error, context, fallbackStrategy) {
        if (!this.enableLogging) return;

        const logMessage = `Location ${context} error: ${error.message}`;
        const logData = {
            type: error.type,
            context,
            fallback: fallbackStrategy,
            timestamp: error.timestamp,
        };

        // Log based on error severity
        switch (error.type) {
            case 'PERMISSION_DENIED':
                this.log('info', `${logMessage} - User denied location access`, logData);
                break;

            case 'POSITION_UNAVAILABLE':
                this.log('warn', `${logMessage} - GPS position unavailable`, logData);
                break;

            case 'TIMEOUT':
                this.log('warn', `${logMessage} - Request timed out`, logData);
                break;

            case 'NETWORK_ERROR':
                this.log('error', `${logMessage} - Network request failed`, logData);
                break;

            default:
                this.log('error', `${logMessage} - Unknown error`, logData);
        }

        // Log fallback strategy
        if (fallbackStrategy !== 'none') {
            this.log('info', `Falling back to ${fallbackStrategy} location detection`, {
                context,
                fallback: fallbackStrategy,
            });
        }
    }

    /**
     * Internal logging method with level checking
     * @param {string} level - Log level ('error', 'warn', 'info', 'debug')
     * @param {string} message - Log message
     * @param {Object} data - Additional log data
     */
    log(level, message, data = {}) {
        const levels = { error: 0, warn: 1, info: 2, debug: 3 };
        const currentLevel = levels[this.logLevel] || 1;
        const messageLevel = levels[level] || 1;

        if (messageLevel <= currentLevel) {
            const logMethod = console[level] || console.log;
            logMethod(`[LocationDetection] ${message}`, data);
        }
    }

    /**
     * Track error for analytics and monitoring
     * @param {Object} error - Standardized error object
     * @param {string} context - Context where error occurred
     */
    trackError(error, context) {
        // This could be extended to send error data to analytics service
        // For now, we'll just track basic statistics in memory
        if (!window.locationErrorStats) {
            window.locationErrorStats = {
                total: 0,
                byType: {},
                byContext: {},
            };
        }

        const stats = window.locationErrorStats;
        stats.total++;
        stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        stats.byContext[context] = (stats.byContext[context] || 0) + 1;
    }

    /**
     * Get error statistics for monitoring
     * @returns {Object} Error statistics
     */
    static getErrorStats() {
        return (
            window.locationErrorStats || {
                total: 0,
                byType: {},
                byContext: {},
            }
        );
    }

    /**
     * Clear error statistics
     */
    static clearErrorStats() {
        window.locationErrorStats = {
            total: 0,
            byType: {},
            byContext: {},
        };
    }

    /**
     * Create user-friendly error messages for UI display
     * @param {Object} error - Standardized error object
     * @returns {string} User-friendly error message
     */
    getUserFriendlyMessage(error) {
        const messages = {
            PERMISSION_DENIED: 'Location access was denied. Using default map center.',
            POSITION_UNAVAILABLE: 'Your location could not be determined. Using default map center.',
            TIMEOUT: 'Location detection timed out. Using default map center.',
            NETWORK_ERROR: 'Unable to detect location due to network issues. Using default map center.',
            UNKNOWN_ERROR: 'Location detection failed. Using default map center.',
        };

        return messages[error.type] || messages.UNKNOWN_ERROR;
    }

    /**
     * Update fallback strategies
     * @param {Object} strategies - New fallback strategies
     */
    updateFallbackStrategies(strategies) {
        this.fallbackStrategies = { ...this.fallbackStrategies, ...strategies };
    }

    /**
     * Update logging configuration
     * @param {Object} config - New logging configuration
     */
    updateLoggingConfig(config) {
        if (config.enableLogging !== undefined) {
            this.enableLogging = config.enableLogging;
        }
        if (config.logLevel) {
            this.logLevel = config.logLevel;
        }
    }
}

export default LocationErrorHandler;
