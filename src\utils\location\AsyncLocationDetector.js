import PerformanceMonitor from './PerformanceMonitor.js';
import LocationDetectionService from './LocationDetectionService.js';

/**
 * AsyncLocationDetector - Optimized async location detection with performance monitoring
 * Provides non-blocking location detection with timeout management and caching optimizations
 */
class AsyncLocationDetector {
    constructor(options = {}) {
        this.options = {
            maxConcurrentDetections: 1,
            enablePerformanceMonitoring: true,
            enableSmartCaching: true,
            enableTimeoutOptimization: true,
            enableProgressiveTimeout: true,
            ...options,
        };

        // Initialize performance monitor
        this.performanceMonitor = new PerformanceMonitor();
        this.performanceMonitor.setEnabled(this.options.enablePerformanceMonitoring);

        // Initialize location detection service
        this.locationService = new LocationDetectionService(options);

        // Track active detections to prevent concurrent operations
        this.activeDetections = new Set();
        this.detectionQueue = [];
        this.lastDetectionTime = 0;
        this.consecutiveFailures = 0;

        // Smart caching with adaptive expiration
        this.smartCache = {
            enabled: this.options.enableSmartCaching,
            adaptiveExpiration: true,
            baseExpiration: 30 * 60 * 1000, // 30 minutes
            maxExpiration: 2 * 60 * 60 * 1000, // 2 hours
            minExpiration: 5 * 60 * 1000, // 5 minutes
        };

        // Progressive timeout optimization
        this.timeoutOptimization = {
            enabled: this.options.enableTimeoutOptimization,
            baseTimeout: 10000, // 10 seconds
            minTimeout: 3000, // 3 seconds
            maxTimeout: 15000, // 15 seconds
            adaptiveTimeout: true,
        };

        // Bind methods to preserve context
        this.detectLocation = this.detectLocation.bind(this);
        this.detectLocationNonBlocking = this.detectLocationNonBlocking.bind(this);
    }

    /**
     * Main async location detection method with performance optimization
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Location data
     */
    async detectLocation(options = {}) {
        const detectionId = this.generateDetectionId();
        const startTime = Date.now();

        // Start performance monitoring
        this.performanceMonitor.startTiming(detectionId, 'location-detection', {
            blocking: options.blocking !== false,
            concurrent: this.activeDetections.size,
        });

        try {
            // Check if we should use cached result
            if (this.shouldUseCachedResult()) {
                const cachedResult = await this.getCachedLocationWithMonitoring(detectionId);
                if (cachedResult) {
                    this.performanceMonitor.endTiming(detectionId, true, {
                        source: 'cache',
                        fromCache: true,
                    });
                    return cachedResult;
                }
            }

            // Check concurrent detection limit
            if (this.activeDetections.size >= this.options.maxConcurrentDetections) {
                if (options.queueIfBusy) {
                    return this.queueDetection(options);
                } else {
                    throw new Error('Maximum concurrent detections reached');
                }
            }

            // Add to active detections
            this.activeDetections.add(detectionId);

            // Perform optimized location detection
            const locationData = await this.performOptimizedDetection(detectionId, options);

            // Update success metrics
            this.consecutiveFailures = 0;
            this.lastDetectionTime = Date.now();

            // Cache the result with smart expiration
            if (locationData && this.smartCache.enabled) {
                this.cacheLocationWithSmartExpiration(locationData);
            }

            this.performanceMonitor.endTiming(detectionId, true, {
                source: locationData?.source || 'unknown',
                fromCache: false,
            });

            return locationData;
        } catch (error) {
            this.consecutiveFailures++;
            this.performanceMonitor.endTiming(detectionId, false, {
                error: error.message,
                errorType: error.type || 'UNKNOWN',
            });
            throw error;
        } finally {
            this.activeDetections.delete(detectionId);
            this.processDetectionQueue();
        }
    }

    /**
     * Non-blocking location detection that doesn't interfere with UI
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Location data
     */
    async detectLocationNonBlocking(options = {}) {
        return new Promise((resolve, reject) => {
            // Use setTimeout to ensure this runs after current call stack
            setTimeout(async () => {
                try {
                    const result = await this.detectLocation({
                        ...options,
                        blocking: false,
                        priority: 'background',
                    });
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            }, 0);
        });
    }

    /**
     * Perform optimized location detection with progressive timeouts
     * @private
     */
    async performOptimizedDetection(detectionId, options) {
        const optimizedTimeout = this.calculateOptimalTimeout();
        const detectionOptions = {
            ...options,
            timeout: optimizedTimeout,
        };

        // Update location service timeout
        this.locationService.updateConfig({ timeout: optimizedTimeout });

        // Perform detection with monitoring
        return await this.locationService.detectUserLocation();
    }

    /**
     * Calculate optimal timeout based on historical performance
     * @private
     */
    calculateOptimalTimeout() {
        if (!this.timeoutOptimization.enabled || !this.timeoutOptimization.adaptiveTimeout) {
            return this.timeoutOptimization.baseTimeout;
        }

        const report = this.performanceMonitor.getPerformanceReport();
        const avgTime = report.summary.averageTime;

        if (avgTime === 0) {
            return this.timeoutOptimization.baseTimeout;
        }

        // Calculate adaptive timeout based on average time + buffer
        let adaptiveTimeout = Math.ceil(avgTime * 1.5); // 50% buffer

        // Apply consecutive failure penalty
        if (this.consecutiveFailures > 0) {
            const penalty = Math.min(this.consecutiveFailures * 1000, 5000); // Max 5s penalty
            adaptiveTimeout += penalty;
        }

        // Ensure timeout is within bounds
        adaptiveTimeout = Math.max(this.timeoutOptimization.minTimeout, adaptiveTimeout);
        adaptiveTimeout = Math.min(this.timeoutOptimization.maxTimeout, adaptiveTimeout);

        return adaptiveTimeout;
    }

    /**
     * Get cached location with performance monitoring
     * @private
     */
    async getCachedLocationWithMonitoring(detectionId) {
        const cacheId = `${detectionId}-cache`;
        this.performanceMonitor.startTiming(cacheId, 'cache');

        try {
            const cachedLocation = this.locationService.getCachedLocation();
            this.performanceMonitor.endTiming(cacheId, !!cachedLocation);
            return cachedLocation;
        } catch (error) {
            this.performanceMonitor.endTiming(cacheId, false, { error: error.message });
            return null;
        }
    }

    /**
     * Cache location with smart expiration based on success rate
     * @private
     */
    cacheLocationWithSmartExpiration(locationData) {
        if (!this.smartCache.adaptiveExpiration) {
            this.locationService.cacheLocation(locationData);
            return;
        }

        const report = this.performanceMonitor.getPerformanceReport();
        const successRate = report.summary.successRate;

        // Calculate adaptive expiration based on success rate
        let expiration = this.smartCache.baseExpiration;

        if (successRate > 80) {
            // High success rate - cache longer
            expiration = Math.min(expiration * 1.5, this.smartCache.maxExpiration);
        } else if (successRate < 50) {
            // Low success rate - cache shorter
            expiration = Math.max(expiration * 0.5, this.smartCache.minExpiration);
        }

        // Update cache max age
        this.locationService.cache.setMaxAge(expiration);
        this.locationService.cacheLocation(locationData);
    }

    /**
     * Check if we should use cached result based on performance metrics
     * @private
     */
    shouldUseCachedResult() {
        if (!this.smartCache.enabled) return true;

        const timeSinceLastDetection = Date.now() - this.lastDetectionTime;
        const report = this.performanceMonitor.getPerformanceReport();

        // Use cache more aggressively if recent detections were slow
        if (report.summary.averageTime > 8000 && timeSinceLastDetection < 60000) {
            return true;
        }

        // Use cache more aggressively if there were recent failures
        if (this.consecutiveFailures > 2) {
            return true;
        }

        return true; // Default behavior
    }

    /**
     * Queue detection request when at concurrent limit
     * @private
     */
    async queueDetection(options) {
        return new Promise((resolve, reject) => {
            this.detectionQueue.push({
                resolve,
                reject,
                options,
                timestamp: Date.now(),
            });

            // Set timeout for queued request
            setTimeout(() => {
                const index = this.detectionQueue.findIndex((item) => item.resolve === resolve);
                if (index !== -1) {
                    this.detectionQueue.splice(index, 1);
                    reject(new Error('Queued detection request timed out'));
                }
            }, options.queueTimeout || 30000);
        });
    }

    /**
     * Process queued detection requests
     * @private
     */
    async processDetectionQueue() {
        if (this.detectionQueue.length === 0 || this.activeDetections.size >= this.options.maxConcurrentDetections) {
            return;
        }

        const queuedRequest = this.detectionQueue.shift();
        if (queuedRequest) {
            try {
                const result = await this.detectLocation(queuedRequest.options);
                queuedRequest.resolve(result);
            } catch (error) {
                queuedRequest.reject(error);
            }
        }
    }

    /**
     * Generate unique detection ID
     * @private
     */
    generateDetectionId() {
        return `detection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Get performance report
     * @returns {Object} Performance metrics and recommendations
     */
    getPerformanceReport() {
        return this.performanceMonitor.getPerformanceReport();
    }

    /**
     * Get current status including active detections and queue
     * @returns {Object} Current status
     */
    getStatus() {
        return {
            activeDetections: this.activeDetections.size,
            queuedDetections: this.detectionQueue.length,
            consecutiveFailures: this.consecutiveFailures,
            lastDetectionTime: this.lastDetectionTime,
            performanceMonitor: this.performanceMonitor.getStatus(),
            smartCache: this.smartCache,
            timeoutOptimization: this.timeoutOptimization,
        };
    }

    /**
     * Update configuration
     * @param {Object} options - New configuration options
     */
    updateConfig(options) {
        this.options = { ...this.options, ...options };

        // Update performance monitor
        if (options.enablePerformanceMonitoring !== undefined) {
            this.performanceMonitor.setEnabled(options.enablePerformanceMonitoring);
        }

        // Update smart cache settings
        if (options.smartCache) {
            this.smartCache = { ...this.smartCache, ...options.smartCache };
        }

        // Update timeout optimization
        if (options.timeoutOptimization) {
            this.timeoutOptimization = { ...this.timeoutOptimization, ...options.timeoutOptimization };
        }

        // Update underlying location service
        this.locationService.updateConfig(options);
    }

    /**
     * Reset performance metrics and clear cache
     */
    reset() {
        this.performanceMonitor.resetMetrics();
        this.activeDetections.clear();
        this.detectionQueue.length = 0;
        this.consecutiveFailures = 0;
        this.lastDetectionTime = 0;
        this.locationService.clearCache();
    }

    /**
     * Cleanup resources
     */
    destroy() {
        this.reset();
        this.performanceMonitor.setEnabled(false);
    }
}

export default AsyncLocationDetector;
