/**
 * Example integration of location detection with map initialization
 * This demonstrates how to integrate LocationDetectionService and MapCenterManager
 * with existing map initialization code
 */

import LocationDetectionService from './LocationDetectionService.js';
import MapCenterManager from './MapCenterManager.js';

/**
 * Example integration class showing how to use location detection
 * with existing map initialization
 */
export class LocationIntegrationExample {
    constructor(mapInstance) {
        this.map = mapInstance;
        this.locationService = null;
        this.mapCenterManager = null;
        this.locationDetectionInProgress = false;
    }

    /**
     * Initialize location services
     */
    initLocationServices() {
        try {
            // Initialize location detection service
            this.locationService = new LocationDetectionService({
                timeout: 10000,
                enableIPFallback: true,
                enableCache: true,
                enableHighAccuracy: false,
            });

            // Initialize map center manager
            this.mapCenterManager = new MapCenterManager(this.map, {
                animationDuration: 1000,
                defaultZoomLevel: 12,
                enableAnimation: true,
            });

            console.log('Location services initialized successfully');
        } catch (error) {
            console.error('Failed to initialize location services:', error);
        }
    }

    /**
     * Modified setInitialMapView that integrates location detection
     * This is non-blocking and doesn't delay map loading
     */
    async setInitialMapView(deviceData = []) {
        // Set default center first (Hefei)
        const hefeiCenter = new T.LngLat(117.27, 31.86);

        if (deviceData && deviceData.length > 0) {
            // If we have device data, show devices first
            this.setMapViewFromDevices(deviceData);
            console.log('Map centered on device data');
        } else {
            // No device data, use default center and wait for location detection
            this.map.centerAndZoom(hefeiCenter, 12);
            console.log('Map centered on default location, waiting for location detection');

            // Start location detection asynchronously
            this.startLocationDetection();
        }
    }

    /**
     * Start location detection (non-blocking)
     */
    async startLocationDetection() {
        if (!this.locationService || !this.mapCenterManager) {
            console.warn('Location services not initialized');
            return;
        }

        if (this.locationDetectionInProgress) {
            console.log('Location detection already in progress');
            return;
        }

        try {
            this.locationDetectionInProgress = true;
            console.log('Starting location detection...');

            // Detect user location asynchronously
            const locationData = await this.locationService.detectUserLocation();

            if (locationData && locationData.source !== 'default') {
                await this.updateMapCenterFromLocation(locationData);
            }
        } catch (error) {
            console.error('Location detection failed:', error);
        } finally {
            this.locationDetectionInProgress = false;
        }
    }

    /**
     * Update map center based on detected location
     */
    async updateMapCenterFromLocation(locationData) {
        if (!this.mapCenterManager || !locationData) {
            return;
        }

        try {
            // Only update if we don't have device data to show
            const success = await this.mapCenterManager.updateCenterFromLocation(locationData, {
                enableAnimation: true,
            });

            if (success) {
                console.log(`Map center updated to user location: ${locationData.city || 'detected location'} (${locationData.source})`);
            } else {
                console.log('Map center update skipped (user may be interacting)');
            }
        } catch (error) {
            console.error('Failed to update map center:', error);
        }
    }

    /**
     * Set map view based on device data
     */
    setMapViewFromDevices(deviceData) {
        // This would contain the existing logic for calculating optimal viewport
        // based on device locations
        console.log('Setting map view based on device data:', deviceData.length, 'devices');

        // Example implementation - in real code this would use the existing
        // calculateOptimalViewport and setMapViewFromInfo methods
        const hefeiCenter = new T.LngLat(117.27, 31.86);
        this.map.centerAndZoom(hefeiCenter, 12);
    }

    /**
     * Cleanup location services
     */
    destroy() {
        if (this.mapCenterManager) {
            this.mapCenterManager.destroy();
            this.mapCenterManager = null;
        }

        if (this.locationService) {
            this.locationService.clearCache();
            this.locationService = null;
        }
    }
}

/**
 * Example usage in a Vue component:
 *
 * // In component data:
 * data() {
 *   return {
 *     map: null,
 *     locationIntegration: null
 *   }
 * }
 *
 * // In initMap method:
 * initMap() {
 *   this.map = new T.Map('mapContainer');
 *
 *   // Initialize location integration
 *   this.locationIntegration = new LocationIntegrationExample(this.map);
 *   this.locationIntegration.initLocationServices();
 *
 *   // Set initial map view with location detection
 *   this.locationIntegration.setInitialMapView(this.deviceData);
 *
 *   // ... rest of map initialization
 * }
 *
 * // In beforeDestroy:
 * beforeDestroy() {
 *   if (this.locationIntegration) {
 *     this.locationIntegration.destroy();
 *   }
 * }
 */

export default LocationIntegrationExample;

/**
 * Enhanced location integration with comprehensive error handling
 * This demonstrates the new error handling capabilities added in task 7
 */
export class EnhancedLocationIntegration {
    constructor(mapInstance, options = {}) {
        this.map = mapInstance;
        this.options = {
            enableDetailedLogging: options.enableDetailedLogging || false,
            logLevel: options.logLevel || 'warn',
            customFallbackStrategies: options.customFallbackStrategies || {},
            ...options,
        };

        this.locationService = null;
        this.mapCenterManager = null;
        this.locationDetectionInProgress = false;
    }

    /**
     * Initialize location services with comprehensive error handling
     */
    initLocationServices() {
        try {
            // Initialize location detection service with error handling configuration
            this.locationService = new LocationDetectionService({
                timeout: 10000,
                enableIPFallback: true,
                enableCache: true,
                enableHighAccuracy: false,
                errorHandling: {
                    enableLogging: this.options.enableDetailedLogging,
                    logLevel: this.options.logLevel,
                    fallbackStrategies: {
                        PERMISSION_DENIED: 'ip',
                        POSITION_UNAVAILABLE: 'ip',
                        TIMEOUT: 'ip',
                        NETWORK_ERROR: 'default',
                        ...this.options.customFallbackStrategies,
                    },
                },
            });

            // Initialize map center manager
            this.mapCenterManager = new MapCenterManager(this.map, {
                animationDuration: 1000,
                defaultZoomLevel: 12,
                enableAnimation: true,
            });

            console.log('Enhanced location services initialized with error handling');
        } catch (error) {
            console.error('Failed to initialize enhanced location services:', error);
        }
    }

    /**
     * Start location detection with comprehensive error handling and monitoring
     */
    async startLocationDetectionWithErrorHandling() {
        if (!this.locationService || !this.mapCenterManager) {
            console.warn('Location services not initialized');
            return { success: false, error: 'Services not initialized' };
        }

        if (this.locationDetectionInProgress) {
            console.log('Location detection already in progress');
            return { success: false, error: 'Detection in progress' };
        }

        try {
            this.locationDetectionInProgress = true;
            console.log('Starting enhanced location detection with error handling...');

            // Clear previous error statistics
            this.locationService.clearErrorStats();

            // Detect user location with built-in error handling
            const locationData = await this.locationService.detectUserLocation();

            // Get error statistics for monitoring
            const errorStats = this.locationService.getErrorStats();

            if (locationData && locationData.source !== 'default') {
                const mapUpdateResult = await this.updateMapCenterFromLocation(locationData);

                return {
                    success: true,
                    location: locationData,
                    mapUpdated: mapUpdateResult.success,
                    errorStats,
                    source: locationData.source,
                };
            } else {
                return {
                    success: true,
                    location: locationData,
                    mapUpdated: false,
                    errorStats,
                    source: 'default',
                    message: 'Using default location',
                };
            }
        } catch (error) {
            console.error('Enhanced location detection failed:', error);

            // Get user-friendly error message
            const userMessage = this.locationService.getUserFriendlyErrorMessage(error);
            const errorStats = this.locationService.getErrorStats();

            return {
                success: false,
                error: error.message,
                userMessage,
                errorStats,
                location: this.locationService.getDefaultLocation(),
            };
        } finally {
            this.locationDetectionInProgress = false;
        }
    }

    /**
     * Update map center with error handling
     */
    async updateMapCenterFromLocation(locationData) {
        if (!this.mapCenterManager || !locationData) {
            return { success: false, error: 'Invalid parameters' };
        }

        try {
            const success = await this.mapCenterManager.updateCenterFromLocation(locationData, {
                enableAnimation: true,
                respectUserInteraction: true,
            });

            if (success) {
                console.log(`Map center updated to: ${locationData.city || 'detected location'} (${locationData.source})`);
                return { success: true, city: locationData.city, source: locationData.source };
            } else {
                console.log('Map center update skipped (user interaction or invalid data)');
                return { success: false, reason: 'User interaction or invalid data' };
            }
        } catch (error) {
            console.error('Failed to update map center:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get comprehensive error report for monitoring
     */
    getErrorReport() {
        if (!this.locationService) {
            return { error: 'Location service not initialized' };
        }

        const errorStats = this.locationService.getErrorStats();

        return {
            totalErrors: errorStats.total,
            errorsByType: errorStats.byType,
            errorsByContext: errorStats.byContext,
            mostCommonError: Object.keys(errorStats.byType).reduce((a, b) => (errorStats.byType[a] > (errorStats.byType[b] || 0) ? a : b), 'none'),
            timestamp: new Date().toISOString(),
            hasErrors: errorStats.total > 0,
        };
    }

    /**
     * Handle different error scenarios with specific strategies
     */
    async handleLocationErrorScenarios() {
        const results = {
            permissionDenied: null,
            networkError: null,
            timeout: null,
            fallbackUsed: false,
        };

        try {
            // Test permission state first
            const permissionState = await GeolocationProvider.checkPermission();
            results.permissionState = permissionState;

            if (permissionState === 'denied') {
                console.log('Location permission denied - will use IP fallback');
                results.permissionDenied = true;
            }

            // Attempt location detection
            const locationResult = await this.startLocationDetectionWithErrorHandling();

            if (locationResult.success) {
                results.location = locationResult.location;
                results.source = locationResult.source;
                results.fallbackUsed = locationResult.source !== 'gps';
            } else {
                results.error = locationResult.error;
                results.userMessage = locationResult.userMessage;
            }

            results.errorStats = locationResult.errorStats;
        } catch (error) {
            results.criticalError = error.message;
        }

        return results;
    }

    /**
     * Cleanup with error statistics reporting
     */
    destroy() {
        try {
            // Get final error report before cleanup
            const finalReport = this.getErrorReport();

            if (finalReport.hasErrors) {
                console.log('Final location error report:', finalReport);
            }

            if (this.mapCenterManager) {
                this.mapCenterManager.destroy();
                this.mapCenterManager = null;
            }

            if (this.locationService) {
                this.locationService.clearCache();
                this.locationService.clearErrorStats();
                this.locationService = null;
            }

            console.log('Enhanced location integration cleaned up');
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }
}

/**
 * Utility functions for error handling demonstrations
 */
export const LocationErrorHandlingUtils = {
    /**
     * Create location service with environment-specific error handling
     */
    createServiceForEnvironment(environment = 'production') {
        const configs = {
            development: {
                timeout: 15000,
                enableIPFallback: true,
                enableCache: true,
                errorHandling: {
                    enableLogging: true,
                    logLevel: 'debug',
                },
            },
            testing: {
                timeout: 5000,
                enableIPFallback: false,
                enableCache: false,
                errorHandling: {
                    enableLogging: false,
                    logLevel: 'error',
                },
            },
            production: {
                timeout: 10000,
                enableIPFallback: true,
                enableCache: true,
                errorHandling: {
                    enableLogging: true,
                    logLevel: 'warn',
                    fallbackStrategies: {
                        TIMEOUT: 'default', // Faster fallback in production
                    },
                },
            },
        };

        const config = configs[environment] || configs.production;
        return new LocationDetectionService(config);
    },

    /**
     * Monitor location errors over time
     */
    createErrorMonitor() {
        let errorHistory = [];

        return {
            recordError: (error, context) => {
                errorHistory.push({
                    error: error.type || 'UNKNOWN',
                    context,
                    timestamp: Date.now(),
                    message: error.message,
                });

                // Keep only last 100 errors
                if (errorHistory.length > 100) {
                    errorHistory = errorHistory.slice(-100);
                }
            },

            getErrorTrends: () => {
                const last24h = Date.now() - 24 * 60 * 60 * 1000;
                const recentErrors = errorHistory.filter((e) => e.timestamp > last24h);

                const trends = {};
                recentErrors.forEach((error) => {
                    trends[error.error] = (trends[error.error] || 0) + 1;
                });

                return {
                    total: recentErrors.length,
                    trends,
                    timeframe: '24h',
                };
            },

            clearHistory: () => {
                errorHistory = [];
            },
        };
    },
};

/**
 * Example usage with comprehensive error handling:
 *
 * // Initialize with error handling
 * const locationIntegration = new EnhancedLocationIntegration(mapInstance, {
 *     enableDetailedLogging: true,
 *     logLevel: 'info',
 *     customFallbackStrategies: {
 *         TIMEOUT: 'default' // Custom strategy
 *     }
 * });
 *
 * locationIntegration.initLocationServices();
 *
 * // Start location detection with error handling
 * const result = await locationIntegration.startLocationDetectionWithErrorHandling();
 *
 * if (result.success) {
 *     console.log('Location detected:', result.location.city, result.source);
 * } else {
 *     console.error('Location detection failed:', result.userMessage);
 * }
 *
 * // Get error report for monitoring
 * const errorReport = locationIntegration.getErrorReport();
 * if (errorReport.hasErrors) {
 *     // Send to analytics service
 *     analytics.track('location_errors', errorReport);
 * }
 */
