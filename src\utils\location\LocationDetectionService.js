import GeolocationProvider from './GeolocationProvider.js';
import LocationCache from './LocationCache.js';
import LocationErrorHandler from './LocationErrorHandler.js';
import IPLocationProvider from './IPLocationProvider.js';
import LocationConfig from './LocationConfig.js';
import FeatureFlags from './FeatureFlags.js';
import PerformanceMonitor from './PerformanceMonitor.js';

/**
 * LocationDetectionService - Core service for detecting user location
 * Handles GPS and IP-based location detection with caching and fallback logic
 */
class LocationDetectionService {
    constructor(options = {}) {
        // Load configuration from environment and merge with options
        this.config = LocationConfig;
        this.featureFlags = FeatureFlags;

        // Override config with provided options
        this.timeout = options.timeout || this.config.getTimeout('total');
        this.enableIPFallback = options.enableIPFallback !== undefined ? options.enableIPFallback : this.config.isIPFallbackEnabled();
        this.enableCache = options.enableCache !== undefined ? options.enableCache : this.config.isCacheEnabled();
        this.enableHighAccuracy = options.enableHighAccuracy !== undefined ? options.enableHighAccuracy : this.config.get('enableHighAccuracy');

        // Initialize components
        this.cache = new LocationCache();
        this.errorHandler = new LocationErrorHandler(options.errorHandling || {});
        this.ipProvider = new IPLocationProvider({
            timeout: this.config.getTimeout('ip'),
            apiKey: this.config.get('ipApiKey'),
            endpoints: this.config.getIPApiEndpoints(),
        });

        // Initialize performance monitor
        this.performanceMonitor = new PerformanceMonitor();
        this.performanceMonitor.setEnabled(options.enablePerformanceMonitoring !== false);

        // Get default location from config
        const defaultCenter = this.config.get('defaultCenter');
        this.defaultLocation = {
            latitude: defaultCenter.lat,
            longitude: defaultCenter.lng,
            city: 'Default Location',
            source: 'default',
            timestamp: Date.now(),
        };

        // Log configuration if debug is enabled
        if (this.config.get('enableDebugLogging')) {
            this.logServiceConfiguration();
        }
    }

    /**
     * Main method to detect user location with fallback chain
     * @returns {Promise<Object>} Location data with coordinates, city, and source
     */
    async detectUserLocation() {
        const detectionId = `detection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Start performance monitoring
        this.performanceMonitor.startTiming(detectionId, 'location-detection');

        // Check if location detection is enabled
        if (!this.featureFlags.isLocationDetectionEnabled()) {
            this.errorHandler.log('info', 'Location detection is disabled via feature flag');
            this.performanceMonitor.endTiming(detectionId, true, { source: 'disabled' });
            return this.defaultLocation;
        }
        try {
            // Check cache first if enabled
            if (this.enableCache) {
                const cacheId = `${detectionId}_cache`;
                this.performanceMonitor.startTiming(cacheId, 'cache');

                try {
                    const cachedLocation = this.getCachedLocation();
                    if (cachedLocation) {
                        this.performanceMonitor.endTiming(cacheId, true);
                        this.performanceMonitor.endTiming(detectionId, true, { source: 'cache', fromCache: true });
                        this.errorHandler.log('info', 'Using cached location', { city: cachedLocation.city });
                        return cachedLocation;
                    }
                    this.performanceMonitor.endTiming(cacheId, false, { reason: 'cache_miss' });
                } catch (cacheError) {
                    this.performanceMonitor.endTiming(cacheId, false, { error: cacheError.message });
                    const errorResult = this.errorHandler.handle(cacheError, 'cache');
                    this.errorHandler.log('warn', 'Cache retrieval failed, proceeding with fresh detection', errorResult);
                }
            }

            // Try GPS location first
            const gpsId = `${detectionId}_gps`;
            this.performanceMonitor.startTiming(gpsId, 'gps');

            try {
                const gpsLocation = await this.getGPSLocation();
                if (gpsLocation) {
                    const locationData = {
                        ...gpsLocation,
                        source: 'gps',
                        timestamp: Date.now(),
                    };

                    this.performanceMonitor.endTiming(gpsId, true, { accuracy: locationData.accuracy });
                    this.cacheLocation(locationData);
                    this.performanceMonitor.endTiming(detectionId, true, { source: 'gps', fromCache: false });
                    this.errorHandler.log('info', 'GPS location detected successfully', {
                        city: locationData.city,
                        accuracy: locationData.accuracy,
                    });
                    return locationData;
                }
                this.performanceMonitor.endTiming(gpsId, false, { reason: 'no_location' });
            } catch (gpsError) {
                this.performanceMonitor.endTiming(gpsId, false, {
                    error: gpsError.message,
                    errorType: gpsError.type,
                });
                const errorResult = this.errorHandler.handle(gpsError, 'gps');

                // If fallback is not IP, skip IP detection
                if (errorResult.fallback !== 'ip') {
                    this.errorHandler.log('info', 'Skipping IP fallback, using default location', errorResult);
                    this.performanceMonitor.endTiming(detectionId, true, { source: 'default', reason: 'gps_failed_no_ip_fallback' });
                    return this.defaultLocation;
                }
            }

            // Try IP-based location as fallback
            if (this.enableIPFallback) {
                const ipId = `${detectionId}_ip`;
                this.performanceMonitor.startTiming(ipId, 'ip');

                try {
                    const ipLocation = await this.getIPLocation();
                    if (ipLocation) {
                        const locationData = {
                            ...ipLocation,
                            timestamp: Date.now(),
                        };

                        this.performanceMonitor.endTiming(ipId, true, { provider: locationData.provider });
                        this.cacheLocation(locationData);
                        this.performanceMonitor.endTiming(detectionId, true, { source: 'ip', fromCache: false });
                        this.errorHandler.log('info', 'IP location detected successfully', {
                            city: locationData.city,
                            provider: locationData.provider,
                        });
                        return locationData;
                    }
                    this.performanceMonitor.endTiming(ipId, false, { reason: 'no_location' });
                } catch (ipError) {
                    this.performanceMonitor.endTiming(ipId, false, {
                        error: ipError.message,
                        errorType: ipError.type,
                    });
                    const errorResult = this.errorHandler.handle(ipError, 'ip');
                    this.errorHandler.log('warn', 'IP location detection failed, using default', errorResult);
                }
            }

            // Final fallback to default location
            this.performanceMonitor.endTiming(detectionId, true, { source: 'default', reason: 'all_methods_failed' });
            this.errorHandler.log('info', 'Using default location as final fallback', {
                city: this.defaultLocation.city,
            });
            return this.defaultLocation;
        } catch (error) {
            this.performanceMonitor.endTiming(detectionId, false, {
                error: error.message,
                errorType: error.type || 'GENERAL_ERROR',
            });
            const errorResult = this.errorHandler.handle(error, 'general');
            this.errorHandler.log('error', 'Complete location detection failure', errorResult);
            return this.defaultLocation;
        }
    }

    /**
     * Get GPS-based location using browser geolocation API
     * @returns {Promise<Object>} GPS location data
     */
    async getGPSLocation() {
        if (!GeolocationProvider.isAvailable()) {
            throw new Error('Geolocation not available');
        }

        const options = this.config.getGeolocationOptions();

        const position = await GeolocationProvider.getCurrentPosition(options);

        return {
            latitude: position.latitude,
            longitude: position.longitude,
            accuracy: position.accuracy,
            city: await this.getCityFromCoordinates(position.latitude, position.longitude),
        };
    }

    /**
     * Get IP-based location using IPLocationProvider
     * @returns {Promise<Object>} IP location data
     */
    async getIPLocation() {
        try {
            const ipLocation = await this.ipProvider.getLocationByIP();
            return ipLocation;
        } catch (error) {
            // Re-throw with context for error handler
            const contextualError = new Error(`IP location detection failed: ${error.message}`);
            contextualError.type = error.type || 'NETWORK_ERROR';
            contextualError.originalError = error;
            throw contextualError;
        }
    }

    /**
     * Convert coordinates to city name (placeholder for future implementation)
     * @param {number} latitude - Latitude coordinate
     * @param {number} longitude - Longitude coordinate
     * @returns {Promise<string>} City name
     */
    async getCityFromCoordinates(latitude, longitude) {
        // This is a placeholder - in a real implementation, this would use
        // reverse geocoding API to convert coordinates to city name
        // For now, return a generic location indicator
        return `Location (${latitude.toFixed(2)}, ${longitude.toFixed(2)})`;
    }

    /**
     * Retrieve cached location data
     * @returns {Object|null} Cached location data or null
     */
    getCachedLocation() {
        if (!this.enableCache) return null;
        return this.cache.get();
    }

    /**
     * Cache location data for the current session
     * @param {Object} locationData - Location data to cache
     */
    cacheLocation(locationData) {
        if (!this.enableCache) return;
        this.cache.set(locationData);
    }

    /**
     * Clear cached location data
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get default location (Hefei)
     * @returns {Object} Default location data
     */
    getDefaultLocation() {
        return { ...this.defaultLocation };
    }

    /**
     * Update service configuration
     * @param {Object} options - New configuration options
     */
    updateConfig(options) {
        this.timeout = options.timeout || this.timeout;
        this.enableIPFallback = options.enableIPFallback !== undefined ? options.enableIPFallback : this.enableIPFallback;
        this.enableCache = options.enableCache !== undefined ? options.enableCache : this.enableCache;
        this.enableHighAccuracy = options.enableHighAccuracy !== undefined ? options.enableHighAccuracy : this.enableHighAccuracy;

        // Update error handler configuration
        if (options.errorHandling) {
            this.errorHandler.updateLoggingConfig(options.errorHandling);
            if (options.errorHandling.fallbackStrategies) {
                this.errorHandler.updateFallbackStrategies(options.errorHandling.fallbackStrategies);
            }
        }

        // Update IP provider timeout
        if (options.timeout && this.ipProvider) {
            this.ipProvider.timeout = options.timeout;
        }
    }

    /**
     * Get error statistics for monitoring
     * @returns {Object} Error statistics
     */
    getErrorStats() {
        return LocationErrorHandler.getErrorStats();
    }

    /**
     * Get performance metrics and report
     * @returns {Object} Performance report with metrics and recommendations
     */
    getPerformanceReport() {
        return this.performanceMonitor.getPerformanceReport();
    }

    /**
     * Reset performance metrics
     */
    resetPerformanceMetrics() {
        this.performanceMonitor.resetMetrics();
    }

    /**
     * Clear error statistics
     */
    clearErrorStats() {
        LocationErrorHandler.clearErrorStats();
    }

    /**
     * Get user-friendly error message for UI display
     * @param {Object} error - Error object
     * @returns {string} User-friendly message
     */
    getUserFriendlyErrorMessage(error) {
        return this.errorHandler.getUserFriendlyMessage(error);
    }

    /**
     * Get current service configuration
     * @returns {Object} Current configuration
     */
    getConfiguration() {
        return {
            timeout: this.timeout,
            enableIPFallback: this.enableIPFallback,
            enableCache: this.enableCache,
            enableHighAccuracy: this.enableHighAccuracy,
            defaultLocation: this.defaultLocation,
            featureFlags: this.featureFlags.getFeatureFlagStatus(),
            runtimeConfig: this.featureFlags.getRuntimeConfig(),
        };
    }

    /**
     * Check if location detection is available and properly configured
     * @returns {Object} Availability status with details
     */
    checkAvailability() {
        const status = {
            available: false,
            reasons: [],
            capabilities: {
                gps: false,
                ip: false,
                cache: false,
            },
        };

        // Check if feature is enabled
        if (!this.featureFlags.isLocationDetectionEnabled()) {
            status.reasons.push('Location detection is disabled via configuration');
            return status;
        }

        // Check GPS capability
        if (this.featureFlags.canUseGPSDetection()) {
            status.capabilities.gps = true;
        } else {
            if (!this.featureFlags.isGeolocationSupported()) {
                status.reasons.push('Browser does not support geolocation API');
            }
            if (!this.featureFlags.isSecureContext()) {
                status.reasons.push('Geolocation requires HTTPS or localhost');
            }
        }

        // Check IP capability
        if (this.featureFlags.canUseIPDetection()) {
            status.capabilities.ip = true;
        } else {
            status.reasons.push('IP location detection not properly configured');
        }

        // Check cache capability
        if (this.featureFlags.isLocationCacheEnabled()) {
            status.capabilities.cache = true;
        }

        // Service is available if at least one detection method is available
        status.available = status.capabilities.gps || status.capabilities.ip;

        if (!status.available && status.reasons.length === 0) {
            status.reasons.push('No location detection methods are available');
        }

        return status;
    }

    /**
     * Log service configuration for debugging
     */
    logServiceConfiguration() {
        console.group('LocationDetectionService Configuration');
        console.log('Service Configuration:', {
            timeout: this.timeout,
            enableIPFallback: this.enableIPFallback,
            enableCache: this.enableCache,
            enableHighAccuracy: this.enableHighAccuracy,
        });
        console.log('Default Location:', this.defaultLocation);
        console.log('Availability:', this.checkAvailability());
        console.groupEnd();
    }

    /**
     * Reload configuration from environment variables
     * Useful for runtime configuration updates
     */
    reloadConfiguration() {
        const newConfig = new LocationConfig();
        this.config = newConfig;

        // Update service properties
        this.timeout = this.config.getTimeout('total');
        this.enableIPFallback = this.config.isIPFallbackEnabled();
        this.enableCache = this.config.isCacheEnabled();
        this.enableHighAccuracy = this.config.get('enableHighAccuracy');

        // Update default location
        const defaultCenter = this.config.get('defaultCenter');
        this.defaultLocation = {
            latitude: defaultCenter.lat,
            longitude: defaultCenter.lng,
            city: 'Default Location',
            source: 'default',
            timestamp: Date.now(),
        };

        // Update IP provider configuration
        this.ipProvider = new IPLocationProvider({
            timeout: this.config.getTimeout('ip'),
            apiKey: this.config.get('ipApiKey'),
            endpoints: this.config.getIPApiEndpoints(),
        });

        if (this.config.get('enableDebugLogging')) {
            console.log('LocationDetectionService configuration reloaded');
            this.logServiceConfiguration();
        }
    }
}

export default LocationDetectionService;
