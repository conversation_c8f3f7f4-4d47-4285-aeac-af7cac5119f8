<template>
    <div class="dashboard-container">
        <!-- Loading 遮罩层 -->
        <div v-if="loading" class="loading-overlay">
            <div class="loading-content">
                <i class="el-icon-setting loading-icon"></i>
                <p class="loading-text">数据加载中...</p>
            </div>
        </div>
        <!-- 模块导航标签 -->
        <div class="module-navigation">
            <div class="nav-tabs">
                <div v-for="module in filteredModules" :key="module.id" class="nav-tab"
                    :class="{ active: activeModule === module.id }" @click="setActiveModule(module.id)">
                    <div class="tab-icon" :style="getTabStyle(module)">
                        <i :class="module.icon"></i>
                    </div>
                    <span class="tab-text">{{ module.name }}</span>
                    <div v-if="activeModule === module.id" class="active-indicator"></div>
                </div>
            </div>
        </div>

        <!-- 仪表盘左右布局 -->
        <div class="dashboard-grid">
            <!-- 左侧面板 - 地图和趋势图表 -->
            <div class="left-panel">
                <!-- 地图面板 -->
                <div class="map-panel" :class="[currentTheme.cardClass, { 'fullscreen-map': isMapFullscreen }]">
                    <div class="panel-header">
                        <div class="header-content">
                            <div class="status-indicator" :style="{ background: currentTheme.primary }"></div>
                            <h3 class="panel-title" :style="{ color: currentTheme.textColor }">{{
                                currentModuleData.map.title }}</h3>
                        </div>
                        <div class="header-actions">
                            <div v-if="currentModuleData.map.legend" class="map-legend">
                                <div v-for="(item, index) in currentModuleData.map.legend" :key="index"
                                    class="legend-item">
                                    <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                                    <span class="legend-label">{{ item.label }}</span>
                                </div>
                            </div>
                            <!-- Location Detection UI -->
                            <LocationDetectionUI :is-detecting="locationUXState.isDetecting"
                                :detected-location="locationUXState.detectedLocation"
                                :error-message="locationUXState.errorMessage" @refresh-location="handleRefreshLocation"
                                v-if="locationUXState.showRefreshButton" />
                            <el-button type="text"
                                :icon="isMapFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'"
                                @click="toggleMapFullscreen" class="fullscreen-btn"
                                :title="isMapFullscreen ? '退出全屏' : '全屏显示'"></el-button>
                        </div>
                    </div>
                    <div id="mapContainer" class="map-content"></div>
                </div>

                <!-- 趋势图表 -->
                <div class="chart-panel" :class="currentTheme.cardClass">
                    <div class="panel-header">
                        <div class="header-content">
                            <div class="status-indicator" :style="{ background: currentTheme.primary }"></div>
                            <h3 class="panel-title" :style="{ color: currentTheme.textColor }">{{
                                currentModuleData.chart.title }}</h3>
                        </div>
                    </div>
                    <div id="trendChart" class="chart-content"></div>
                </div>
            </div>

            <!-- 右侧面板 - 统计卡片、数据表格和饼图 -->
            <div class="right-panel">
                <!-- 统计卡片 -->
                <div class="stats-section">
                    <div v-for="(stat, index) in currentModuleData.stats" :key="index" class="stat-card"
                        :class="currentTheme.cardClass" :style="getStatCardStyle(stat)">
                        <div class="stat-icon-wrapper" :style="getStatIconStyle(stat)">
                            <i :class="stat.icon" class="stat-icon"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value" :style="{ color: stat.color }">
                                <count-to :start-val="0" :end-val="stat.value" :duration="2000"
                                    :decimals="0"></count-to>
                            </div>
                            <div class="stat-label">{{ stat.label }}</div>
                        </div>
                        <div class="stat-trend" v-if="stat.trend">
                            <i :class="stat.trend > 0 ? 'el-icon-top' : 'el-icon-bottom'"
                                :style="{ color: stat.trend > 0 ? '#10b981' : '#ef4444' }"></i>
                            <span :style="{ color: stat.trend > 0 ? '#10b981' : '#ef4444' }">{{ Math.abs(stat.trend)
                                }}%</span>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-panel" :class="currentTheme.cardClass">
                    <div class="panel-header">
                        <div class="header-content">
                            <div class="status-indicator" :style="{ background: currentTheme.primary }"></div>
                            <h3 class="panel-title" :style="{ color: currentTheme.textColor }">{{
                                currentModuleData.table.title }}</h3>
                        </div>
                    </div>
                    <div class="table-content">
                        <el-table :data="currentModuleData.table.data" style="width: 100%" size="small" stripe
                            :header-cell-style="{ backgroundColor: currentTheme.headerBg, color: currentTheme.textColor }">
                            <el-table-column v-for="column in currentModuleData.table.columns" :key="column.prop"
                                :prop="column.prop" :label="column.label" :width="column.width" show-overflow-tooltip>
                                <template #default="scope" v-if="column.prop === 'status'">
                                    <el-tag :type="getStatusType(scope.row[column.prop])" size="small">
                                        {{ scope.row[column.prop] }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>

                <!-- 饼图区域 -->
                <div class="pie-charts-section">
                    <div v-for="(pie, index) in currentModuleData.pieCharts" :key="index" class="pie-chart-panel"
                        :class="currentTheme.cardClass">
                        <div class="panel-header">
                            <div class="header-content">
                                <div class="status-indicator" :style="{ background: currentTheme.primary }"></div>
                                <h3 class="panel-title" :style="{ color: currentTheme.textColor }">{{ pie.title }}</h3>
                            </div>
                        </div>
                        <div :id="'pieChart' + index" class="pie-chart-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import {
    getEquipmentData,
    getProductData,
    getScenarioData,
    getOrganizeData,
    getOMData,
    getOpenCapabilityData,
    getProductionTestData
} from '@/api/iot/home'
import LocationDetectionService from '@/utils/location/LocationDetectionService.js'
import AsyncLocationDetector from '@/utils/location/AsyncLocationDetector.js'
import MapCenterManager from '@/utils/location/MapCenterManager.js'
import LocationUXManager from '@/utils/location/LocationUXManager.js'
import LocationDetectionUI from '@/components/LocationDetectionUI.vue'
import { getCityCoordinates, getDefaultCoordinates } from '@/utils/cityCoordinates.js'

export default {
    name: 'Dashboard',
    components: {
        CountTo,
        LocationDetectionUI
    },
    data() {
        return {
            loading: false, // 加载状态
            activeModule: 'devices',
            map: null,
            deviceList: [], // 设备列表（用于聚合）
            trendChart: null,
            pieCharts: [],
            isMapFullscreen: false, // 地图全屏状态

            // Location detection services
            locationService: null,
            asyncLocationDetector: null,
            mapCenterManager: null,
            locationUXManager: null,
            locationDetectionInProgress: false,

            // UX state for location detection
            locationUXState: {
                isDetecting: false,
                detectedLocation: '',
                errorMessage: '',
                showRefreshButton: true
            },

            // 模块配置
            modules: [
                {
                    id: 'devices',
                    name: '设备',
                    icon: 'el-icon-cpu',
                    color: '#3b82f6',
                    gradient: 'linear-gradient(135deg, #3b82f6, #1d4ed8)'
                },
                {
                    id: 'products',
                    name: '产品',
                    icon: 'el-icon-box',
                    color: '#10b981',
                    gradient: 'linear-gradient(135deg, #10b981, #059669)'
                },
                {
                    id: 'scenarios',
                    name: '场景',
                    icon: 'el-icon-office-building',
                    color: '#8b5cf6',
                    gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'
                },
                {
                    id: 'personnel',
                    name: '人员',
                    icon: 'el-icon-user',
                    color: '#f59e0b',
                    gradient: 'linear-gradient(135deg, #f59e0b, #d97706)'
                },
                {
                    id: 'maintenance',
                    name: '运维',
                    icon: 'el-icon-setting',
                    color: '#ef4444',
                    gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'
                },
                {
                    id: 'openapi',
                    name: '开放能力',
                    icon: 'el-icon-connection',
                    color: '#22c55e',
                    gradient: 'linear-gradient(135deg, #22c55e, #16a34a)'
                },
                {
                    id: 'testing',
                    name: '产测',
                    icon: 'el-icon-data-analysis',
                    color: '#8b5cf6',
                    gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'
                }
            ],

            // 主题配置
            themeConfig: {
                devices: {
                    primary: '#3b82f6',
                    secondary: '#dbeafe',
                    textColor: '#1e40af',
                    cardClass: 'theme-devices',
                    headerBg: '#f8fafc'
                },
                products: {
                    primary: '#10b981',
                    secondary: '#d1fae5',
                    textColor: '#047857',
                    cardClass: 'theme-products',
                    headerBg: '#f0fdf4'
                },
                scenarios: {
                    primary: '#8b5cf6',
                    secondary: '#ede9fe',
                    textColor: '#7c3aed',
                    cardClass: 'theme-scenarios',
                    headerBg: '#faf5ff'
                },
                personnel: {
                    primary: '#f59e0b',
                    secondary: '#fef3c7',
                    textColor: '#d97706',
                    cardClass: 'theme-personnel',
                    headerBg: '#fffbeb'
                },
                maintenance: {
                    primary: '#ef4444',
                    secondary: '#fecaca',
                    textColor: '#dc2626',
                    cardClass: 'theme-maintenance',
                    headerBg: '#fef2f2'
                },
                openapi: {
                    primary: '#22c55e',
                    secondary: '#dcfce7',
                    textColor: '#16a34a',
                    cardClass: 'theme-openapi',
                    headerBg: '#f0fdf4'
                },
                testing: {
                    primary: '#8b5cf6',
                    secondary: '#ede9fe',
                    textColor: '#7c3aed',
                    cardClass: 'theme-testing',
                    headerBg: '#faf5ff'
                }
            },

            // 模块数据
            moduleData: {
                devices: {
                    map: {
                        title: '设备分布',
                        data: []
                    },
                    stats: [],
                    table: {
                        title: '最新设备状态',
                        columns: [],
                        data: []
                    },
                    chart: {
                        title: '设备接入趋势',
                        data: {
                            xData: [],
                            yData: []
                        }
                    },
                    pieCharts: []
                },
                products: {
                    map: {
                        title: '产品分布',
                        data: []
                    },
                    stats: [],
                    table: {
                        title: '产品列表',
                        columns: [],
                        data: []
                    },
                    chart: {
                        title: '产品接入趋势',
                        data: {
                            xData: [],
                            yData: []
                        }
                    },
                    pieCharts: []
                },
                scenarios: {
                    map: {
                        title: '场景分布',
                        data: []
                    },
                    stats: [],
                    table: {
                        title: '场景列表',
                        columns: [],
                        data: []
                    },
                    chart: {
                        title: '场景执行趋势',
                        data: {
                            xData: [],
                            yData: []
                        }
                    },
                    pieCharts: []
                },
                personnel: {
                    map: {
                        title: '人员分布',
                        data: []
                    },
                    stats: [],
                    table: {
                        title: '人员列表',
                        columns: [],
                        data: []
                    },
                    chart: {
                        title: '人员活跃度',
                        data: {
                            xData: [],
                            yData: []
                        }
                    },
                    pieCharts: []
                },
                maintenance: {
                    map: {
                        title: '运维分布',
                        data: []
                    },
                    stats: [],
                    table: {
                        title: '运维记录',
                        columns: [],
                        data: []
                    },
                    chart: {
                        title: '运维趋势',
                        data: {
                            xData: [],
                            yData: []
                        }
                    },
                    pieCharts: []
                },
                openapi: {
                    map: {
                        title: 'API分布',
                        data: []
                    },
                    stats: [],
                    table: {
                        title: 'API列表',
                        columns: [],
                        data: []
                    },
                    chart: {
                        title: 'API调用趋势',
                        data: {
                            xData: [],
                            yData: []
                        }
                    },
                    pieCharts: []
                },
                testing: {
                    map: {
                        title: '产测分布',
                        data: []
                    },
                    stats: [],
                    table: {
                        title: '测试记录',
                        columns: [],
                        data: []
                    },
                    chart: {
                        title: '测试通过率趋势',
                        data: {
                            xData: [],
                            yData: []
                        }
                    },
                    pieCharts: []
                }
            }
        }
    },

    computed: {
        // 过滤后的模块列表，只有admin用户才能看到后3个tab
        filteredModules() {
            const adminOnlyModules = ['maintenance', 'openapi', 'testing']
            const isAdmin = this.$store.state.user && this.$store.state.user.roles && this.$store.state.user.roles.includes('admin')

            return this.modules.filter(module => {
                // 如果是admin专用模块且用户不是admin，则过滤掉
                if (adminOnlyModules.includes(module.id) && !isAdmin) {
                    return false
                }
                return true
            })
        },

        currentModuleData() {
            return this.moduleData[this.activeModule] || this.moduleData.devices
        },

        currentTheme() {
            return this.themeConfig[this.activeModule] || this.themeConfig.devices
        }
    },

    async mounted() {
        this.loading = true
        try {
            this.initMap()
            this.initPieCharts()
            this.initTrendChart()
            await this.loadData()

            // 数据加载完成后更新图表
            this.$nextTick(() => {
                this.updateCharts()
            })

            // 添加全局的设备详情查看函数
            window.viewDeviceDetail = (deviceId) => {
                console.log('查看设备详情:', deviceId)
                // 这里可以添加跳转到设备详情页面的逻辑
                // 例如：this.$router.push(`/iot/device-detail/${deviceId}`)
            }
        } finally {
            this.loading = false
        }
    },

    beforeDestroy() {
        // 清理位置检测服务
        if (this.locationUXManager) {
            this.locationUXManager.destroy()
            this.locationUXManager = null
        }

        if (this.mapCenterManager) {
            this.mapCenterManager.destroy()
            this.mapCenterManager = null
        }

        if (this.asyncLocationDetector) {
            this.asyncLocationDetector.destroy()
            this.asyncLocationDetector = null
        }

        if (this.locationService) {
            this.locationService.clearCache()
            this.locationService = null
        }

        // 清理全局函数
        if (window.viewDeviceDetail) {
            delete window.viewDeviceDetail
        }
    },

    methods: {
        // 设置活跃模块
        async setActiveModule(moduleId) {
            this.loading = true
            try {
                this.activeModule = moduleId

                // 加载对应模块的数据
                await this.loadModuleData(moduleId)

                this.$nextTick(() => {
                    this.updateCharts()
                    this.updateMapData()

                    // 切换模块后，如果没有设备数据，重新尝试位置检测
                    const mapData = this.currentModuleData.map.data
                    if (!mapData || mapData.length === 0) {
                        this.startLocationDetection()
                    }
                })
            } finally {
                this.loading = false
            }
        },

        // 获取标签样式
        getTabStyle(module) {
            const isActive = this.activeModule === module.id
            return {
                color: isActive ? '#fff' : module.color,
                background: isActive ? module.gradient : 'transparent'
            }
        },

        // 获取统计卡片样式
        getStatCardStyle(stat) {
            return {
                borderLeft: `4px solid ${stat.color}`,
                boxShadow: `0 4px 12px ${stat.color}20`
            }
        },

        // 获取统计图标样式
        getStatIconStyle(stat) {
            return {
                backgroundColor: `${stat.color}15`,
                color: stat.color
            }
        },

        // 获取状态标签类型
        getStatusType(status) {
            const statusMap = {
                '在线': 'success',
                '离线': 'danger',
                '故障': 'warning',
                '运行中': 'success',
                '已停止': 'info',
                '通过': 'success',
                '失败': 'danger'
            }
            return statusMap[status] || 'info'
        },

        // 切换地图全屏状态
        toggleMapFullscreen() {
            this.isMapFullscreen = !this.isMapFullscreen

            // 等待DOM更新后重新调整地图大小
            this.$nextTick(() => {
                if (this.map) {
                    // 延迟一点时间确保CSS动画完成
                    setTimeout(() => {
                        this.map.checkResize()
                    }, 300)
                }
            })
        },

        // 初始化地图
        initMap() {
            try {
                console.log('初始化天地图')

                // 初始化地图对象
                this.map = new T.Map('mapContainer')

                // 初始化位置检测服务
                this.initLocationServices()

                // 根据设备点位情况设置地图中心和缩放级别（非阻塞）
                this.setInitialMapView()

                // 添加缩放控件
                this.map.addControl(new T.Control.Zoom())

                // 添加比例尺控件
                this.map.addControl(new T.Control.Scale())

                // 添加地图类型控件
                this.map.addControl(new T.Control.MapType())

                // 添加鹰眼控件
                this.map.addControl(new T.Control.OverviewMap())

                // 如果有设备，则添加设备标记
                const mapData = this.currentModuleData.map.data
                if (mapData && mapData.length > 0) {
                    this.addDeviceMarkers(mapData)
                }

                // 添加标题
                this.addMapTitle()

                // 添加地图缩放事件监听
                this.map.addEventListener('zoomend', () => {
                    const zoom = this.map.getZoom()
                    console.log('当前缩放级别:', zoom)
                    // 缩放级别变化时，重新渲染标记
                    this.updateMarkers(zoom)
                })

                // 开始位置检测（非阻塞）
                this.startLocationDetection()
            } catch (error) {
                console.error('地图初始化失败:', error)
            }
        },

        // 更新地图数据
        updateMapData() {
            if (!this.map) return

            // 清除现有标记
            this.map.clearOverLays()

            // 根据当前模块添加相应的地图标记
            const mapData = this.currentModuleData.map.data
            if (mapData && mapData.length > 0) {
                this.addDeviceMarkers(mapData)
            }

            // 重新添加标题
            this.addMapTitle()
        },

        /**
         * 设置地图的初始视图（中心点和缩放级别）
         * 现在支持位置检测，会在检测到用户位置后更新地图中心
         * 支持设备点位和用户位置的智能组合显示
         * 支持根据用户所属城市设置默认地图中心
         */
        async setInitialMapView() {
            // 获取用户城市对应的坐标，如果获取失败则使用合肥作为默认
            const userCityCenter = this.getUserCityCenter()
            const mapData = this.currentModuleData.map.data

            // 尝试获取缓存的用户位置
            let cachedUserLocation = null
            if (this.locationService) {
                try {
                    cachedUserLocation = this.locationService.getCachedLocation()
                } catch (error) {
                    console.warn('获取缓存位置失败:', error)
                }
            }

            if (mapData && mapData.length > 0) {
                try {
                    // 收集所有设备的有效点位
                    const validPoints = []

                    mapData.forEach((device) => {
                        const longitude = device.longitude || device.lng || device.lon || device.x
                        const latitude = device.latitude || device.lat || device.y

                        if (longitude && latitude) {
                            const lng = parseFloat(longitude)
                            const lat = parseFloat(latitude)

                            if (!isNaN(lng) && !isNaN(lat)) {
                                validPoints.push({
                                    lng: lng,
                                    lat: lat,
                                })
                            }
                        }
                    })

                    console.log(`找到 ${validPoints.length} 个有效点位`)

                    if (validPoints.length === 0) {
                        // 没有有效点位，尝试使用用户位置或默认中心点
                        if (cachedUserLocation) {
                            const viewportInfo = this.calculateOptimalViewport([], cachedUserLocation)
                            if (viewportInfo) {
                                await this.setMapViewFromInfo(viewportInfo, '缓存用户位置', { enableAnimation: false })
                                console.log('没有有效点位，使用缓存的用户位置')
                                return
                            }
                        }

                        // 使用用户城市中心点，位置检测完成后可能会更新
                        this.map.centerAndZoom(userCityCenter, 12)
                        console.log(`没有有效点位，默认显示${this.getUserCityName()}（等待位置检测）`)
                        return
                    }

                    // 计算包含所有点位的最佳视图
                    // 如果设备点位较少且有用户位置，可以考虑包含用户位置
                    const includeUserLocation = validPoints.length <= 3 && cachedUserLocation
                    const viewportInfo = this.calculateOptimalViewport(
                        validPoints,
                        cachedUserLocation,
                        { includeUserLocation }
                    )

                    // 设置地图视图
                    await this.setMapViewFromInfo(viewportInfo, '所有设备', { enableAnimation: false })

                    if (includeUserLocation) {
                        console.log('地图视图已包含用户位置和设备点位')
                    }
                } catch (error) {
                    console.error('计算地图视图失败:', error)
                    // 出错时使用用户城市默认视图
                    this.map.centerAndZoom(userCityCenter, 12)
                    console.log(`计算失败，默认显示${this.getUserCityName()}（等待位置检测）`)
                }
            } else {
                // 没有设备数据，优先使用用户位置
                if (cachedUserLocation) {
                    const viewportInfo = this.calculateOptimalViewport([], cachedUserLocation)
                    if (viewportInfo) {
                        await this.setMapViewFromInfo(viewportInfo, '缓存用户位置', { enableAnimation: false })
                        console.log('没有地图数据，使用缓存的用户位置')
                        return
                    }
                }

                // 使用用户城市默认视图，位置检测完成后会更新到用户位置
                this.map.centerAndZoom(userCityCenter, 12)
                console.log(`没有地图数据，默认显示${this.getUserCityName()}（等待位置检测）`)
            }
        },

        /**
         * 获取用户所属城市的中心坐标
         * @returns {T.LngLat} 天地图坐标对象
         */
        getUserCityCenter() {
            try {
                const cityName = this.getUserCityName()
                const coordinates = getCityCoordinates(cityName)

                if (coordinates) {
                    console.log(`根据用户城市"${cityName}"获取坐标:`, coordinates)
                    return new T.LngLat(coordinates[0], coordinates[1])
                } else {
                    console.warn(`未找到城市"${cityName}"的坐标，使用默认坐标`)
                    const defaultCoords = getDefaultCoordinates()
                    return new T.LngLat(defaultCoords[0], defaultCoords[1])
                }
            } catch (error) {
                console.error('获取用户城市坐标失败:', error)
                const defaultCoords = getDefaultCoordinates()
                return new T.LngLat(defaultCoords[0], defaultCoords[1])
            }
        },

        /**
         * 获取用户所属城市名称
         * @returns {string} 城市名称
         */
        getUserCityName() {
            try {
                // 从Vuex store中获取用户信息
                const userState = this.$store.state.user

                // 尝试从用户部门信息中获取城市
                if (userState && userState.dept && userState.dept.city) {
                    console.log('从store.dept获取城市:', userState.dept.city)
                    return userState.dept.city
                }

                // 尝试从完整用户信息中获取城市
                if (userState && userState.userInfo && userState.userInfo.dept && userState.userInfo.dept.city) {
                    console.log('从store.userInfo.dept获取城市:', userState.userInfo.dept.city)
                    return userState.userInfo.dept.city
                }

                // 如果没有找到城市信息，返回默认城市
                console.warn('未找到用户城市信息，使用默认城市：合肥')
                console.log('当前用户状态:', userState)
                return '合肥'
            } catch (error) {
                console.error('获取用户城市名称失败:', error)
                return '合肥'
            }
        },

        /**
         * 计算包含所有点位的最佳视图
         * 现在支持用户位置作为参考点，优化视图计算
         * @param {Array} points 点位数组，每个点包含lng和lat属性
         * @param {Object} userLocation 用户位置信息 (可选)
         * @param {Object} options 计算选项
         * @returns {Object} 包含中心点、边界和缩放级别的对象
         */
        calculateOptimalViewport(points, userLocation = null, options = {}) {
            if (!points || points.length === 0) {
                // 如果没有设备点位但有用户位置，返回用户位置视图
                if (userLocation && userLocation.longitude && userLocation.latitude) {
                    return {
                        center: {
                            lng: parseFloat(userLocation.longitude),
                            lat: parseFloat(userLocation.latitude)
                        },
                        bounds: null,
                        zoomLevel: userLocation.source === 'gps' ? 13 : 12,
                        source: 'user_location'
                    }
                }
                return null
            }

            // 计算设备点位边界
            let minLng = Number.MAX_VALUE
            let maxLng = Number.MIN_VALUE
            let minLat = Number.MAX_VALUE
            let maxLat = Number.MIN_VALUE

            points.forEach((point) => {
                minLng = Math.min(minLng, point.lng)
                maxLng = Math.max(maxLng, point.lng)
                minLat = Math.min(minLat, point.lat)
                maxLat = Math.max(maxLat, point.lat)
            })

            // 如果有用户位置，考虑将其包含在视图中
            if (userLocation && userLocation.longitude && userLocation.latitude && options.includeUserLocation) {
                const userLng = parseFloat(userLocation.longitude)
                const userLat = parseFloat(userLocation.latitude)

                if (!isNaN(userLng) && !isNaN(userLat)) {
                    minLng = Math.min(minLng, userLng)
                    maxLng = Math.max(maxLng, userLng)
                    minLat = Math.min(minLat, userLat)
                    maxLat = Math.max(maxLat, userLat)
                }
            }

            // 计算中心点
            const centerLng = (minLng + maxLng) / 2
            const centerLat = (minLat + maxLat) / 2

            // 计算跨度
            const lngDelta = maxLng - minLng
            const latDelta = maxLat - minLat

            // 为边界添加缓冲区
            const bufferFactor = options.bufferFactor || 0.2
            const bufferedMinLng = minLng - lngDelta * bufferFactor
            const bufferedMaxLng = maxLng + lngDelta * bufferFactor
            const bufferedMinLat = minLat - latDelta * bufferFactor
            const bufferedMaxLat = maxLat + latDelta * bufferFactor

            // 推算合适的缩放级别
            let zoomLevel = this.calculateZoomLevel(bufferedMinLng, bufferedMaxLng, bufferedMinLat, bufferedMaxLat)

            // 点位少时特殊调整
            if (points.length === 1) {
                zoomLevel = 13 // 单点使用更合适的缩放级别
            } else if (points.length <= 5) {
                zoomLevel = Math.min(zoomLevel, 10)
            }

            // 如果包含用户位置且设备点位较少，适当提高缩放级别
            if (userLocation && options.includeUserLocation && points.length <= 3) {
                zoomLevel = Math.min(zoomLevel + 1, 14)
            }

            // 确保缩放级别在有效范围内
            zoomLevel = Math.max(4, Math.min(16, zoomLevel))

            return {
                center: { lng: centerLng, lat: centerLat },
                bounds: {
                    minLng: bufferedMinLng,
                    maxLng: bufferedMaxLng,
                    minLat: bufferedMinLat,
                    maxLat: bufferedMaxLat,
                },
                zoomLevel: zoomLevel,
                source: 'device_points'
            }
        },

        /**
         * 根据地理范围计算合适的缩放级别
         */
        calculateZoomLevel(minLng, maxLng, minLat, maxLat) {
            // 获取地图容器尺寸
            const mapContainer = document.getElementById('mapContainer')
            if (!mapContainer) {
                return 8
            }

            const mapWidth = mapContainer.clientWidth
            const mapHeight = mapContainer.clientHeight

            // 计算经纬度范围
            const lngSpan = maxLng - minLng
            const latSpan = maxLat - minLat

            if (lngSpan <= 0 || latSpan <= 0) {
                return 8
            }

            // 根据经度跨度计算缩放级别
            const zoomLng = Math.log2(360 / lngSpan) + Math.log2(mapWidth / 256) - 0.5

            // 根据纬度跨度计算缩放级别
            const latRadian = (latSpan * Math.PI) / 180
            const zoomLat = Math.log2((2 * Math.PI) / latRadian) + Math.log2(mapHeight / 256) - 0.5

            // 取较小的缩放级别
            let zoom = Math.min(zoomLng, zoomLat)
            zoom = Math.floor(zoom)

            if (isNaN(zoom) || zoom < 0 || zoom > 18) {
                zoom = 8
            }

            return zoom
        },

        /**
         * 根据视口信息设置地图视图
         * 现在支持位置检测的平滑过渡和动画效果
         * @param {Object} viewportInfo 视口信息
         * @param {string} sourceDesc 来源描述
         * @param {Object} options 设置选项
         */
        async setMapViewFromInfo(viewportInfo, sourceDesc, options = {}) {
            if (!viewportInfo) {
                console.error('视口信息无效')
                return false
            }

            const { center, zoomLevel } = viewportInfo
            const mapCenter = new T.LngLat(center.lng, center.lat)

            try {
                // 检查是否需要使用平滑过渡
                const useAnimation = options.enableAnimation !== false && this.mapCenterManager

                if (useAnimation && viewportInfo.source !== 'device_points') {
                    // 对于用户位置检测，使用MapCenterManager的平滑过渡
                    const success = await this.mapCenterManager.animateToCenter(
                        { lng: center.lng, lat: center.lat },
                        zoomLevel,
                        { enableAnimation: true }
                    )

                    if (success) {
                        console.log(`已平滑设置地图视图 (${sourceDesc}):`, {
                            center: center,
                            zoomLevel: zoomLevel,
                        })
                        return true
                    }
                }

                // 直接设置地图视图（设备点位或动画失败时）
                this.map.centerAndZoom(mapCenter, zoomLevel)
                console.log(`已设置地图视图 (${sourceDesc}):`, {
                    center: center,
                    zoomLevel: zoomLevel,
                })
                return true
            } catch (error) {
                console.error('设置地图视图失败:', error)
                // 降级处理
                this.map.centerAndZoom(mapCenter, zoomLevel)
                return false
            }
        },

        /**
         * 初始化位置检测服务
         */
        initLocationServices() {
            try {
                // 初始化基础位置检测服务
                this.locationService = new LocationDetectionService({
                    timeout: 10000,
                    enableIPFallback: true,
                    enableCache: true,
                    enableHighAccuracy: false,
                    enablePerformanceMonitoring: true
                })

                // 初始化优化的异步位置检测器
                this.asyncLocationDetector = new AsyncLocationDetector({
                    timeout: 10000,
                    enableIPFallback: true,
                    enableCache: true,
                    enableHighAccuracy: false,
                    enablePerformanceMonitoring: true,
                    enableSmartCaching: true,
                    enableTimeoutOptimization: true,
                    maxConcurrentDetections: 1
                })

                // 初始化地图中心管理器
                this.mapCenterManager = new MapCenterManager(this.map, {
                    animationDuration: 1000,
                    defaultZoomLevel: 12,
                    enableAnimation: true
                })

                // 初始化位置检测UX管理器
                this.locationUXManager = new LocationUXManager({
                    locationService: this.locationService,
                    maxRetryAttempts: 2,
                    retryDelay: 3000,
                    enableNotifications: true,
                    enableRetry: true,
                    showLoadingIndicator: true,
                    onDetectionStart: this.handleLocationDetectionStart,
                    onDetectionSuccess: this.handleLocationDetectionSuccess,
                    onDetectionError: this.handleLocationDetectionError,
                    onDetectionComplete: this.handleLocationDetectionComplete
                })

                console.log('位置检测服务初始化完成')
            } catch (error) {
                console.error('位置检测服务初始化失败:', error)
            }
        },

        /**
         * 开始位置检测（非阻塞）
         */
        async startLocationDetection() {
            if (!this.locationUXManager || !this.mapCenterManager) {
                console.warn('位置检测服务未初始化')
                return
            }

            if (this.locationDetectionInProgress) {
                console.log('位置检测已在进行中')
                return
            }

            try {
                // 使用UX管理器进行位置检测
                const locationResult = await this.locationUXManager.detectLocationWithUX({
                    priority: 'background',
                    enableAnimation: true
                })

                if (locationResult && locationResult.latitude && locationResult.longitude) {
                    // 更新地图中心
                    await this.updateMapCenterFromLocation(locationResult)
                }
            } catch (error) {
                console.error('位置检测失败:', error)
                // Error is already handled by UX manager
            }
        },

        /**
         * 根据检测到的位置更新地图中心
         * 现在支持与设备标记的智能协调和平滑过渡
         */
        async updateMapCenterFromLocation(locationData) {
            if (!this.mapCenterManager || !locationData) {
                return
            }

            try {
                const mapData = this.currentModuleData.map.data
                const hasDeviceData = mapData && mapData.length > 0

                if (hasDeviceData) {
                    // 有设备数据时的智能处理
                    const validPoints = []
                    mapData.forEach((device) => {
                        const longitude = device.longitude || device.lng || device.lon || device.x
                        const latitude = device.latitude || device.lat || device.y

                        if (longitude && latitude) {
                            const lng = parseFloat(longitude)
                            const lat = parseFloat(latitude)

                            if (!isNaN(lng) && !isNaN(lat)) {
                                validPoints.push({ lng, lat })
                            }
                        }
                    })

                    // 如果设备点位较少，考虑重新计算视图以包含用户位置
                    if (validPoints.length <= 3 && validPoints.length > 0) {
                        console.log('设备点位较少，重新计算视图以包含用户位置')

                        const viewportInfo = this.calculateOptimalViewport(
                            validPoints,
                            locationData,
                            { includeUserLocation: true, bufferFactor: 0.3 }
                        )

                        if (viewportInfo) {
                            await this.setMapViewFromInfo(viewportInfo, '设备+用户位置', { enableAnimation: true })
                            console.log(`地图视图已更新，包含设备和用户位置: ${locationData.city || '检测位置'} (${locationData.source})`)
                            return
                        }
                    } else if (validPoints.length > 3) {
                        console.log('设备点位较多，保持当前设备分布视图')
                        return
                    }
                }

                // 没有设备数据或设备数据无效时，更新到用户位置
                const success = await this.mapCenterManager.updateCenterFromLocation(locationData, {
                    enableAnimation: true
                })

                if (success) {
                    console.log(`地图中心已更新到用户位置: ${locationData.city || '检测位置'} (${locationData.source})`)
                } else {
                    console.log('地图中心更新被跳过（用户可能正在交互）')
                }
            } catch (error) {
                console.error('更新地图中心失败:', error)
            }
        },

        /**
         * 处理默认位置和检测位置之间的平滑过渡
         * @param {Object} fromLocation 起始位置 (可选)
         * @param {Object} toLocation 目标位置
         * @param {Object} options 过渡选项
         */
        async handleLocationTransition(fromLocation, toLocation, options = {}) {
            if (!this.mapCenterManager || !toLocation) {
                return false
            }

            try {
                const transitionOptions = {
                    enableAnimation: true,
                    animationDuration: 1500,
                    ...options
                }

                // 如果没有起始位置，直接设置到目标位置
                if (!fromLocation) {
                    return await this.mapCenterManager.updateCenterFromLocation(toLocation, transitionOptions)
                }

                // 检查距离，如果太远则分步过渡
                const distance = this.calculateDistance(fromLocation, toLocation)

                if (distance > 500) { // 距离超过500km，使用分步过渡
                    console.log('距离较远，使用分步过渡')

                    // 先缩小视图
                    const currentCenter = this.mapCenterManager.getCurrentCenter()
                    if (currentCenter && currentCenter.zoom > 8) {
                        await this.mapCenterManager.animateToCenter(
                            { lng: currentCenter.lng, lat: currentCenter.lat },
                            8,
                            { enableAnimation: true }
                        )
                        // 等待缩放动画完成
                        await new Promise(resolve => setTimeout(resolve, 800))
                    }
                }

                // 执行主要的位置过渡
                const success = await this.mapCenterManager.updateCenterFromLocation(toLocation, transitionOptions)

                if (success) {
                    console.log(`位置过渡完成: ${fromLocation?.city || '默认位置'} -> ${toLocation.city || '检测位置'}`)
                }

                return success
            } catch (error) {
                console.error('位置过渡失败:', error)
                return false
            }
        },

        /**
         * 计算两个位置之间的距离（公里）
         * @param {Object} loc1 位置1
         * @param {Object} loc2 位置2
         * @returns {number} 距离（公里）
         */
        calculateDistance(loc1, loc2) {
            if (!loc1 || !loc2 || !loc1.longitude || !loc1.latitude || !loc2.longitude || !loc2.latitude) {
                return 0
            }

            const lat1 = parseFloat(loc1.latitude)
            const lng1 = parseFloat(loc1.longitude)
            const lat2 = parseFloat(loc2.latitude)
            const lng2 = parseFloat(loc2.longitude)

            const R = 6371 // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180
            const dLng = (lng2 - lng1) * Math.PI / 180
            const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng / 2) * Math.sin(dLng / 2)
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

            return R * c
        },

        /** 添加设备标记 */
        addDeviceMarkers(data) {
            try {
                console.log('添加设备标记，数据长度:', data.length)
                this.deviceList = data // 保存设备列表用于聚合

                // 记录当前缩放级别
                const zoom = this.map.getZoom()

                // 根据缩放级别添加合适的标记
                this.updateMarkers(zoom)
            } catch (error) {
                console.error('添加设备标记失败:', error)
            }
        },

        /**
         * 根据缩放级别更新标记
         */
        updateMarkers(zoom) {
            try {
                // 清除所有现有标记
                this.map.clearOverLays()

                // 在高缩放级别（大于等于12）时显示所有点位
                // 在低缩放级别（小于12）时使用聚合
                if (zoom >= 12) {
                    this.addAllMarkers()
                } else {
                    this.addClusteredMarkers()
                }

                // 重新添加标题
                this.addMapTitle()
            } catch (error) {
                console.error('更新标记失败:', error)
            }
        },

        /**
         * 创建设备状态的SVG图标
         */
        createDeviceSvgIcon(device) {
            try {
                // 判断设备是否在线
                let isOnline = device.status === 3

                // 对于场景、人员、开放能力、产测模块，强制显示为在线状态（绿色）
                if (['scenarios', 'personnel', 'openapi', 'testing'].includes(this.activeModule)) {
                    isOnline = true
                }

                // 设置颜色
                const fillColor = isOnline ? '#4CD964' : '#8E8E93' // 在线绿色，离线灰色
                const pulseColor = isOnline ? 'rgba(76, 217, 100, 0.8)' : 'rgba(142, 142, 147, 0.8)'

                // 创建SVG
                let svg = `
                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                    <!-- 脉动效果-外环 -->
                    <circle cx="16" cy="16" r="14" fill="none" stroke="${pulseColor}" stroke-width="${isOnline ? 1.5 : 1}">
                        <animate attributeName="r" values="9;14" dur="1.5s" repeatCount="indefinite" />
                        <animate attributeName="stroke-opacity" values="1;0" dur="1.5s" repeatCount="indefinite" />
                    </circle>

                    <!-- 脉动效果-中环 -->
                    <circle cx="16" cy="16" r="7" fill="none" stroke="${pulseColor}" stroke-width="${isOnline ? 2 : 1.2}">
                        <animate attributeName="r" values="6;11" dur="1.5s" repeatCount="indefinite" />
                        <animate attributeName="stroke-opacity" values="0.8;0.1" dur="1.5s" repeatCount="indefinite" />
                    </circle>

                    <!-- 设备点主体 -->
                    <circle cx="16" cy="16" r="6" fill="${fillColor}" stroke="${isOnline ? '#4CD964' : '#E5E5EA'}" stroke-width="1.5">
                        ${isOnline ? '<animate attributeName="r" values="5;6;5" dur="2s" repeatCount="indefinite" />' : ''}
                    </circle>

                    <!-- 设备点内部 -->
                    <circle cx="16" cy="16" r="3" fill="${isOnline ? '#fff' : '#D1D1D6'}" opacity="${isOnline ? '0.9' : '0.7'}" />
                </svg>`

                // 转换为Data URL
                return 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg)
            } catch (error) {
                console.error('创建设备SVG图标失败:', error)
                return "data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='6' fill='%23999'/%3E%3C/svg%3E"
            }
        },

        /**
         * 创建设备标记
         */
        createDeviceMarker(device, lnglat) {
            try {
                // 获取设备SVG图标的Data URL
                const iconUrl = this.createDeviceSvgIcon(device)

                // 创建图标对象
                const icon = new T.Icon({
                    iconUrl: iconUrl,
                    iconSize: new T.Point(32, 32),
                    iconAnchor: new T.Point(16, 16),
                })

                // 创建标记
                const marker = new T.Marker(lnglat, { icon: icon })

                // 添加点击事件
                marker.addEventListener('click', () => {
                    console.log('标记被点击:', device)
                    // 根据当前模块类型显示不同的信息窗口
                    if (this.activeModule === 'scenarios') {
                        this.showScenarioInfoWindow(device, lnglat)
                    } else if (this.activeModule === 'personnel') {
                        this.showPersonnelInfoWindow(device, lnglat)
                    } else if (this.activeModule === 'openapi') {
                        this.showOpenCapabilityInfoWindow(device, lnglat)
                    } else if (this.activeModule === 'testing') {
                        this.showTestingInfoWindow(device, lnglat)
                    } else {
                        this.showDeviceInfoWindow(device, lnglat)
                    }
                })

                return marker
            } catch (error) {
                console.error('创建设备标记失败:', error)
                return null
            }
        },

        /**
         * 添加所有标记点
         */
        addAllMarkers() {
            try {
                console.log('添加所有标记点')
                let validMarkers = 0
                const mapData = this.currentModuleData.map.data

                if (!mapData || !Array.isArray(mapData)) {
                    console.warn('地图数据无效')
                    return
                }

                mapData.forEach((device) => {
                    const longitude = device.longitude || device.lng || device.lon || device.x
                    const latitude = device.latitude || device.lat || device.y

                    if (!longitude || !latitude) {
                        return
                    }

                    const marker = this.createDeviceMarker(device, new T.LngLat(longitude, latitude))
                    if (marker) {
                        this.map.addOverLay(marker)
                        validMarkers++
                    }
                })

                console.log(`成功添加 ${validMarkers} 个标记点`)
            } catch (error) {
                console.error('添加所有标记失败:', error)
            }
        },

        /**
         * 添加聚合标记
         */
        addClusteredMarkers() {
            try {
                console.log('添加聚合标记')
                const mapData = this.currentModuleData.map.data

                if (!mapData || !Array.isArray(mapData)) {
                    console.warn('地图数据无效')
                    return
                }

                // 根据当前缩放级别动态调整聚合距离阈值（千米）
                const zoom = this.map.getZoom()
                const distanceThreshold = 2 * Math.pow(1.5, 8 - zoom)

                // 按距离聚合
                const clusters = []

                mapData.forEach((device) => {
                    const longitude = device.longitude || device.lng || device.lon || device.x
                    const latitude = device.latitude || device.lat || device.y

                    if (!longitude || !latitude) {
                        return
                    }

                    const lng = parseFloat(longitude)
                    const lat = parseFloat(latitude)

                    if (isNaN(lng) || isNaN(lat)) {
                        return
                    }

                    const devicePoint = {
                        lng: lng,
                        lat: lat,
                        device: device,
                    }

                    // 检查是否可以加入现有聚合
                    let addedToCluster = false

                    for (const cluster of clusters) {
                        const centerPoint = cluster.center
                        const distance = this.calculateDistance(centerPoint.lng, centerPoint.lat, devicePoint.lng, devicePoint.lat)

                        if (distance < distanceThreshold) {
                            // 加入现有聚合
                            cluster.points.push(devicePoint)
                            // 重新计算中心点
                            const totalPoints = cluster.points.length
                            cluster.center = {
                                lng: (cluster.center.lng * (totalPoints - 1) + devicePoint.lng) / totalPoints,
                                lat: (cluster.center.lat * (totalPoints - 1) + devicePoint.lat) / totalPoints,
                            }
                            addedToCluster = true
                            break
                        }
                    }

                    // 如果没有加入任何聚合，则创建新的聚合
                    if (!addedToCluster) {
                        clusters.push({
                            center: {
                                lng: devicePoint.lng,
                                lat: devicePoint.lat,
                            },
                            points: [devicePoint],
                        })
                    }
                })

                console.log(`创建了 ${clusters.length} 个聚合点`)

                // 添加聚合点或单个点到地图
                clusters.forEach((cluster, index) => {
                    const lnglat = new T.LngLat(cluster.center.lng, cluster.center.lat)

                    if (cluster.points.length > 1) {
                        // 创建聚合标记
                        const canvas = document.createElement('canvas')
                        const size = 44
                        canvas.width = size
                        canvas.height = size
                        const ctx = canvas.getContext('2d')

                        // 添加阴影效果
                        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
                        ctx.shadowBlur = 5
                        ctx.shadowOffsetX = 0
                        ctx.shadowOffsetY = 2

                        // 设置聚合点颜色
                        const fillColor = '#1890ff'

                        // 绘制聚合点背景
                        ctx.beginPath()
                        ctx.arc(size / 2, size / 2, 20, 0, 2 * Math.PI)
                        ctx.fillStyle = fillColor
                        ctx.fill()

                        // 内圈高光效果
                        ctx.beginPath()
                        ctx.arc(size / 2, size / 2 - 3, 8, 0, 2 * Math.PI)
                        const gradient = ctx.createRadialGradient(size / 2, size / 2 - 3, 0, size / 2, size / 2 - 3, 8)
                        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)')
                        gradient.addColorStop(1, 'rgba(24, 144, 255, 0)')
                        ctx.fillStyle = gradient
                        ctx.fill()

                        // 添加聚合数字
                        ctx.shadowColor = 'rgba(0, 0, 0, 0)'
                        ctx.fillStyle = '#FFFFFF'
                        ctx.font = 'bold 14px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
                        ctx.textAlign = 'center'
                        ctx.textBaseline = 'middle'
                        ctx.fillText(cluster.points.length.toString(), size / 2, size / 2)

                        // 创建图标
                        const icon = new T.Icon({
                            iconUrl: canvas.toDataURL(),
                            iconSize: new T.Point(size, size),
                            iconAnchor: new T.Point(size / 2, size / 2),
                        })

                        // 创建标记
                        const marker = new T.Marker(lnglat, { icon: icon })

                        // 添加点击事件
                        marker.addEventListener('click', () => {
                            // 切换到显示所有点的级别
                            this.map.setZoom(12)
                            this.map.panTo(lnglat)
                        })

                        // 添加到地图
                        this.map.addOverLay(marker)
                    } else if (cluster.points.length === 1) {
                        // 创建单个设备标记
                        const device = cluster.points[0].device
                        const marker = this.createDeviceMarker(device, lnglat)
                        if (marker) {
                            this.map.addOverLay(marker)
                        }
                    }
                })
            } catch (error) {
                console.error('添加聚合标记失败:', error)
            }
        },

        /**
         * 计算两点之间的距离（单位：千米）
         */
        calculateDistance(lng1, lat1, lng2, lat2) {
            const radLat1 = (lat1 * Math.PI) / 180.0
            const radLat2 = (lat2 * Math.PI) / 180.0
            const a = radLat1 - radLat2
            const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
            let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)))
            s = s * 6371.0 // 地球平均半径
            return s
        },

        /**
         * 添加地图标题
         */
        addMapTitle() {
            try {
                const mapData = this.currentModuleData.map.data || []

                // 根据当前模块生成不同的标题
                let titleText = ''
                switch (this.activeModule) {
                    case 'devices':
                        titleText = `设备分布（设备总数 ${mapData.length}）`
                        break
                    case 'products':
                        titleText = `产品分布（产品设备总数 ${mapData.length}）`
                        break
                    case 'scenarios':
                        titleText = `场景分布（场景总数 ${mapData.length}）`
                        break
                    case 'personnel':
                        titleText = `人员组织分布（人员组织总数 ${mapData.length}）`
                        break
                    case 'maintenance':
                        titleText = `运维设备分布（运维设备总数 ${mapData.length}）`
                        break
                    case 'openapi':
                    case 'testing':
                        // 开放能力和产测不显示标题，只清除已存在的标题
                        titleText = null
                        break
                    default:
                        titleText = `设备分布（设备总数 ${mapData.length}）`
                }

                // 获取地图容器
                const mapContainer = document.getElementById('mapContainer')
                if (mapContainer) {
                    // 移除所有已存在的地图标题（使用更精确的选择器）
                    const existingTitles = mapContainer.querySelectorAll('.map-title, div[style*="position:absolute"][style*="left:50%"][style*="top:10px"]')
                    existingTitles.forEach(title => title.remove())

                    // 如果有标题文本，则创建并添加标题
                    if (titleText) {
                        const titleDiv = document.createElement('div')
                        titleDiv.className = 'map-title'
                        titleDiv.style.cssText = 'position:absolute;left:50%;top:10px;transform:translateX(-50%);background:white;padding:5px;border-radius:3px;z-index:1000;font-weight:bold;font-size:18px;'
                        titleDiv.innerHTML = titleText
                        mapContainer.appendChild(titleDiv)
                    }
                }
            } catch (error) {
                console.error('添加标题控件失败:', error)
            }
        },

        /**
         * 显示设备信息窗口
         */
        showDeviceInfoWindow(device, lnglat) {
            try {
                // 创建信息窗口内容
                const content = this.createDeviceInfoContent(device)

                // 创建信息窗口
                const infoWindow = new T.InfoWindow(content, {
                    offset: new T.Point(0, -16),
                    closeButton: true,
                    autoPan: true,
                    width: 280,
                    minWidth: 240,
                    maxWidth: 360,
                })

                // 打开信息窗口
                this.map.openInfoWindow(infoWindow, lnglat)
            } catch (error) {
                console.error('显示设备信息窗口失败:', error)
            }
        },

        /**
         * 显示场景信息窗口
         */
        showScenarioInfoWindow(scenario, lnglat) {
            try {
                // 创建场景信息窗口内容
                const content = this.createScenarioInfoContent(scenario)

                // 创建信息窗口
                const infoWindow = new T.InfoWindow(content, {
                    offset: new T.Point(0, -16),
                    closeButton: true,
                    autoPan: true,
                    width: 320,
                    minWidth: 280,
                    maxWidth: 400,
                })

                // 打开信息窗口
                this.map.openInfoWindow(infoWindow, lnglat)
            } catch (error) {
                console.error('显示场景信息窗口失败:', error)
            }
        },

        /**
         * 创建场景信息内容
         */
        createScenarioInfoContent(scenario) {
            // 格式化时间
            const formatTime = (timeStr) => {
                if (!timeStr) return '未知'
                try {
                    const date = new Date(timeStr)
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    })
                } catch (error) {
                    return timeStr
                }
            }

            // 获取完整地址
            const getFullAddress = () => {
                const { province, city, district } = scenario
                const parts = [province, city, district].filter(Boolean)
                return parts.length > 0 ? parts.join(' ') : '未知'
            }

            return `
            <div class="scenario-info-window">
                <div class="scenario-header">
                    <h3 class="scenario-name">${scenario.scenarioName || '未命名场景'}</h3>
                    <div class="scenario-type">${scenario.typeName || '未知类型'}</div>
                </div>
                <div class="scenario-detail">
                    <div class="detail-row">
                        <span class="label">场景ID:</span>
                        <span class="value">${scenario.scenarioId || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">场景类型:</span>
                        <span class="value">${scenario.typeName || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">模板ID:</span>
                        <span class="value">${scenario.templateId || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">所属机构:</span>
                        <span class="value">${scenario.tenantName || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">地址信息:</span>
                        <span class="value">${getFullAddress()}</span>
                    </div>
                    ${scenario.address ? `
                    <div class="detail-row">
                        <span class="label">详细地址:</span>
                        <span class="value">${scenario.address}</span>
                    </div>` : ''}
                    <div class="detail-row">
                        <span class="label">创建时间:</span>
                        <span class="value">${formatTime(scenario.createTime)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">更新时间:</span>
                        <span class="value">${formatTime(scenario.updateTime)}</span>
                    </div>
                    ${scenario.remark ? `
                    <div class="detail-row">
                        <span class="label">备注:</span>
                        <span class="value remark">${scenario.remark}</span>
                    </div>` : ''}
                </div>
                <style>
                    .scenario-info-window {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                        padding: 8px;
                        min-width: 280px;
                        max-width: 360px;
                        background: #fff;
                    }
                    .scenario-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid #e8e8e8;
                        padding-bottom: 10px;
                        margin-bottom: 12px;
                    }
                    .scenario-name {
                        margin: 0;
                        font-size: 16px;
                        font-weight: 600;
                        color: #1a1a1a;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 180px;
                    }
                    .scenario-type {
                        padding: 4px 10px;
                        border-radius: 12px;
                        font-size: 12px;
                        font-weight: 500;
                        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                        color: white;
                        box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
                    }
                    .scenario-detail {
                        margin: 12px 0;
                    }
                    .detail-row {
                        display: flex;
                        margin-bottom: 8px;
                        align-items: flex-start;
                    }
                    .label {
                        font-size: 13px;
                        color: #666;
                        font-weight: 500;
                        min-width: 80px;
                        flex-shrink: 0;
                        margin-right: 8px;
                    }
                    .value {
                        font-size: 13px;
                        color: #333;
                        line-height: 1.4;
                        word-break: break-all;
                        flex: 1;
                    }
                    .value.remark {
                        background: #f8f9fa;
                        padding: 6px 8px;
                        border-radius: 4px;
                        border-left: 3px solid #8b5cf6;
                        white-space: pre-wrap;
                    }
                </style>
            </div>`
        },

        /**
         * 显示人员组织信息窗口
         */
        showPersonnelInfoWindow(personnel, lnglat) {
            try {
                // 创建人员组织信息窗口内容
                const content = this.createPersonnelInfoContent(personnel)

                // 创建信息窗口
                const infoWindow = new T.InfoWindow(content, {
                    offset: new T.Point(0, -16),
                    closeButton: true,
                    autoPan: true,
                    width: 320,
                    minWidth: 280,
                    maxWidth: 400,
                })

                // 打开信息窗口
                this.map.openInfoWindow(infoWindow, lnglat)
            } catch (error) {
                console.error('显示人员组织信息窗口失败:', error)
            }
        },

        /**
         * 创建人员组织信息内容
         */
        createPersonnelInfoContent(personnel) {
            // 格式化时间
            const formatTime = (timeStr) => {
                if (!timeStr) return '未知'
                try {
                    const date = new Date(timeStr)
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    })
                } catch (error) {
                    return timeStr
                }
            }

            // 获取完整地址
            const getFullAddress = () => {
                const { province, city, district } = personnel
                const parts = [province, city, district].filter(Boolean)
                return parts.length > 0 ? parts.join(' ') : '未知'
            }

            // 获取部门类型文本
            const getDeptTypeText = (type) => {
                switch (type) {
                    case 1: return '公司'
                    case 2: return '部门'
                    default: return '未知类型'
                }
            }

            // 获取状态文本
            const getStatusText = (status) => {
                return status === '0' ? '正常' : '停用'
            }

            return `
            <div class="personnel-info-window">
                <div class="personnel-header">
                    <h3 class="personnel-name">${personnel.deptName || '未命名组织'}</h3>
                    <div class="personnel-type">${getDeptTypeText(personnel.deptType)}</div>
                </div>
                <div class="personnel-detail">
                    <div class="detail-row">
                        <span class="label">组织ID:</span>
                        <span class="value">${personnel.deptId || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">组织名称:</span>
                        <span class="value">${personnel.deptName || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">组织类型:</span>
                        <span class="value">${getDeptTypeText(personnel.deptType)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">负责人:</span>
                        <span class="value">${personnel.leader || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">联系电话:</span>
                        <span class="value">${personnel.phone || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">地址信息:</span>
                        <span class="value">${getFullAddress()}</span>
                    </div>
                    ${personnel.address ? `
                    <div class="detail-row">
                        <span class="label">详细地址:</span>
                        <span class="value">${personnel.address}</span>
                    </div>` : ''}
                    <div class="detail-row">
                        <span class="label">状态:</span>
                        <span class="value">${getStatusText(personnel.status)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">创建时间:</span>
                        <span class="value">${formatTime(personnel.createTime)}</span>
                    </div>
                    ${personnel.remark ? `
                    <div class="detail-row">
                        <span class="label">备注:</span>
                        <span class="value remark">${personnel.remark}</span>
                    </div>` : ''}
                </div>
                <style>
                    .personnel-info-window {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                        padding: 8px;
                        min-width: 280px;
                        max-width: 360px;
                        background: #fff;
                    }
                    .personnel-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid #e8e8e8;
                        padding-bottom: 10px;
                        margin-bottom: 12px;
                    }
                    .personnel-name {
                        margin: 0;
                        font-size: 16px;
                        font-weight: 600;
                        color: #1a1a1a;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 180px;
                    }
                    .personnel-type {
                        padding: 4px 10px;
                        border-radius: 12px;
                        font-size: 12px;
                        font-weight: 500;
                        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                        color: white;
                        box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
                    }
                    .personnel-detail {
                        margin: 12px 0;
                    }
                    .detail-row {
                        display: flex;
                        margin-bottom: 8px;
                        align-items: flex-start;
                    }
                    .label {
                        font-size: 13px;
                        color: #666;
                        font-weight: 500;
                        min-width: 80px;
                        flex-shrink: 0;
                        margin-right: 8px;
                    }
                    .value {
                        font-size: 13px;
                        color: #333;
                        line-height: 1.4;
                        word-break: break-all;
                        flex: 1;
                    }
                    .value.remark {
                        background: #f8f9fa;
                        padding: 6px 8px;
                        border-radius: 4px;
                        border-left: 3px solid #f59e0b;
                        white-space: pre-wrap;
                    }
                </style>
            </div>`
        },

        /**
         * 创建设备信息内容
         */
        createDeviceInfoContent(device) {
            // 判断设备是否在线
            const isOnline = device.status === 3
            const statusClass = isOnline ? 'online-status' : 'offline-status'
            const statusText = isOnline ? '在线' : '离线'

            return `
            <div class="device-info-window">
                <div class="device-header">
                    <h3 class="device-name">${device.deviceName || '未命名设备'}</h3>
                    <div class="device-status ${statusClass}">${statusText}</div>
                </div>
                <div class="device-detail">
                    <p><strong>设备编号:</strong> ${device.serialNumber || '未知'}</p>
                    <p><strong>设备名称:</strong> ${device.deviceName || '未知'}</p>
                    <p><strong>产品名称:</strong> ${device.productName || '未知'}</p>
                    <p><strong>联网地址:</strong> ${device.networkAddress || '未知'}</p>
                    <p><strong>经度:</strong> ${device.longitude || device.lng || device.lon || device.x || '未知'}</p>
                    <p><strong>纬度:</strong> ${device.latitude || device.lat || device.y || '未知'}</p>
                    <p><strong>运行状态:</strong> ${statusText}</p>
                    <p><strong>更新时间:</strong> ${device.activeTime || '未知'}</p>
                </div>
                <div class="device-footer">
                    <button style="display:none" class="detail-btn" onclick="window.viewDeviceDetail('${device.id || device.deviceId}')">查看详情</button>
                </div>
                <style>
                    .device-info-window {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                        padding: 5px;
                    }
                    .device-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 8px;
                        margin-bottom: 8px;
                    }
                    .device-name {
                        margin: 0;
                        font-size: 16px;
                        color: #333;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 180px;
                    }
                    .device-status {
                        padding: 2px 8px;
                        border-radius: 12px;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    .online-status {
                        background-color: #f6ffed;
                        color: #52c41a;
                        border: 1px solid #b7eb8f;
                    }
                    .offline-status {
                        background-color: #fff2f0;
                        color: #ff4d4f;
                        border: 1px solid #ffccc7;
                    }
                    .device-detail {
                        margin: 8px 0;
                    }
                    .device-detail p {
                        margin: 4px 0;
                        font-size: 13px;
                        line-height: 1.4;
                    }
                    .device-footer {
                        text-align: center;
                        margin-top: 8px;
                        padding-top: 8px;
                        border-top: 1px solid #eee;
                    }
                    .detail-btn {
                        background-color: #1890ff;
                        color: white;
                        border: none;
                        padding: 6px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    }
                    .detail-btn:hover {
                        background-color: #40a9ff;
                    }
                </style>
            </div>`
        },

        /**
         * 显示开放能力信息窗口
         */
        showOpenCapabilityInfoWindow(capability, lnglat) {
            try {
                // 创建开放能力信息窗口内容
                const content = this.createOpenCapabilityInfoContent(capability)

                // 创建信息窗口
                const infoWindow = new T.InfoWindow(content, {
                    offset: new T.Point(0, -16),
                    closeButton: true,
                    autoPan: true,
                    width: 320,
                    minWidth: 280,
                    maxWidth: 400,
                })

                // 打开信息窗口
                this.map.openInfoWindow(infoWindow, lnglat)
            } catch (error) {
                console.error('显示开放能力信息窗口失败:', error)
            }
        },

        /**
         * 创建开放能力信息内容
         */
        createOpenCapabilityInfoContent(capability) {
            // 格式化时间
            const formatTime = (timeStr) => {
                if (!timeStr) return '未知'
                try {
                    const date = new Date(timeStr)
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    })
                } catch (error) {
                    return timeStr
                }
            }

            // 获取完整地址
            const getFullAddress = () => {
                const { province, city, district } = capability
                const parts = [province, city, district].filter(Boolean)
                return parts.length > 0 ? parts.join(' ') : '未知'
            }

            // 获取部门类型文本
            const getDeptTypeText = (type) => {
                switch (type) {
                    case 1: return '公司'
                    case 2: return '部门'
                    default: return '未知类型'
                }
            }

            // 获取状态文本
            const getStatusText = (status) => {
                return status === '0' ? '正常' : '停用'
            }

            return `
            <div class="capability-info-window">
                <div class="capability-header">
                    <h3 class="capability-name">${capability.deptName || '未命名机构'}</h3>
                    <div class="capability-type">${getDeptTypeText(capability.deptType)}</div>
                </div>
                <div class="capability-detail">
                    <div class="detail-row">
                        <span class="label">机构ID:</span>
                        <span class="value">${capability.deptId || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">机构名称:</span>
                        <span class="value">${capability.deptName || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">机构类型:</span>
                        <span class="value">${getDeptTypeText(capability.deptType)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">负责人:</span>
                        <span class="value">${capability.leader || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">联系电话:</span>
                        <span class="value">${capability.phone || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">邮箱地址:</span>
                        <span class="value">${capability.email || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">地址信息:</span>
                        <span class="value">${getFullAddress()}</span>
                    </div>
                    ${capability.address ? `
                    <div class="detail-row">
                        <span class="label">详细地址:</span>
                        <span class="value">${capability.address}</span>
                    </div>` : ''}
                    <div class="detail-row">
                        <span class="label">状态:</span>
                        <span class="value">${getStatusText(capability.status)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">创建时间:</span>
                        <span class="value">${formatTime(capability.createTime)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">创建人:</span>
                        <span class="value">${capability.createBy || '未知'}</span>
                    </div>
                    ${capability.remark ? `
                    <div class="detail-row">
                        <span class="label">备注:</span>
                        <span class="value remark">${capability.remark}</span>
                    </div>` : ''}
                </div>
                <style>
                    .capability-info-window {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                        padding: 8px;
                        min-width: 280px;
                        max-width: 360px;
                        background: #fff;
                    }
                    .capability-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid #e8e8e8;
                        padding-bottom: 10px;
                        margin-bottom: 12px;
                    }
                    .capability-name {
                        margin: 0;
                        font-size: 16px;
                        font-weight: 600;
                        color: #1a1a1a;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 180px;
                    }
                    .capability-type {
                        padding: 4px 10px;
                        border-radius: 12px;
                        font-size: 12px;
                        font-weight: 500;
                        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                        color: white;
                        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
                    }
                    .capability-detail {
                        margin: 12px 0;
                    }
                    .detail-row {
                        display: flex;
                        margin-bottom: 8px;
                        align-items: flex-start;
                    }
                    .label {
                        font-size: 13px;
                        color: #666;
                        font-weight: 500;
                        min-width: 80px;
                        flex-shrink: 0;
                        margin-right: 8px;
                    }
                    .value {
                        font-size: 13px;
                        color: #333;
                        line-height: 1.4;
                        word-break: break-all;
                        flex: 1;
                    }
                    .value.remark {
                        background: #f8f9fa;
                        padding: 6px 8px;
                        border-radius: 4px;
                        border-left: 3px solid #10b981;
                        white-space: pre-wrap;
                    }
                </style>
            </div>`
        },

        /**
         * 显示产测信息窗口
         */
        showTestingInfoWindow(testing, lnglat) {
            try {
                // 创建产测信息窗口内容
                const content = this.createTestingInfoContent(testing)

                // 创建信息窗口
                const infoWindow = new T.InfoWindow(content, {
                    offset: new T.Point(0, -16),
                    closeButton: true,
                    autoPan: true,
                    width: 320,
                    minWidth: 280,
                    maxWidth: 400,
                })

                // 打开信息窗口
                this.map.openInfoWindow(infoWindow, lnglat)
            } catch (error) {
                console.error('显示产测信息窗口失败:', error)
            }
        },

        /**
         * 创建产测信息内容
         */
        createTestingInfoContent(testing) {
            // 格式化时间
            const formatTime = (timeStr) => {
                if (!timeStr) return '未知'
                try {
                    const date = new Date(timeStr)
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    })
                } catch (error) {
                    return timeStr
                }
            }

            // 获取完整地址
            const getFullAddress = () => {
                const { province, city, district } = testing
                const parts = [province, city, district].filter(Boolean)
                return parts.length > 0 ? parts.join(' ') : '未知'
            }

            // 获取部门类型文本
            const getDeptTypeText = (type) => {
                switch (type) {
                    case 1: return '公司'
                    case 2: return '部门'
                    case 3: return '工厂'
                    case 4: return '代工厂'
                    default: return '未知类型'
                }
            }

            // 获取状态文本
            const getStatusText = (status) => {
                return status === '0' ? '正常' : '停用'
            }

            return `
            <div class="testing-info-window">
                <div class="testing-header">
                    <h3 class="testing-name">${testing.deptName || '未命名代工厂'}</h3>
                    <div class="testing-type">${getDeptTypeText(testing.deptType)}</div>
                </div>
                <div class="testing-detail">
                    <div class="detail-row">
                        <span class="label">机构ID:</span>
                        <span class="value">${testing.deptId || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">机构名称:</span>
                        <span class="value">${testing.deptName || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">机构类型:</span>
                        <span class="value">${getDeptTypeText(testing.deptType)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">负责人:</span>
                        <span class="value">${testing.leader || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">联系电话:</span>
                        <span class="value">${testing.phone || '未知'}</span>
                    </div>
                    ${testing.email ? `
                    <div class="detail-row">
                        <span class="label">邮箱地址:</span>
                        <span class="value">${testing.email}</span>
                    </div>` : ''}
                    <div class="detail-row">
                        <span class="label">地址信息:</span>
                        <span class="value">${getFullAddress()}</span>
                    </div>
                    ${testing.address ? `
                    <div class="detail-row">
                        <span class="label">详细地址:</span>
                        <span class="value">${testing.address}</span>
                    </div>` : ''}
                    <div class="detail-row">
                        <span class="label">状态:</span>
                        <span class="value">${getStatusText(testing.status)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">创建时间:</span>
                        <span class="value">${formatTime(testing.createTime)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">创建人:</span>
                        <span class="value">${testing.createBy || '未知'}</span>
                    </div>
                    ${testing.updateTime ? `
                    <div class="detail-row">
                        <span class="label">更新时间:</span>
                        <span class="value">${formatTime(testing.updateTime)}</span>
                    </div>` : ''}
                    ${testing.updateBy ? `
                    <div class="detail-row">
                        <span class="label">更新人:</span>
                        <span class="value">${testing.updateBy}</span>
                    </div>` : ''}
                    ${testing.remark ? `
                    <div class="detail-row">
                        <span class="label">备注:</span>
                        <span class="value remark">${testing.remark}</span>
                    </div>` : ''}
                </div>
                <style>
                    .testing-info-window {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                        padding: 8px;
                        min-width: 280px;
                        max-width: 360px;
                        background: #fff;
                    }
                    .testing-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border-bottom: 1px solid #e8e8e8;
                        padding-bottom: 10px;
                        margin-bottom: 12px;
                    }
                    .testing-name {
                        margin: 0;
                        font-size: 16px;
                        font-weight: 600;
                        color: #1a1a1a;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 180px;
                    }
                    .testing-type {
                        padding: 4px 10px;
                        border-radius: 12px;
                        font-size: 12px;
                        font-weight: 500;
                        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                        color: white;
                        box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
                    }
                    .testing-detail {
                        margin: 12px 0;
                    }
                    .detail-row {
                        display: flex;
                        margin-bottom: 8px;
                        align-items: flex-start;
                    }
                    .label {
                        font-size: 13px;
                        color: #666;
                        font-weight: 500;
                        min-width: 80px;
                        flex-shrink: 0;
                        margin-right: 8px;
                    }
                    .value {
                        font-size: 13px;
                        color: #333;
                        line-height: 1.4;
                        word-break: break-all;
                        flex: 1;
                    }
                    .value.remark {
                        background: #f8f9fa;
                        padding: 6px 8px;
                        border-radius: 4px;
                        border-left: 3px solid #8b5cf6;
                        white-space: pre-wrap;
                    }
                </style>
            </div>`
        },

        // 添加地图标记（保持兼容性）
        addMapMarkers(data) {
            console.log('开始添加地图标记，数据长度:', data.length)
            let addedCount = 0
            data.forEach((item, index) => {
                console.log(`处理第${index + 1}个地图项:`, item)
                // 检查各种可能的经纬度字段名
                const longitude = item.longitude || item.lng || item.lon || item.x
                const latitude = item.latitude || item.lat || item.y

                if (longitude && latitude) {
                    const point = new T.LngLat(parseFloat(longitude), parseFloat(latitude))
                    console.log(`添加标记点: 经度${longitude}, 纬度${latitude}`)

                    // 创建标记图标
                    const icon = this.createMarkerIcon(item)

                    // 创建标记
                    const marker = new T.Marker(point, { icon: icon })

                    // 添加点击事件
                    marker.addEventListener('click', () => {
                        console.log('标记被点击:', item)
                        // 根据当前模块类型显示不同的信息窗口
                        if (this.activeModule === 'scenarios') {
                            this.showScenarioInfoWindow(item, point)
                        } else {
                            this.showDeviceInfoWindow(item, point)
                        }
                    })

                    // 添加到地图
                    this.map.addOverLay(marker)
                    addedCount++
                } else {
                    console.warn(`第${index + 1}个地图项缺少有效的经纬度信息:`, item)
                }
            })
            console.log(`成功添加 ${addedCount} 个地图标记`)
        },

        // 创建标记图标
        createMarkerIcon(item) {
            // 设备状态：1-未激活，2-禁用，3-在线，4-离线
            const status = parseInt(item.status) || 4
            let isOnline = status === 3
            let baseColor = isOnline ? '#10b981' : '#6b7280'
            const signalColor = this.getSignalColor(item.rssi || -100)

            // 对于场景、人员、开放能力、产测模块，强制显示为在线状态（绿色）
            if (['scenarios', 'personnel', 'openapi', 'testing'].includes(this.activeModule)) {
                isOnline = true
                baseColor = '#10b981' // 绿色
            }

            console.log(`设备${item.name || item.deviceName || '未知'}状态: ${status}, 在线: ${isOnline}, 信号强度: ${item.rssi}`)

            // 对于场景模块，使用统一的波纹图标
            if (this.activeModule === 'scenarios') {
                const iconUrl = this.createDeviceSvgIcon(item)
                return new T.Icon({
                    iconUrl: iconUrl,
                    iconSize: new T.Point(32, 32),
                    iconAnchor: new T.Point(16, 16)
                })
            }

            // 其他模块使用原有的图标样式
            return this.createModuleSpecificIcon(item, isOnline, baseColor, signalColor)
        },

        // 根据RSSI获取信号颜色
        getSignalColor(rssi) {
            // wifi信号强度rssi（信号极好4格[-55— 0]，信号好3格[-70— -55]，信号一般2格[-85— -70]，信号差1格[-100— -85]）
            if (rssi >= -55) return '#10b981' // 信号极好 - 绿色
            if (rssi >= -70) return '#22c55e' // 信号好 - 浅绿色
            if (rssi >= -85) return '#f59e0b' // 信号一般 - 黄色
            return '#ef4444' // 信号差 - 红色
        },

        // 创建模块特定的图标
        createModuleSpecificIcon(item, isOnline, baseColor, signalColor) {
            const iconHtml = this.getIconHtmlByModule(item, isOnline, baseColor, signalColor)

            return new T.Icon({
                iconUrl: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40">
                        <foreignObject width="40" height="40">
                            <div xmlns="http://www.w3.org/1999/xhtml">${iconHtml}</div>
                        </foreignObject>
                    </svg>
                `),
                iconSize: new T.Point(40, 40),
                iconAnchor: new T.Point(20, 20)
            })
        },

        // 根据模块获取图标HTML
        getIconHtmlByModule(item, isOnline, baseColor, signalColor) {
            switch (this.activeModule) {
                case 'devices':
                    return this.createDeviceIcon(isOnline, baseColor, signalColor)
                case 'products':
                    return this.createProductIcon(isOnline, baseColor, signalColor)
                case 'scenarios':
                    return this.createScenarioIcon(isOnline, baseColor, signalColor)
                case 'personnel':
                    return this.createPersonnelIcon(isOnline, baseColor, signalColor)
                case 'maintenance':
                    return this.createMaintenanceIcon(isOnline, baseColor, signalColor)
                case 'openapi':
                    return this.createOpenApiIcon(isOnline, baseColor, signalColor)
                case 'testing':
                    return this.createTestingIcon(isOnline, baseColor, signalColor)
                default:
                    return this.createDeviceIcon(isOnline, baseColor, signalColor)
            }
        },

        // 创建设备图标（圆圈+信号）
        createDeviceIcon(isOnline, baseColor, signalColor) {
            const bgColor = isOnline ? baseColor : '#9ca3af'
            const slashDisplay = isOnline ? 'none' : 'block'
            const animationClass = isOnline ? 'ripple-animation' : ''

            return `
                <style>
                    .device-marker {
                        position: relative;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .device-marker:hover {
                        transform: scale(1.1);
                    }
                    .signal-waves {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 24px;
                        height: 24px;
                        z-index: 1;
                    }
                    .signal-wave {
                        position: absolute;
                        border: 1px solid ${signalColor};
                        border-radius: 50%;
                        opacity: 0.6;
                    }
                    .signal-wave:nth-child(1) {
                        width: 16px;
                        height: 16px;
                        top: 4px;
                        left: 4px;
                    }
                    .signal-wave:nth-child(2) {
                        width: 20px;
                        height: 20px;
                        top: 2px;
                        left: 2px;
                    }
                    .signal-wave:nth-child(3) {
                        width: 24px;
                        height: 24px;
                        top: 0;
                        left: 0;
                    }
                    .ripple-animation .signal-wave {
                        animation: ripple 2s infinite;
                    }
                    .ripple-animation .signal-wave:nth-child(2) {
                        animation-delay: 0.3s;
                    }
                    .ripple-animation .signal-wave:nth-child(3) {
                        animation-delay: 0.6s;
                    }
                    @keyframes ripple {
                        0% {
                            opacity: 0.6;
                            transform: scale(0.8);
                        }
                        50% {
                            opacity: 0.3;
                            transform: scale(1.1);
                        }
                        100% {
                            opacity: 0.6;
                            transform: scale(0.8);
                        }
                    }
                    .device-circle {
                        width: 20px;
                        height: 20px;
                        background: ${bgColor};
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
                        position: relative;
                        z-index: 2;
                        transition: all 0.3s ease;
                    }
                    .device-marker:hover .device-circle {
                        box-shadow: 0 4px 12px rgba(0,0,0,0.25);
                    }
                    .device-offline-slash {
                        position: absolute;
                        width: 22px;
                        height: 2px;
                        background-color: #ef4444;
                        transform: rotate(45deg);
                        top: 50%;
                        left: 50%;
                        margin-left: -11px;
                        margin-top: -1px;
                        z-index: 3;
                        display: ${slashDisplay};
                        border-radius: 1px;
                    }
                </style>
                <div class="device-marker ${animationClass}">
                    <div class="signal-waves">
                        <div class="signal-wave"></div>
                        <div class="signal-wave"></div>
                        <div class="signal-wave"></div>
                    </div>
                    <div class="device-circle">
                        <svg width="10" height="10" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2L8 6H4V8H8L12 4L16 8H20V6H16L12 2ZM12 6L10 8V18H14V8L12 6ZM6 10V12H18V10H6ZM6 14V16H18V14H6ZM6 18V20H18V18H6Z"/>
                        </svg>
                    </div>
                    <div class="device-offline-slash"></div>
                </div>
            `
        },

        // 创建产品图标
        createProductIcon(isOnline, baseColor, signalColor) {
            const bgGradient = isOnline ?
                `linear-gradient(135deg, ${baseColor} 0%, ${this.darkenColor(baseColor, 20)} 100%)` :
                'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
            const shadowColor = isOnline ? this.hexToRgba(baseColor, 0.4) : 'rgba(107, 114, 128, 0.4)'
            const slashDisplay = isOnline ? 'none' : 'block'

            return `
                <style>
                    .product-marker {
                        position: relative;
                        width: 36px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .product-marker:hover {
                        transform: scale(1.15);
                    }
                    .product-container {
                        position: relative;
                        width: 28px;
                        height: 28px;
                        background: ${bgGradient};
                        border: 2px solid rgba(255, 255, 255, 0.9);
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 12px ${shadowColor}, 0 2px 4px rgba(0,0,0,0.1);
                        backdrop-filter: blur(4px);
                        transition: all 0.3s ease;
                    }
                    .product-marker:hover .product-container {
                        box-shadow: 0 6px 20px ${shadowColor}, 0 4px 8px rgba(0,0,0,0.15);
                    }
                    .product-offline-slash {
                        position: absolute;
                        width: 30px;
                        height: 2px;
                        background-color: #ef4444;
                        transform: rotate(45deg);
                        top: 50%;
                        left: 50%;
                        margin-left: -15px;
                        margin-top: -1px;
                        z-index: 3;
                        display: ${slashDisplay};
                    }
                </style>
                <div class="product-marker">
                    <div class="product-container">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2L2 7L12 12L22 7L12 2ZM2 17L12 22L22 17M2 12L12 17L22 12" stroke="white" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <div class="product-offline-slash"></div>
                </div>
            `
        },

        // 颜色处理辅助方法
        hexToRgba(hex, alpha) {
            const r = parseInt(hex.slice(1, 3), 16)
            const g = parseInt(hex.slice(3, 5), 16)
            const b = parseInt(hex.slice(5, 7), 16)
            return `rgba(${r}, ${g}, ${b}, ${alpha})`
        },

        darkenColor(hex, percent) {
            const r = parseInt(hex.slice(1, 3), 16)
            const g = parseInt(hex.slice(3, 5), 16)
            const b = parseInt(hex.slice(5, 7), 16)
            const factor = (100 - percent) / 100
            const newR = Math.round(r * factor)
            const newG = Math.round(g * factor)
            const newB = Math.round(b * factor)
            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
        },

        // 创建场景图标
        createScenarioIcon(isOnline, baseColor, signalColor) {
            const bgGradient = isOnline ?
                `linear-gradient(135deg, ${baseColor} 0%, ${this.darkenColor(baseColor, 20)} 100%)` :
                'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
            const shadowColor = isOnline ? this.hexToRgba(baseColor, 0.4) : 'rgba(107, 114, 128, 0.4)'
            const slashDisplay = isOnline ? 'none' : 'block'

            return `
                <style>
                    .scenario-marker {
                        position: relative;
                        width: 36px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .scenario-marker:hover {
                        transform: scale(1.15);
                    }
                    .scenario-container {
                        position: relative;
                        width: 28px;
                        height: 28px;
                        background: ${bgGradient};
                        border: 2px solid rgba(255, 255, 255, 0.9);
                        border-radius: 6px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 12px ${shadowColor}, 0 2px 4px rgba(0,0,0,0.1);
                        backdrop-filter: blur(4px);
                        transition: all 0.3s ease;
                    }
                    .scenario-marker:hover .scenario-container {
                        box-shadow: 0 6px 20px ${shadowColor}, 0 4px 8px rgba(0,0,0,0.15);
                    }
                    .scenario-offline-slash {
                        position: absolute;
                        width: 30px;
                        height: 2px;
                        background-color: #ef4444;
                        transform: rotate(45deg);
                        top: 50%;
                        left: 50%;
                        margin-left: -15px;
                        margin-top: -1px;
                        z-index: 3;
                        display: ${slashDisplay};
                    }
                </style>
                <div class="scenario-marker">
                    <div class="scenario-container">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
                        </svg>
                    </div>
                    <div class="scenario-offline-slash"></div>
                </div>
            `
        },

        // 创建人员图标
        createPersonnelIcon(isOnline, baseColor, signalColor) {
            const bgGradient = isOnline ?
                `linear-gradient(135deg, ${baseColor} 0%, ${this.darkenColor(baseColor, 20)} 100%)` :
                'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
            const shadowColor = isOnline ? this.hexToRgba(baseColor, 0.4) : 'rgba(107, 114, 128, 0.4)'
            const slashDisplay = isOnline ? 'none' : 'block'

            return `
                <style>
                    .personnel-marker {
                        position: relative;
                        width: 36px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .personnel-marker:hover {
                        transform: scale(1.15);
                    }
                    .personnel-container {
                        position: relative;
                        width: 28px;
                        height: 28px;
                        background: ${bgGradient};
                        border: 2px solid rgba(255, 255, 255, 0.9);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 12px ${shadowColor}, 0 2px 4px rgba(0,0,0,0.1);
                        backdrop-filter: blur(4px);
                        transition: all 0.3s ease;
                    }
                    .personnel-marker:hover .personnel-container {
                        box-shadow: 0 6px 20px ${shadowColor}, 0 4px 8px rgba(0,0,0,0.15);
                    }
                    .personnel-offline-slash {
                        position: absolute;
                        width: 30px;
                        height: 2px;
                        background-color: #ef4444;
                        transform: rotate(45deg);
                        top: 50%;
                        left: 50%;
                        margin-left: -15px;
                        margin-top: -1px;
                        z-index: 3;
                        display: ${slashDisplay};
                    }
                </style>
                <div class="personnel-marker">
                    <div class="personnel-container">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z"/>
                        </svg>
                    </div>
                    <div class="personnel-offline-slash"></div>
                </div>
            `
        },

        // 创建运维图标
        createMaintenanceIcon(isOnline, baseColor, signalColor) {
            const bgGradient = isOnline ?
                `linear-gradient(135deg, ${baseColor} 0%, ${this.darkenColor(baseColor, 20)} 100%)` :
                'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
            const shadowColor = isOnline ? this.hexToRgba(baseColor, 0.4) : 'rgba(239, 68, 68, 0.4)'

            return `
                <style>
                    .maintenance-marker {
                        position: relative;
                        width: 36px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .maintenance-marker:hover {
                        transform: scale(1.15);
                    }
                    .maintenance-container {
                        position: relative;
                        width: 28px;
                        height: 28px;
                        background: ${bgGradient};
                        border: 2px solid rgba(255, 255, 255, 0.9);
                        border-radius: 6px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 12px ${shadowColor}, 0 2px 4px rgba(0,0,0,0.1);
                        backdrop-filter: blur(4px);
                        transition: all 0.3s ease;
                    }
                    .maintenance-marker:hover .maintenance-container {
                        box-shadow: 0 6px 20px ${shadowColor}, 0 4px 8px rgba(0,0,0,0.15);
                    }
                </style>
                <div class="maintenance-marker">
                    <div class="maintenance-container">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            ${isOnline ?
                    '<path d="M22.7 19L13.6 9.9C14.5 7.6 14 4.9 12.1 3C10.1 1 7.1 0.6 4.7 1.7L9 6L6 9L1.6 4.7C0.4 7.1 0.9 10.1 2.9 12.1C4.8 14 7.5 14.5 9.8 13.6L18.9 22.7C19.3 23.1 19.9 23.1 20.3 22.7L22.6 20.4C23.1 20 23.1 19.3 22.7 19Z"/>' :
                    '<path d="M1 21H23L12 2L1 21ZM13 18H11V16H13V18ZM13 14H11V10H13V14Z"/>'}
                        </svg>
                    </div>
                </div>
            `
        },

        // 创建开放能力图标
        createOpenApiIcon(isOnline, baseColor, signalColor) {
            const bgGradient = isOnline ?
                `linear-gradient(135deg, ${baseColor} 0%, ${this.darkenColor(baseColor, 20)} 100%)` :
                'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
            const shadowColor = isOnline ? this.hexToRgba(baseColor, 0.4) : 'rgba(107, 114, 128, 0.4)'
            const slashDisplay = isOnline ? 'none' : 'block'

            return `
                <style>
                    .openapi-marker {
                        position: relative;
                        width: 36px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .openapi-marker:hover {
                        transform: scale(1.15);
                    }
                    .openapi-container {
                        position: relative;
                        width: 28px;
                        height: 28px;
                        background: ${bgGradient};
                        border: 2px solid rgba(255, 255, 255, 0.9);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 12px ${shadowColor}, 0 2px 4px rgba(0,0,0,0.1);
                        backdrop-filter: blur(4px);
                        transition: all 0.3s ease;
                    }
                    .openapi-marker:hover .openapi-container {
                        box-shadow: 0 6px 20px ${shadowColor}, 0 4px 8px rgba(0,0,0,0.15);
                    }
                    .openapi-offline-slash {
                        position: absolute;
                        width: 30px;
                        height: 2px;
                        background-color: #ef4444;
                        transform: rotate(45deg);
                        top: 50%;
                        left: 50%;
                        margin-left: -15px;
                        margin-top: -1px;
                        z-index: 3;
                        display: ${slashDisplay};
                    }
                </style>
                <div class="openapi-marker">
                    <div class="openapi-container">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z"/>
                        </svg>
                    </div>
                    <div class="openapi-offline-slash"></div>
                </div>
            `
        },

        // 创建产测图标
        createTestingIcon(isOnline, baseColor, signalColor) {
            const bgGradient = isOnline ?
                `linear-gradient(135deg, ${baseColor} 0%, ${this.darkenColor(baseColor, 20)} 100%)` :
                'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'
            const shadowColor = isOnline ? this.hexToRgba(baseColor, 0.4) : 'rgba(107, 114, 128, 0.4)'
            const slashDisplay = isOnline ? 'none' : 'block'

            return `
                <style>
                    .testing-marker {
                        position: relative;
                        width: 36px;
                        height: 36px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .testing-marker:hover {
                        transform: scale(1.15);
                    }
                    .testing-container {
                        position: relative;
                        width: 28px;
                        height: 28px;
                        background: ${bgGradient};
                        border: 2px solid rgba(255, 255, 255, 0.9);
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 4px 12px ${shadowColor}, 0 2px 4px rgba(0,0,0,0.1);
                        backdrop-filter: blur(4px);
                        transition: all 0.3s ease;
                    }
                    .testing-marker:hover .testing-container {
                        box-shadow: 0 6px 20px ${shadowColor}, 0 4px 8px rgba(0,0,0,0.15);
                    }
                    .testing-offline-slash {
                        position: absolute;
                        width: 30px;
                        height: 2px;
                        background-color: #ef4444;
                        transform: rotate(45deg);
                        top: 50%;
                        left: 50%;
                        margin-left: -15px;
                        margin-top: -1px;
                        z-index: 3;
                        display: ${slashDisplay};
                    }
                </style>
                <div class="testing-marker">
                    <div class="testing-container">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M9 11H7V9H9V11ZM13 11H11V9H13V11ZM17 11H15V9H17V11ZM19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19Z"/>
                        </svg>
                    </div>
                    <div class="testing-offline-slash"></div>
                </div>
            `
        },

        // 显示信息窗口
        showInfoWindow(item, point) {
            const content = this.createInfoContent(item)
            const infoWindow = new T.InfoWindow(content, {
                offset: new T.Point(0, -16),
                closeButton: true,
                autoPan: true,
                width: 280
            })
            this.map.openInfoWindow(infoWindow, point)
        },

        // 创建信息窗口内容
        createInfoContent(item) {
            return `
                <div style="padding: 10px; font-family: Arial, sans-serif;">
                    <h4 style="margin: 0 0 10px 0; color: #333;">${item.deviceName || item.name || '未知'}</h4>
                    <p style="margin: 5px 0; font-size: 12px; color: #666;">
                        <strong>类型:</strong> ${item.productName || item.type || '未知'}
                    </p>
                    <p style="margin: 5px 0; font-size: 12px; color: #666;">
                        <strong>状态:</strong>
                        <span style="color: ${item.status === 3 ? '#10b981' : '#ef4444'}">
                            ${item.status === 3 ? '在线' : '离线'}
                        </span>
                    </p>
                    <p style="margin: 5px 0; font-size: 12px; color: #666;">
                        <strong>位置:</strong> ${item.networkAddress || '未知'}
                    </p>
                </div>
            `
        },

        // 初始化图表
        initCharts() {
            this.$nextTick(() => {
                this.initTrendChart()
                this.initPieCharts()
            })
        },

        // 初始化趋势图
        initTrendChart() {
            const chartDom = document.getElementById('trendChart')
            if (!chartDom) return

            this.trendChart = echarts.init(chartDom)
            this.updateTrendChart()
        },

        // 更新趋势图
        updateTrendChart() {
            if (!this.trendChart) return

            const chartData = this.currentModuleData.chart.data
            console.log('更新趋势图，当前模块:', this.activeModule)
            console.log('趋势图数据:', chartData)
            console.log('xData长度:', chartData.xData?.length, 'yData长度:', chartData.yData?.length)
            console.log('xData内容:', chartData.xData)
            console.log('yData内容:', chartData.yData)

            // 检查数据是否有效
            if (!chartData || !chartData.xData || !chartData.yData ||
                chartData.xData.length === 0 || chartData.yData.length === 0) {
                console.log('趋势图数据为空，跳过渲染')
                return
            }

            const module = this.modules.find(m => m.id === this.activeModule)

            // 根据不同模块获取不同的图表配置
            const chartConfig = this.getTrendChartConfig()

            const option = {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: module ? module.color : '#3b82f6',
                    borderWidth: 1,
                    textStyle: {
                        color: '#333'
                    },
                    formatter: function (params) {
                        const param = params[0]
                        return `${param.name}<br/>${param.seriesName}: ${param.value}`
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '18%',
                    top: '5%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: chartData.xData,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#e5e7eb',
                            width: 1
                        }
                    },
                    axisTick: {
                        show: true,
                        alignWithLabel: true,
                        lineStyle: {
                            color: '#e5e7eb'
                        }
                    },
                    axisLabel: {
                        show: true,
                        color: '#6b7280',
                        fontSize: 12,
                        interval: 0, // 强制显示所有标签
                        rotate: chartData.xData.length > 6 ? 45 : 0, // 数据多时倾斜显示
                        margin: 8,
                        formatter: function (value) {
                            // 确保标签完整显示
                            return value
                        }
                    },
                    boundaryGap: (this.activeModule === 'scenarios' || this.activeModule === 'personnel' || this.activeModule === 'openapi' || this.activeModule === 'testing') ? true : false // 场景、人员、开放能力和产测模块柱状图需要边界间隙，折线图不需要
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#6b7280',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#f3f4f6',
                            type: 'dashed'
                        }
                    }
                },
                series: [{
                    name: chartConfig.seriesName,
                    data: chartData.yData,
                    type: (this.activeModule === 'scenarios' || this.activeModule === 'personnel' || this.activeModule === 'openapi' || this.activeModule === 'testing') ? 'bar' : 'line', // 场景、人员、开放能力和产测模块使用柱状图，其他模块使用折线图
                    smooth: this.activeModule !== 'products' && this.activeModule !== 'scenarios' && this.activeModule !== 'personnel' && this.activeModule !== 'maintenance' && this.activeModule !== 'openapi' && this.activeModule !== 'testing', // 产品、场景、人员、运维、开放能力和产测模块不使用平滑曲线
                    step: this.activeModule === 'products' ? 'end' : false, // 产品模块使用阶梯状折线
                    symbol: (this.activeModule === 'scenarios' || this.activeModule === 'personnel' || this.activeModule === 'openapi' || this.activeModule === 'testing') ? 'none' : (this.activeModule === 'maintenance' ? 'arrow' : 'circle'), // 场景、人员、开放能力和产测模块不显示点，运维模块显示箭头
                    symbolSize: 6,
                    // 柱状图配置
                    ...((this.activeModule === 'scenarios' || this.activeModule === 'personnel' || this.activeModule === 'openapi' || this.activeModule === 'testing') ? {
                        barWidth: '60%',
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: module ? module.color : (this.activeModule === 'personnel' ? '#10b981' : (this.activeModule === 'openapi' ? '#3b82f6' : (this.activeModule === 'testing' ? '#f59e0b' : '#8b5cf6')))
                                }, {
                                    offset: 1,
                                    color: (module ? module.color : (this.activeModule === 'personnel' ? '#10b981' : (this.activeModule === 'openapi' ? '#3b82f6' : (this.activeModule === 'testing' ? '#f59e0b' : '#8b5cf6')))) + '80'
                                }]
                            },
                            borderRadius: [4, 4, 0, 0],
                            shadowColor: (module ? module.color : (this.activeModule === 'personnel' ? '#10b981' : (this.activeModule === 'openapi' ? '#3b82f6' : (this.activeModule === 'testing' ? '#f59e0b' : '#8b5cf6')))) + '40',
                            shadowBlur: 8,
                            shadowOffsetY: 2
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 15,
                                shadowColor: (module ? module.color : (this.activeModule === 'personnel' ? '#10b981' : (this.activeModule === 'openapi' ? '#3b82f6' : (this.activeModule === 'testing' ? '#f59e0b' : '#8b5cf6')))) + '60'
                            }
                        },
                        animationDelay: function (idx) {
                            return idx * 100
                        }
                    } : {
                        // 折线图配置
                        lineStyle: {
                            color: module ? module.color : '#3b82f6',
                            width: 3,
                            type: this.activeModule === 'maintenance' ? 'dashed' : 'solid', // 运维模块使用虚线
                            shadowColor: (module ? module.color : '#3b82f6') + '40',
                            shadowBlur: 10,
                            shadowOffsetY: 2
                        },
                        itemStyle: {
                            color: module ? module.color : '#3b82f6',
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        // 产品模块不显示面积填充，其他模块保持原有样式
                        areaStyle: this.activeModule === 'products' ? null : {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: (module ? module.color : '#3b82f6') + '40'
                                }, {
                                    offset: 1,
                                    color: (module ? module.color : '#3b82f6') + '00'
                                }]
                            }
                        },
                        emphasis: {
                            focus: 'series',
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: (module ? module.color : '#3b82f6') + '80'
                            }
                        }
                    })
                }]
            }

            this.trendChart.setOption(option, true)
        },

        // 获取趋势图配置（根据不同模块返回不同配置）
        getTrendChartConfig() {
            const configs = {
                devices: {
                    seriesName: '设备接入数量',
                    yAxisName: '设备数量'
                },
                products: {
                    seriesName: '产品接入数量',
                    yAxisName: '产品数量'
                },
                scenarios: {
                    seriesName: '场景执行次数',
                    yAxisName: '执行次数'
                },
                personnel: {
                    seriesName: '人员活跃度',
                    yAxisName: '活跃人数'
                },
                maintenance: {
                    seriesName: '运维事件数',
                    yAxisName: '事件数量'
                },
                openapi: {
                    seriesName: 'API调用次数',
                    yAxisName: '调用次数'
                },
                testing: {
                    seriesName: '测试执行次数',
                    yAxisName: '执行次数'
                }
            }

            return configs[this.activeModule] || configs.devices
        },

        // 获取饼图配置（根据不同模块返回不同配置）
        getPieChartConfig() {
            const configs = {
                devices: {
                    radius: ['30%', '60%'],
                    showLabel: false,
                    labelFormatter: '{b}: {c}'
                },
                products: {
                    radius: ['30%', '60%'],
                    showLabel: true,
                    labelFormatter: '{b}\n{d}%'
                },
                scenarios: {
                    radius: ['20%', '40%'],
                    showLabel: true,
                    labelFormatter: '{b}\n  {c}'
                },
                personnel: {
                    radius: ['45%', '75%'],
                    showLabel: false,
                    labelFormatter: '{b}'
                },
                maintenance: {
                    radius: ['20%', '45%'],
                    showLabel: true,
                    labelFormatter: '{b}\n{d}%'
                },
                openapi: {
                    radius: ['15%', '33%'],
                    showLabel: true,
                    labelFormatter: '{b}\n {d}%'
                },
                testing: {
                    radius: ['30%', '60%'],
                    showLabel: false,
                    labelFormatter: '{b}: {c}'
                }
            }

            return configs[this.activeModule] || configs.devices
        },

        // 初始化饼图
        initPieCharts() {
            this.pieCharts = []
            this.currentModuleData.pieCharts.forEach((pieData, index) => {
                const chartDom = document.getElementById('pieChart' + index)
                if (chartDom) {
                    const chart = echarts.init(chartDom)
                    this.pieCharts.push(chart)
                    this.updatePieChart(chart, pieData)
                }
            })
        },

        // 更新饼图
        updatePieChart(chart, pieData) {
            // 根据不同模块获取饼图配置
            const pieConfig = this.getPieChartConfig()
            const module = this.modules.find(m => m.id === this.activeModule)

            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: module ? module.color : '#3b82f6',
                    borderWidth: 1,
                    textStyle: {
                        color: '#333',
                        fontSize: 12
                    }
                },
                legend: {
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '1%',
                    textStyle: {
                        color: '#6b7280',
                        fontSize: 10
                    },
                    itemGap: 8,
                    itemWidth: 12,
                    itemHeight: 8,
                    formatter: function (name) {
                        return name
                    }
                },
                series: [{
                    name: pieData.title,
                    type: 'pie',
                    radius: pieConfig.radius,
                    center: ['50%', '38%'],
                    avoidLabelOverlap: true,
                    label: {
                        show: pieConfig.showLabel,
                        position: 'outside',
                        fontSize: 11,
                        color: '#6b7280',
                        formatter: pieConfig.labelFormatter,
                        minMargin: 5,
                        alignTo: 'none',
                        bleedMargin: 10
                    },
                    emphasis: {
                        label: {
                            show: false,
                            fontSize: '14',
                            fontWeight: 'bold',
                            color: '#333'
                        },
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.3)'
                        }
                    },
                    labelLine: {
                        show: pieConfig.showLabel,
                        length: 20,
                        length2: 15,
                        smooth: 0.2,
                        lineStyle: {
                            color: '#d1d5db',
                            width: 1
                        }
                    },
                    animationType: 'scale',
                    animationEasing: 'elasticOut',
                    animationDelay: function (idx) {
                        return Math.random() * 200
                    },
                    data: pieData.data.map(item => ({
                        value: item.value,
                        name: item.name,
                        itemStyle: {
                            color: item.color,
                            borderColor: '#fff',
                            borderWidth: 2
                        }
                    }))
                }]
            }

            chart.setOption(option)
        },

        // 更新图表
        updateCharts() {
            this.updateTrendChart()

            // 重新初始化饼图
            this.pieCharts.forEach(chart => {
                chart.dispose()
            })
            this.$nextTick(() => {
                this.initPieCharts()
            })
        },

        // 加载数据
        async loadData() {
            // 根据当前活跃模块加载对应数据
            await this.loadModuleData(this.activeModule)
        },

        // 加载模块数据
        async loadModuleData(moduleId) {
            try {
                let data = null
                switch (moduleId) {
                    case 'devices':
                        data = await this.fetchDeviceData()
                        break
                    case 'products':
                        data = await this.fetchProductData()
                        break
                    case 'scenarios':
                        data = await this.fetchScenarioData()
                        break
                    case 'personnel':
                        data = await this.fetchPersonnelData()
                        break
                    case 'maintenance':
                        data = await this.fetchMaintenanceData()
                        break
                    case 'openapi':
                        data = await this.fetchOpenApiData()
                        break
                    case 'testing':
                        data = await this.fetchTestingData()
                        break
                }

                if (data) {
                    await this.processModuleData(moduleId, data)
                }
            } catch (error) {
                console.error(`加载${moduleId}数据失败:`, error)
                this.$message.error(`加载数据失败: ${error.message}`)
            }
        },

        // 处理模块数据
        async processModuleData(moduleId, apiData) {
            console.log(`处理${moduleId}模块数据:`, apiData)
            const { data1, data2, map } = apiData

            // 更新统计数据
            if (data1) {
                this.updateStatsData(moduleId, data1)
            }

            // 更新图表数据
            if (data2) {
                console.log(`${moduleId}趋势图数据:`, data2)
                this.updateChartData(moduleId, data2)
            }

            // 更新地图数据（异步处理地理编码）
            if (map && map.mapInfo) {
                console.log(`${moduleId}地图数据:`, map.mapInfo, '数据长度:', map.mapInfo.length)
                await this.updateMapDataFromApi(moduleId, map)
            }

            // 更新表格数据
            if (data1 && data1.listMap) {
                this.updateTableData(moduleId, data1.listMap)
            }

            // 更新饼图数据
            if (data1) {
                this.updatePieChartsData(moduleId, data1)
            }

            // 刷新图表
            this.$nextTick(() => {
                this.updateCharts()
                this.updateMapData()
            })
        },

        // 更新统计数据
        updateStatsData(moduleId, data1) {
            const stats = []

            if (data1.count1) {
                stats.push({
                    label: data1.count1.name,
                    value: data1.count1.value,
                    icon: this.getStatIcon(moduleId, 'count1'),
                    color: this.getStatColor(moduleId, 'count1')
                })
            }

            if (data1.count2) {
                stats.push({
                    label: data1.count2.name,
                    value: data1.count2.value,
                    icon: this.getStatIcon(moduleId, 'count2'),
                    color: this.getStatColor(moduleId, 'count2')
                })
            }

            this.moduleData[moduleId].stats = stats
        },

        // 获取统计图标
        getStatIcon(moduleId, type) {
            const iconMap = {
                devices: { count1: 'el-icon-cpu', count2: 'el-icon-collection' },
                products: { count1: 'el-icon-box', count2: 'el-icon-document' },
                scenarios: { count1: 'el-icon-office-building', count2: 'el-icon-lightning' },
                personnel: { count1: 'el-icon-user', count2: 'el-icon-success' },
                maintenance: { count1: 'el-icon-setting', count2: 'el-icon-warning' },
                openapi: { count1: 'el-icon-connection', count2: 'el-icon-data-analysis' },
                testing: { count1: 'el-icon-data-analysis', count2: 'el-icon-check' }
            }
            return iconMap[moduleId]?.[type] || 'el-icon-info'
        },

        // 获取统计颜色
        getStatColor(moduleId, type) {
            const module = this.modules.find(m => m.id === moduleId)
            return type === 'count1' ? module?.color || '#3b82f6' : '#10b981'
        },

        // 更新图表数据
        updateChartData(moduleId, data2) {
            // 确保xData和yData是数组格式
            const xData = Array.isArray(data2.xData) ? data2.xData : []
            const yData = Array.isArray(data2.yData) ? data2.yData : []

            console.log(`${moduleId}图表数据处理:`, {
                原始xData: data2.xData,
                原始yData: data2.yData,
                处理后xData: xData,
                处理后yData: yData
            })

            this.moduleData[moduleId].chart = {
                title: data2.title || '趋势图',
                data: {
                    xData: xData,
                    yData: yData
                }
            }
        },

        // 更新地图数据
        async updateMapDataFromApi(moduleId, mapData) {
            const mapInfo = mapData.mapInfo || []
            console.log(`开始处理${moduleId}地图数据，原始数据:`, mapInfo)

            // 设备/产品/运维三个tab返回的数据默认有经纬度，直接使用；其他tab才需要处理坐标
            const needsCoordinateProcessing = !['devices', 'products', 'maintenance'].includes(moduleId)

            let processedData
            if (needsCoordinateProcessing) {
                // 需要处理坐标的模块：场景/人员/开放能力/产测
                processedData = await Promise.all(mapInfo.map(async (item, index) => {
                    console.log(`处理地图项${index + 1}:`, item)
                    // 检查各种可能的经纬度字段名
                    const longitude = item.longitude || item.lng || item.lon || item.x
                    const latitude = item.latitude || item.lat || item.y

                    // 如果已有经纬度，直接返回（统一字段名）
                    if (longitude && latitude) {
                        return {
                            ...item,
                            longitude: parseFloat(longitude),
                            latitude: parseFloat(latitude)
                        }
                    }

                    // 如果有省市区信息但没有经纬度，进行地理编码
                    if (item.province || item.city || item.district) {
                        const location = await this.getLocationByAddress(
                            item.province || '',
                            item.city || '',
                            item.district || ''
                        )

                        return {
                            ...item,
                            longitude: location.longitude,
                            latitude: location.latitude
                        }
                    }

                    // 如果既没有经纬度也没有地址信息，使用默认坐标
                    return {
                        ...item,
                        longitude: 116.4074,
                        latitude: 39.9042
                    }
                }))
            } else {
                // 不需要处理坐标的模块：设备/产品/运维，直接使用接口返回的数据
                processedData = mapInfo.map(item => {
                    // 统一字段名，确保经纬度字段一致
                    const longitude = item.longitude || item.lng || item.lon || item.x
                    const latitude = item.latitude || item.lat || item.y
                    return {
                        ...item,
                        longitude: longitude ? parseFloat(longitude) : 116.4074,
                        latitude: latitude ? parseFloat(latitude) : 39.9042
                    }
                })
                console.log(`${moduleId}模块直接使用接口数据，无需地理编码处理`)
            }

            console.log(`${moduleId}地图数据处理完成，最终数据:`, processedData)
            this.moduleData[moduleId].map = {
                title: mapData.title || '分布图',
                data: processedData
            }
            console.log(`${moduleId}模块地图数据已更新:`, this.moduleData[moduleId].map)
        },

        // 更新表格数据
        updateTableData(moduleId, listMap) {
            this.moduleData[moduleId].table = {
                title: listMap.title || '数据列表',
                columns: this.getTableColumns(moduleId, listMap.nameList),
                data: listMap.list || []
            }
        },

        // 获取表格列配置
        getTableColumns(moduleId, nameList) {
            if (!nameList || !Array.isArray(nameList)) return []

            return nameList.map(name => ({
                prop: name,
                label: name,
                width: this.getColumnWidth(name)
            }))
        },

        // 获取列宽度
        getColumnWidth(columnName) {
            const widthMap = {
                '设备编号': '150',
                '设备名称': '150',
                '产品名称': '120',
                '场景名称': '150',
                '状态': '80',
                '创建时间': '150',
                '更新时间': '150',
                '标识符': '120',
                '日志值': '100'
            }
            return widthMap[columnName] || '120'
        },

        // 更新饼图数据
        updatePieChartsData(moduleId, data1) {
            const pieCharts = []

            if (data1.pie1) {
                pieCharts.push({
                    title: data1.pie1.title,
                    data: this.convertPieData(data1.pie1.pieData1)
                })
            }

            if (data1.pie2) {
                pieCharts.push({
                    title: data1.pie2.title,
                    data: this.convertPieData(data1.pie2.pieData2)
                })
            }

            this.moduleData[moduleId].pieCharts = pieCharts
        },

        // 转换饼图数据格式
        convertPieData(pieData) {
            if (!pieData) return []

            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#22c55e']
            let colorIndex = 0

            return Object.entries(pieData)
                .filter(([key]) => !key.includes('总数'))
                .map(([name, value]) => ({
                    name,
                    value,
                    color: colors[colorIndex++ % colors.length]
                }))
        },

        // 数据接口方法
        async fetchDeviceData() {
            const response = await getEquipmentData()
            return response.data
        },

        async fetchProductData() {
            const response = await getProductData()
            return response.data
        },

        async fetchScenarioData() {
            const response = await getScenarioData()
            return response.data
        },

        async fetchPersonnelData() {
            const response = await getOrganizeData()
            return response.data
        },

        async fetchMaintenanceData() {
            const response = await getOMData()
            return response.data
        },

        async fetchOpenApiData() {
            const response = await getOpenCapabilityData()
            return response.data
        },

        async fetchTestingData() {
            const response = await getProductionTestData()
            return response.data
        },

        // 地理编码服务 - 根据省市区获取经纬度
        async getLocationByAddress(province, city, district) {
            try {
                // 参数验证
                if (!province && !city && !district) {
                    console.warn('地址信息为空，使用默认坐标')
                    return this.getDefaultLocationByCity('合肥')
                }

                // 构建查询地址
                const address = `${province || ''}${city || ''}${district || ''}`.trim()

                if (!address) {
                    console.warn('地址信息为空，使用默认坐标')
                    return this.getDefaultLocationByCity('合肥')
                }

                // 检查API密钥
                if (!process.env.VUE_APP_TIANDITU_KEY) {
                    console.warn('天地图API密钥未配置，使用默认坐标')
                    return this.getDefaultLocationByCity(city || '合肥')
                }

                // 使用天地图地理编码API (HTTPS)
                const response = await this.$http.get('https://api.tianditu.gov.cn/geocoder', {
                    params: {
                        ds: JSON.stringify({
                            keyWord: address
                        }),
                        tk: process.env.VUE_APP_TIANDITU_KEY
                    },
                    timeout: 5000 // 设置5秒超时
                })

                if (response.data && response.data.location && response.data.location.lon && response.data.location.lat) {
                    return {
                        longitude: parseFloat(response.data.location.lon),
                        latitude: parseFloat(response.data.location.lat)
                    }
                }

                // 如果天地图API返回数据格式不正确，使用备用坐标
                console.warn('天地图API返回数据格式不正确，使用默认坐标')
                return this.getDefaultLocationByCity(city || '合肥')
            } catch (error) {
                // 详细的错误处理
                if (error.message && error.message.includes('Mixed Content')) {
                    console.warn('Mixed Content错误：HTTPS页面无法请求HTTP资源，已切换到HTTPS API')
                } else if (error.code === 'ECONNABORTED') {
                    console.warn('地理编码请求超时，使用默认坐标')
                } else {
                    console.warn('地理编码失败，使用默认坐标:', error.message || error)
                }
                return this.getDefaultLocationByCity(city || '合肥')
            }
        },

        // 获取城市默认坐标（备用方案）
        getDefaultLocationByCity(city) {
            // 参数验证
            if (!city || typeof city !== 'string') {
                console.warn('城市参数无效，使用北京坐标')
                return { longitude: 116.4074, latitude: 39.9042 }
            }

            const cityCoords = {
                '北京': { longitude: 116.4074, latitude: 39.9042 },
                '上海': { longitude: 121.4737, latitude: 31.2304 },
                '广州': { longitude: 113.2644, latitude: 23.1291 },
                '深圳': { longitude: 114.0579, latitude: 22.5431 },
                '杭州': { longitude: 120.1551, latitude: 30.2741 },
                '南京': { longitude: 118.7969, latitude: 32.0603 },
                '武汉': { longitude: 114.3054, latitude: 30.5931 },
                '成都': { longitude: 104.0665, latitude: 30.5723 },
                '西安': { longitude: 108.9402, latitude: 34.3416 },
                '重庆': { longitude: 106.5516, latitude: 29.5630 },
                '天津': { longitude: 117.1901, latitude: 39.1235 },
                '苏州': { longitude: 120.6197, latitude: 31.2989 },
                '合肥': { longitude: 117.2272, latitude: 31.8206 },
                '济南': { longitude: 117.0009, latitude: 36.6758 },
                '青岛': { longitude: 120.3826, latitude: 36.0671 },
                '大连': { longitude: 121.6147, latitude: 38.9140 },
                '沈阳': { longitude: 123.4315, latitude: 41.8057 },
                '长春': { longitude: 125.3245, latitude: 43.8171 },
                '哈尔滨': { longitude: 126.5358, latitude: 45.8023 },
                '石家庄': { longitude: 114.5149, latitude: 38.0428 },
                '太原': { longitude: 112.5489, latitude: 37.8706 },
                '呼和浩特': { longitude: 111.7519, latitude: 40.8414 },
                '银川': { longitude: 106.2309, latitude: 38.4872 },
                '西宁': { longitude: 101.7782, latitude: 36.6171 },
                '乌鲁木齐': { longitude: 87.6177, latitude: 43.7928 },
                '拉萨': { longitude: 91.1322, latitude: 29.6604 },
                '昆明': { longitude: 102.8329, latitude: 24.8801 },
                '贵阳': { longitude: 106.6302, latitude: 26.6477 },
                '南宁': { longitude: 108.3669, latitude: 22.8170 },
                '海口': { longitude: 110.3312, latitude: 20.0311 },
                '福州': { longitude: 119.3063, latitude: 26.0745 },
                '厦门': { longitude: 118.1689, latitude: 24.4797 },
                '南昌': { longitude: 115.8921, latitude: 28.6765 },
                '长沙': { longitude: 112.9388, latitude: 28.2282 },
                '郑州': { longitude: 113.6254, latitude: 34.7466 },
                '兰州': { longitude: 103.8343, latitude: 36.0611 }
            }

            try {
                // 查找匹配的城市（支持模糊匹配）
                const cityName = city.trim()
                for (const [name, coords] of Object.entries(cityCoords)) {
                    if (cityName.includes(name) || name.includes(cityName)) {
                        console.log(`找到匹配城市: ${name}，坐标: ${coords.longitude}, ${coords.latitude}`)
                        return coords
                    }
                }

                // 如果没有找到匹配的城市，记录日志并返回北京坐标
                console.warn(`未找到城市 "${cityName}" 的坐标，使用北京默认坐标`)
                return { longitude: 116.4074, latitude: 39.9042 }
            } catch (error) {
                console.error('获取默认城市坐标时发生错误:', error)
                return { longitude: 116.4074, latitude: 39.9042 }
            }
        },

        // Location Detection UX Event Handlers

        /**
         * Handle location detection start event
         */
        handleLocationDetectionStart(event) {
            this.locationUXState.isDetecting = true
            this.locationUXState.errorMessage = ''
            this.locationDetectionInProgress = true

            console.log('Location detection started:', event)
        },

        /**
         * Handle successful location detection
         */
        handleLocationDetectionSuccess(result) {
            this.locationUXState.isDetecting = false
            this.locationDetectionInProgress = false

            if (result.ux && result.ux.shouldShowNotification) {
                this.locationUXState.detectedLocation = result.ux.userFriendlyLocation
            }

            console.log('Location detection successful:', result)
        },

        /**
         * Handle location detection error
         */
        handleLocationDetectionError(errorInfo) {
            this.locationUXState.isDetecting = false
            this.locationDetectionInProgress = false

            if (errorInfo.shouldShowErrorNotification) {
                this.locationUXState.errorMessage = errorInfo.userFriendlyMessage
            }

            console.warn('Location detection error:', errorInfo)
        },

        /**
         * Handle location detection completion
         */
        handleLocationDetectionComplete(result) {
            this.locationUXState.isDetecting = false
            this.locationDetectionInProgress = false

            console.log('Location detection completed:', result)
        },

        /**
         * Handle manual location refresh request
         */
        async handleRefreshLocation() {
            if (this.locationUXManager && !this.locationDetectionInProgress) {
                try {
                    // Clear previous state
                    this.locationUXState.detectedLocation = ''
                    this.locationUXState.errorMessage = ''

                    // Trigger manual retry
                    const locationResult = await this.locationUXManager.retryDetection({
                        enableAnimation: true,
                        priority: 'user_initiated'
                    })

                    if (locationResult && locationResult.latitude && locationResult.longitude) {
                        // Update map center
                        await this.updateMapCenterFromLocation(locationResult)
                    }
                } catch (error) {
                    console.error('Manual location refresh failed:', error)
                }
            }
        }
    },

    beforeDestroy() {
        // 销毁图表实例
        if (this.trendChart) {
            this.trendChart.dispose()
        }
        this.pieCharts.forEach(chart => {
            chart.dispose()
        })
    }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
    padding: 1rem;
    padding-right: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    height: calc(100vh - 50px);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    overflow: hidden;
}

.module-navigation {
    margin-bottom: 1rem;
}

.nav-tabs {
    display: flex;
    gap: 16px;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow-x: auto;
}

.nav-tab {
    position: relative;
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    min-width: 140px;
    justify-content: center;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    &:hover {
        transform: translateY(-2px) scale(1.02);

        &::before {
            opacity: 1;
        }
    }

    &.active {
        color: white;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);

        .tab-icon {
            color: white;
            animation: pulse 2s infinite;
        }

        .tab-text {
            color: white;
        }

        .active-indicator {
            position: absolute;
            bottom: 4px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 3px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
        }
    }
}

.tab-icon {
    font-size: 18px;
    transition: all 0.3s ease;
    z-index: 1;
}

.tab-text {
    font-weight: 600;
    font-size: 14px;
    z-index: 1;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

.dashboard-grid {
    display: flex;
    gap: 1rem;
    height: calc(100vh - 150px);
    min-height: 600px;
    padding-bottom: 1rem;
    overflow: hidden;
}

.left-panel {
    flex: 0 0 60%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.right-panel {
    flex: 0 0 39%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-width: 0;
}

.map-panel,
.table-panel,
.chart-panel,
.pie-chart-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    }
}

.map-panel {
    flex: 1;
    min-height: 280px;
}

.table-panel {
    flex: 0 0 330px;
}

.left-panel .chart-panel {
    flex: 0 0 334px;
    margin-bottom: 20px;
}

.map-content {
    height: calc(100% - 50px);
    width: 100%;
    border-radius: 0 0 20px 20px;
}

.panel-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
    backdrop-filter: blur(10px);

    .header-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    .panel-title {
        margin: 0;
        font-size: 18px;
        font-weight: 700;
        letter-spacing: -0.025em;
    }

    .map-legend {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .legend-label {
        color: #64748b;
    }
}

.stats-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 0 0 auto;
}

.chart-panel {
    flex: 1;
    min-height: 200px;
}

.pie-charts-section {
    display: flex;
    gap: 1rem;
    flex: 1;
    margin-bottom: 20px;
}

.pie-chart-panel {
    flex: 1;
    min-height: 180px;
}

.stat-card {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    min-height: 80px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--stat-color), transparent);
    }

    &:hover {
        transform: translateY(-2px) scale(1.01);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
    }
}

.stat-icon-wrapper {
    position: relative;
    width: 56px;
    height: 56px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
        border-radius: 16px;
    }
}

.stat-icon {
    font-size: 24px;
    z-index: 1;
    transition: transform 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-value {
    font-size: 28px;
    font-weight: 800;
    line-height: 1;
    letter-spacing: -0.025em;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.5);

    i {
        font-size: 14px;
    }
}

.data-table-container {
    flex: 1;
    min-height: 300px;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px 20px 0 0;
    }
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
}

.table-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 1rem;
}

.table-action-btn {
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
}

.table-content {
    padding: 1rem;
    max-height: 280px;
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 1px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 0.5px;

        &:hover {
            background: #9ca3af;
        }
    }
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 1rem;
        text-align: left;
        font-weight: 600;
        color: #4a5568;
        font-size: 14px;
        border-bottom: 2px solid #e2e8f0;
        position: relative;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%);
        }
    }

    td {
        padding: 1rem;
        border-bottom: 1px solid #f1f5f9;
        color: #2d3748;
        font-size: 14px;
        transition: background-color 0.2s ease;
    }

    tr:hover td {
        background-color: #f8fafc;
    }

    tr:last-child td {
        border-bottom: none;
    }
}

.status-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &::before {
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: currentColor;
        margin-right: 6px;
        animation: pulse 2s infinite;
    }

    &.online {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
    }

    &.offline {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
    }

    &.warning {
        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
    }

    &.running {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
    }

    &.stopped {
        background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(160, 174, 192, 0.3);
    }

    &.completed {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
    }

    &.pending {
        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
    }

    &.failed {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
    }

    &.success {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
    }
}

.chart-content {
    height: 300px;
    padding: 1rem;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px 20px 0 0;
    }
}

.pie-charts {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
}

.pie-chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    flex: 1;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px 16px 0 0;
    }
}

.pie-chart-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);

    h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #2d3748;
        letter-spacing: -0.025em;
    }
}

.pie-chart-content {
    height: 254px;
    padding: 1rem;
    position: relative;

    .chart-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #94a3b8;
        font-size: 14px;
    }

    .chart-error {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ef4444;
        font-size: 14px;
        text-align: center;
    }
}

.chart-container {
    position: relative;

    .chart-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: #94a3b8;
        font-size: 14px;

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
    }

    .chart-error {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #ef4444;
        font-size: 14px;
        text-align: center;

        i {
            font-size: 24px;
            margin-bottom: 1rem;
            display: block;
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.chart-toolbar {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 1rem;
    z-index: 10;
}

.chart-tool-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background: rgba(255, 255, 255, 1);
        color: #667eea;
        transform: scale(1.1);
    }

    i {
        font-size: 14px;
    }
}

// 响应式设计
@media (max-height: 1079px) and (min-height: 910px) {
    .dashboard-container {
        overflow-y: auto;
        height: 100vh;
    }

    .dashboard-grid {
        height: auto;
        min-height: 800px;
    }

    .left-panel .chart-panel {
        -webkit-box-flex: 0;
        flex: 0 0 304px;
        margin-bottom: 20px;
    }
}

@media (max-height: 910px) {
    .dashboard-container {
        overflow-y: auto;
        height: 100vh;
    }

    .dashboard-grid {
        height: auto;
        min-height: 800px;
    }
}

@media (max-width: 1200px) {
    .dashboard-grid {
        flex-direction: column;
        height: auto;
        min-height: auto;
    }

    .left-panel {
        flex: none;
        width: 100%;
    }

    .right-panel {
        flex: none;
        width: 100%;
    }

    .pie-charts-section {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }

    .module-tabs {
        padding: 1rem;
    }

    .module-tab {
        padding: 1rem;
        min-width: 100px;
    }

    .pie-charts-section {
        flex-direction: column;
    }

    .panel-header {
        padding: 1rem;

        h3 {
            font-size: 16px;
        }
    }
}

// Loading 样式
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: all 0.3s ease;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-icon {
    font-size: 48px;
    color: #3b82f6;
    animation: spin 2s linear infinite;
}

.loading-text {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #64748b;
    letter-spacing: 0.025em;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

// 全局滚动条样式优化
::-webkit-scrollbar {
    width: 1px;
    height: 1px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 0.5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

// 全屏地图样式
.fullscreen-map {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    margin: 0 !important;
    border-radius: 0 !important;

    .panel-header {
        position: relative !important;
        z-index: 10000 !important;
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
    }

    .map-content {
        height: calc(100vh - 80px) !important;
        border-radius: 0 !important;
    }
}

// 全屏按钮样式
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: auto;
}

.fullscreen-btn {
    padding: 8px !important;
    font-size: 16px !important;
    color: #64748b !important;
    transition: all 0.3s ease !important;

    &:hover {
        color: #3b82f6 !important;
        background-color: rgba(59, 130, 246, 0.1) !important;
    }
}

// 调整panel-header布局
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
        flex: 1;
    }
}

// 地图图例样式调整
.map-legend {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}
</style>
