import { getDeptTrees, getDeptChannelList } from '@/api/video/group';

// Error types for better error handling
const ERROR_TYPES = {
    NETWORK_ERROR: 'NETWORK_ERROR',
    API_ERROR: 'API_ERROR',
    DATA_ERROR: 'DATA_ERROR',
    TIMEOUT_ERROR: 'TIMEOUT_ERROR',
    AUTH_ERROR: 'AUTH_ERROR',
};

// Helper function to categorize errors
const categorizeError = (error) => {
    if (!error) return { type: ERROR_TYPES.API_ERROR, message: '未知错误' };

    if (error.message) {
        if (error.message.includes('Network Error') || error.message.includes('网络')) {
            return { type: ERROR_TYPES.NETWORK_ERROR, message: '网络连接异常，请检查网络设置' };
        }
        if (error.message.includes('timeout') || error.message.includes('超时')) {
            return { type: ERROR_TYPES.TIMEOUT_ERROR, message: '请求超时，请稍后重试' };
        }
        if (error.message.includes('401') || error.message.includes('认证')) {
            return { type: ERROR_TYPES.AUTH_ERROR, message: '认证失败，请重新登录' };
        }
    }

    if (error.response) {
        const status = error.response.status;
        const data = error.response.data;

        switch (status) {
            case 401:
                return { type: ERROR_TYPES.AUTH_ERROR, message: '认证失败，请重新登录' };
            case 403:
                return { type: ERROR_TYPES.AUTH_ERROR, message: '权限不足，无法访问该资源' };
            case 404:
                return { type: ERROR_TYPES.API_ERROR, message: '请求的资源不存在' };
            case 500:
                return { type: ERROR_TYPES.API_ERROR, message: '服务器内部错误，请稍后重试' };
            case 502:
            case 503:
            case 504:
                return { type: ERROR_TYPES.NETWORK_ERROR, message: '服务暂时不可用，请稍后重试' };
            default:
                return {
                    type: ERROR_TYPES.API_ERROR,
                    message: data?.msg || data?.message || `请求失败 (${status})`,
                };
        }
    }

    return {
        type: ERROR_TYPES.API_ERROR,
        message: error.message || error.msg || '请求失败，请稍后重试',
    };
};

// Helper function to validate department tree data
const validateDeptTreeData = (data) => {
    if (!data) {
        throw new Error('部门树数据为空');
    }

    if (!Array.isArray(data)) {
        throw new Error('部门树数据格式错误：应为数组格式');
    }

    // Validate each department node
    for (const dept of data) {
        if (!dept.id && dept.id !== 0) {
            console.warn('部门节点缺少ID:', dept);
        }
        if (!dept.label) {
            console.warn('部门节点缺少标签:', dept);
        }
    }

    return true;
};

// Helper function to validate channel data
const validateChannelData = (data) => {
    if (!data) {
        throw new Error('通道数据为空');
    }

    if (!Array.isArray(data)) {
        throw new Error('通道数据格式错误：应为数组格式');
    }

    // Validate each channel
    for (const channel of data) {
        if (!channel.gbId && !channel.id) {
            console.warn('通道缺少ID:', channel);
        }
        if (!channel.gbName && !channel.name) {
            console.warn('通道缺少名称:', channel);
        }
    }

    return true;
};

const actions = {
    getDeptTrees({ commit }, options = {}) {
        const { retryCount = 0, maxRetries = 2, fallbackData = [] } = options;

        return new Promise((resolve, reject) => {
            getDeptTrees()
                .then((response) => {
                    try {
                        // Handle nested response structure: response.data.data.data
                        let data = response.data;
                        if (data && data.data && Array.isArray(data.data)) {
                            data = data.data;
                        } else if (data && data.data && data.data.data && Array.isArray(data.data.data)) {
                            data = data.data.data;
                        }

                        console.log('原始API响应:', response.data);
                        console.log('解析后的部门数据:', data);

                        // Validate the received data
                        validateDeptTreeData(data);

                        console.log('部门树数据加载成功:', data?.length || 0, '个部门');
                        resolve(data);
                    } catch (validationError) {
                        console.error('部门树数据验证失败:', validationError);

                        // If validation fails but we have some data, try to use it anyway
                        let fallbackData = response.data;
                        if (fallbackData && fallbackData.data && Array.isArray(fallbackData.data)) {
                            fallbackData = fallbackData.data;
                        } else if (fallbackData && fallbackData.data && fallbackData.data.data && Array.isArray(fallbackData.data.data)) {
                            fallbackData = fallbackData.data.data;
                        }

                        if (Array.isArray(fallbackData)) {
                            console.warn('使用未完全验证的部门树数据');
                            resolve(fallbackData);
                        } else {
                            // Use fallback data if available
                            if (fallbackData.length > 0) {
                                console.warn('使用备用部门树数据');
                                resolve(fallbackData);
                            } else {
                                const error = categorizeError(validationError);
                                error.originalError = validationError;
                                reject(error);
                            }
                        }
                    }
                })
                .catch((error) => {
                    const categorizedError = categorizeError(error);
                    console.error('获取部门树失败:', categorizedError);

                    // Implement retry logic for network errors
                    if ((categorizedError.type === ERROR_TYPES.NETWORK_ERROR || categorizedError.type === ERROR_TYPES.TIMEOUT_ERROR) && retryCount < maxRetries) {
                        console.log(`重试获取部门树 (${retryCount + 1}/${maxRetries})`);

                        // Exponential backoff: wait longer between retries
                        const delay = Math.pow(2, retryCount) * 1000;

                        setTimeout(() => {
                            actions
                                .getDeptTrees(
                                    { commit },
                                    {
                                        retryCount: retryCount + 1,
                                        maxRetries,
                                        fallbackData,
                                    }
                                )
                                .then(resolve)
                                .catch(reject);
                        }, delay);
                    } else {
                        // If all retries failed or it's not a retryable error, use fallback
                        if (fallbackData.length > 0) {
                            console.warn('所有重试失败，使用备用部门树数据');
                            resolve(fallbackData);
                        } else {
                            categorizedError.originalError = error;
                            categorizedError.retryCount = retryCount;
                            reject(categorizedError);
                        }
                    }
                });
        });
    },

    getDeptChannelList({ commit }, params, options = {}) {
        const { retryCount = 0, maxRetries = 2, fallbackData = [] } = options;

        return new Promise((resolve, reject) => {
            // Validate input parameters
            if (!params || (!params.deptId && params.deptId !== 0)) {
                const error = {
                    type: ERROR_TYPES.DATA_ERROR,
                    message: '部门ID参数缺失',
                    originalError: new Error('Missing deptId parameter'),
                };
                reject(error);
                return;
            }

            getDeptChannelList(params)
                .then((response) => {
                    try {
                        // Channel list API returns data directly in response.data (not nested like dept tree)
                        const data = response.data;

                        console.log(`部门 ${params.deptId} 原始API响应:`, response.data);
                        console.log(`部门 ${params.deptId} 解析后的通道数据:`, data);

                        // Validate the received data
                        validateChannelData(data);

                        console.log(`部门 ${params.deptId} 的通道数据加载成功:`, data?.length || 0, '个通道');
                        resolve(data);
                    } catch (validationError) {
                        console.error('通道数据验证失败:', validationError);

                        // If validation fails but we have some data, try to use it anyway
                        const fallbackData = response.data;

                        if (Array.isArray(fallbackData)) {
                            console.warn('使用未完全验证的通道数据');
                            resolve(fallbackData);
                        } else {
                            // Use fallback data if available
                            if (options.fallbackData && options.fallbackData.length > 0) {
                                console.warn('使用备用通道数据');
                                resolve(options.fallbackData);
                            } else {
                                const error = categorizeError(validationError);
                                error.originalError = validationError;
                                error.deptId = params.deptId;
                                reject(error);
                            }
                        }
                    }
                })
                .catch((error) => {
                    const categorizedError = categorizeError(error);
                    console.error(`获取部门 ${params.deptId} 通道失败:`, categorizedError);

                    // Implement retry logic for network errors
                    if ((categorizedError.type === ERROR_TYPES.NETWORK_ERROR || categorizedError.type === ERROR_TYPES.TIMEOUT_ERROR) && retryCount < maxRetries) {
                        console.log(`重试获取部门通道 (${retryCount + 1}/${maxRetries})`);

                        // Exponential backoff: wait longer between retries
                        const delay = Math.pow(2, retryCount) * 1000;

                        setTimeout(() => {
                            actions
                                .getDeptChannelList({ commit }, params, {
                                    retryCount: retryCount + 1,
                                    maxRetries,
                                    fallbackData,
                                })
                                .then(resolve)
                                .catch(reject);
                        }, delay);
                    } else {
                        // If all retries failed or it's not a retryable error, use fallback
                        if (fallbackData.length > 0) {
                            console.warn('所有重试失败，使用备用通道数据');
                            resolve(fallbackData);
                        } else {
                            categorizedError.originalError = error;
                            categorizedError.retryCount = retryCount;
                            categorizedError.deptId = params.deptId;
                            reject(categorizedError);
                        }
                    }
                });
        });
    },
    // Department-specific device operations with enhanced error handling
    addDeviceToDepartment({ commit }, params) {
        return new Promise((resolve, reject) => {
            // Validate input parameters
            if (!params || !params.deptId || !params.deviceIds) {
                const error = {
                    type: ERROR_TYPES.DATA_ERROR,
                    message: '添加设备到部门需要部门ID和设备ID列表',
                    originalError: new Error('Missing required parameters: deptId or deviceIds'),
                };
                reject(error);
                return;
            }

            // This would need to be implemented with appropriate API endpoint
            // For now, we'll provide a more informative error with fallback suggestion
            const error = {
                type: ERROR_TYPES.API_ERROR,
                message: '部门设备管理API尚未实现，请使用区域管理功能作为替代',
                originalError: new Error('Department-specific device addition API not yet implemented'),
                fallbackAvailable: true,
                fallbackAction: 'video/commonChanel/addDeviceToRegion',
            };
            reject(error);
        });
    },

    deleteDeviceFromDepartment({ commit }, params) {
        return new Promise((resolve, reject) => {
            // Validate input parameters
            if (!params || !params.deptId || !params.deviceIds) {
                const error = {
                    type: ERROR_TYPES.DATA_ERROR,
                    message: '从部门移除设备需要部门ID和设备ID列表',
                    originalError: new Error('Missing required parameters: deptId or deviceIds'),
                };
                reject(error);
                return;
            }

            // This would need to be implemented with appropriate API endpoint
            // For now, we'll provide a more informative error with fallback suggestion
            const error = {
                type: ERROR_TYPES.API_ERROR,
                message: '部门设备管理API尚未实现，请使用区域管理功能作为替代',
                originalError: new Error('Department-specific device removal API not yet implemented'),
                fallbackAvailable: true,
                fallbackAction: 'video/commonChanel/deleteDeviceFromRegion',
            };
            reject(error);
        });
    },

    // Utility action to check API availability
    checkApiAvailability({ commit }) {
        return new Promise((resolve) => {
            // Test both main APIs
            const tests = [
                getDeptTrees()
                    .then(() => ({ api: 'getDeptTrees', available: true }))
                    .catch(() => ({ api: 'getDeptTrees', available: false })),
                // Skip getDeptChannelList availability check to avoid unnecessary API calls
                // This API should only be called when a specific department is selected
                Promise.resolve({ api: 'getDeptChannelList', available: true }),
            ];

            Promise.allSettled(tests).then((results) => {
                const availability = {};
                results.forEach((result) => {
                    if (result.status === 'fulfilled') {
                        availability[result.value.api] = result.value.available;
                    } else {
                        availability[result.value?.api || 'unknown'] = false;
                    }
                });

                console.log('部门API可用性检查结果:', availability);
                resolve(availability);
            });
        });
    },
};

// State to track error states and fallback data
const state = {
    lastError: null,
    apiAvailability: {
        getDeptTrees: true,
        getDeptChannelList: true,
    },
    fallbackData: {
        deptTrees: [],
        channelLists: {},
    },
};

const mutations = {
    SET_LAST_ERROR(state, error) {
        state.lastError = error;
    },

    CLEAR_LAST_ERROR(state) {
        state.lastError = null;
    },

    SET_API_AVAILABILITY(state, availability) {
        state.apiAvailability = { ...state.apiAvailability, ...availability };
    },

    SET_FALLBACK_DEPT_TREES(state, data) {
        state.fallbackData.deptTrees = data;
    },

    SET_FALLBACK_CHANNEL_LIST(state, { deptId, data }) {
        state.fallbackData.channelLists[deptId] = data;
    },
};

const getters = {
    lastError: (state) => state.lastError,
    isApiAvailable: (state) => (api) => state.apiAvailability[api] !== false,
    getFallbackDeptTrees: (state) => state.fallbackData.deptTrees,
    getFallbackChannelList: (state) => (deptId) => state.fallbackData.channelLists[deptId] || [],
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
    // Export error types for use in components
    ERROR_TYPES,
};
