import LocationDetectionService from './LocationDetectionService.js';
import Location<PERSON>rrorHandler from './LocationErrorHandler.js';

/**
 * LocationUXManager - Manages user experience aspects of location detection
 * Handles UI feedback, notifications, and graceful degradation
 */
class LocationUXManager {
    constructor(options = {}) {
        this.locationService = options.locationService || new LocationDetectionService();
        this.errorHandler = options.errorHandler || new LocationErrorHandler();

        // UX state management
        this.state = {
            isDetecting: false,
            lastDetectedLocation: null,
            lastError: null,
            detectionAttempts: 0,
            maxRetryAttempts: options.maxRetryAttempts || 3,
            retryDelay: options.retryDelay || 2000,
        };

        // Event callbacks
        this.callbacks = {
            onDetectionStart: options.onDetectionStart || (() => {}),
            onDetectionSuccess: options.onDetectionSuccess || (() => {}),
            onDetectionError: options.onDetectionError || (() => {}),
            onDetectionComplete: options.onDetectionComplete || (() => {}),
        };

        // Configuration
        this.config = {
            enableNotifications: options.enableNotifications !== false,
            enableRetry: options.enableRetry !== false,
            showLoadingIndicator: options.showLoadingIndicator !== false,
            notificationDuration: {
                success: options.successNotificationDuration || 4000,
                error: options.errorNotificationDuration || 6000,
            },
        };

        // Bind methods
        this.detectLocationWithUX = this.detectLocationWithUX.bind(this);
        this.retryDetection = this.retryDetection.bind(this);
    }

    /**
     * Detect location with full UX management
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Location data with UX metadata
     */
    async detectLocationWithUX(options = {}) {
        const detectionId = `ux_detection_${Date.now()}`;

        try {
            // Start detection
            this.startDetection(detectionId);

            // Perform location detection
            const locationData = await this.locationService.detectUserLocation();

            // Handle successful detection
            return this.handleDetectionSuccess(locationData, detectionId);
        } catch (error) {
            // Handle detection error
            return this.handleDetectionError(error, detectionId, options);
        }
    }

    /**
     * Start location detection with UX feedback
     * @param {string} detectionId - Unique detection identifier
     */
    startDetection(detectionId) {
        this.state.isDetecting = true;
        this.state.detectionAttempts++;

        // Clear previous error
        this.state.lastError = null;

        // Notify UI about detection start
        this.callbacks.onDetectionStart({
            detectionId,
            attempt: this.state.detectionAttempts,
            isRetry: this.state.detectionAttempts > 1,
        });

        console.log(`Location detection started (attempt ${this.state.detectionAttempts})`);
    }

    /**
     * Handle successful location detection
     * @param {Object} locationData - Detected location data
     * @param {string} detectionId - Detection identifier
     * @returns {Object} Enhanced location data with UX metadata
     */
    handleDetectionSuccess(locationData, detectionId) {
        this.state.isDetecting = false;
        this.state.lastDetectedLocation = locationData;
        this.state.detectionAttempts = 0; // Reset attempts on success

        // Create enhanced result with UX metadata
        const result = {
            ...locationData,
            ux: {
                detectionId,
                wasRetry: this.state.detectionAttempts > 1,
                userFriendlyLocation: this.getUserFriendlyLocationName(locationData),
                shouldShowNotification: this.config.enableNotifications && locationData.source !== 'default',
            },
        };

        // Notify UI about success
        this.callbacks.onDetectionSuccess(result);
        this.callbacks.onDetectionComplete(result);

        console.log('Location detection successful:', {
            source: locationData.source,
            city: locationData.city,
            userFriendly: result.ux.userFriendlyLocation,
        });

        return result;
    }

    /**
     * Handle location detection error with graceful degradation
     * @param {Error} error - Detection error
     * @param {string} detectionId - Detection identifier
     * @param {Object} options - Detection options
     * @returns {Object} Fallback location data with error information
     */
    async handleDetectionError(error, detectionId, options = {}) {
        this.state.lastError = error;

        // Get user-friendly error message
        const userFriendlyMessage = this.getUserFriendlyErrorMessage(error);

        // Check if we should retry
        const shouldRetry = this.shouldRetryDetection(error, options);

        if (shouldRetry) {
            console.warn(`Location detection failed, retrying in ${this.config.retryDelay}ms:`, error.message);

            // Schedule retry
            return this.scheduleRetry(detectionId, options);
        }

        // No retry, use graceful degradation
        this.state.isDetecting = false;

        // Get default location as fallback
        const fallbackLocation = this.locationService.getDefaultLocation();

        const result = {
            ...fallbackLocation,
            ux: {
                detectionId,
                hasError: true,
                error: error,
                userFriendlyError: userFriendlyMessage,
                shouldShowErrorNotification: this.config.enableNotifications,
                gracefulDegradation: true,
            },
        };

        // Notify UI about error
        this.callbacks.onDetectionError({
            error,
            userFriendlyMessage,
            fallbackLocation: result,
            canRetry: false,
        });

        this.callbacks.onDetectionComplete(result);

        console.error('Location detection failed, using default location:', {
            error: error.message,
            fallback: fallbackLocation.city,
        });

        return result;
    }

    /**
     * Schedule a retry attempt
     * @param {string} detectionId - Detection identifier
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Retry result
     */
    async scheduleRetry(detectionId, options) {
        return new Promise((resolve) => {
            setTimeout(async () => {
                try {
                    const result = await this.detectLocationWithUX(options);
                    resolve(result);
                } catch (retryError) {
                    // If retry also fails, handle as final error
                    const finalResult = await this.handleDetectionError(retryError, detectionId, { ...options, disableRetry: true });
                    resolve(finalResult);
                }
            }, this.config.retryDelay);
        });
    }

    /**
     * Determine if detection should be retried
     * @param {Error} error - Detection error
     * @param {Object} options - Detection options
     * @returns {boolean} Whether to retry
     */
    shouldRetryDetection(error, options) {
        // Don't retry if disabled
        if (!this.config.enableRetry || options.disableRetry) {
            return false;
        }

        // Don't retry if max attempts reached
        if (this.state.detectionAttempts >= this.state.maxRetryAttempts) {
            return false;
        }

        // Don't retry for certain error types
        const nonRetryableErrors = ['PERMISSION_DENIED', 'PERMISSION_DENIED_PERMANENTLY'];
        if (nonRetryableErrors.includes(error.type)) {
            return false;
        }

        // Retry for network errors and timeouts
        const retryableErrors = ['TIMEOUT', 'NETWORK_ERROR', 'POSITION_UNAVAILABLE'];
        return retryableErrors.includes(error.type);
    }

    /**
     * Manually trigger location detection retry
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Detection result
     */
    async retryDetection(options = {}) {
        // Reset attempts for manual retry
        this.state.detectionAttempts = 0;
        this.state.lastError = null;

        return this.detectLocationWithUX(options);
    }

    /**
     * Get user-friendly location name for display
     * @param {Object} locationData - Location data
     * @returns {string} User-friendly location name
     */
    getUserFriendlyLocationName(locationData) {
        if (!locationData) return '';

        // Use city name if available
        if (locationData.city && locationData.city !== 'Default Location') {
            return locationData.city;
        }

        // For GPS coordinates without city name
        if (locationData.source === 'gps' && locationData.latitude && locationData.longitude) {
            return `位置 (${locationData.latitude.toFixed(2)}, ${locationData.longitude.toFixed(2)})`;
        }

        // For IP-based location
        if (locationData.source === 'ip' && locationData.region) {
            return locationData.region;
        }

        // Fallback based on source
        const sourceNames = {
            gps: '当前位置',
            ip: '大致位置',
            default: '默认位置',
        };

        return sourceNames[locationData.source] || '未知位置';
    }

    /**
     * Get user-friendly error message
     * @param {Error} error - Error object
     * @returns {string} User-friendly error message
     */
    getUserFriendlyErrorMessage(error) {
        const errorMessages = {
            PERMISSION_DENIED: '位置访问被拒绝，使用默认位置',
            PERMISSION_DENIED_PERMANENTLY: '位置权限已永久拒绝，请在浏览器设置中启用',
            POSITION_UNAVAILABLE: 'GPS信号不可用，使用默认位置',
            TIMEOUT: '定位超时，使用默认位置',
            NETWORK_ERROR: '网络连接失败，使用默认位置',
            GEOLOCATION_NOT_SUPPORTED: '浏览器不支持定位功能',
            INSECURE_CONTEXT: '定位功能需要HTTPS连接',
        };

        return errorMessages[error.type] || '定位失败，使用默认位置';
    }

    /**
     * Get current UX state
     * @returns {Object} Current UX state
     */
    getState() {
        return {
            ...this.state,
            canRetry: this.state.detectionAttempts < this.state.maxRetryAttempts && !this.state.isDetecting,
        };
    }

    /**
     * Update UX configuration
     * @param {Object} newConfig - New configuration options
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * Update callbacks
     * @param {Object} newCallbacks - New callback functions
     */
    updateCallbacks(newCallbacks) {
        this.callbacks = { ...this.callbacks, ...newCallbacks };
    }

    /**
     * Reset UX state
     */
    resetState() {
        this.state = {
            isDetecting: false,
            lastDetectedLocation: null,
            lastError: null,
            detectionAttempts: 0,
            maxRetryAttempts: this.state.maxRetryAttempts,
            retryDelay: this.state.retryDelay,
        };
    }

    /**
     * Check if location detection is currently in progress
     * @returns {boolean} Whether detection is in progress
     */
    isDetecting() {
        return this.state.isDetecting;
    }

    /**
     * Get the last detected location
     * @returns {Object|null} Last detected location data
     */
    getLastDetectedLocation() {
        return this.state.lastDetectedLocation;
    }

    /**
     * Get the last error that occurred
     * @returns {Error|null} Last error
     */
    getLastError() {
        return this.state.lastError;
    }

    /**
     * Clean up resources
     */
    destroy() {
        // Reset state
        this.resetState();

        // Clear callbacks
        this.callbacks = {
            onDetectionStart: () => {},
            onDetectionSuccess: () => {},
            onDetectionError: () => {},
            onDetectionComplete: () => {},
        };

        // Clear location service cache if needed
        if (this.locationService && typeof this.locationService.clearCache === 'function') {
            this.locationService.clearCache();
        }
    }
}

export default LocationUXManager;
