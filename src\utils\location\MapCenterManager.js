/**
 * MapCenterManager - 地图中心点管理器
 *
 * 负责处理地图中心点的更新和平滑过渡动画
 * 集成现有的城市坐标映射系统
 * 支持在不干扰用户交互的情况下更新地图中心
 */

export class MapCenterManager {
    constructor(mapInstance, options = {}) {
        this.map = mapInstance;
        this.options = {
            animationDuration: options.animationDuration || 1000, // 动画持续时间(ms)
            defaultZoomLevel: options.defaultZoomLevel || 12,
            enableAnimation: options.enableAnimation !== false,
            ...options,
        };

        // 默认中心点 - 合肥
        this.defaultCenter = { lng: 117.27, lat: 31.86 };

        // 用户交互状态跟踪
        this.userInteracting = false;
        this.lastUserInteraction = 0;

        // 绑定地图事件监听器
        this.bindMapEvents();

        // 城市坐标映射
        this.cityCoordinates = this.initializeCityCoordinates();
    }

    /**
     * 绑定地图事件监听器以跟踪用户交互
     */
    bindMapEvents() {
        if (!this.map) return;

        try {
            // 监听用户交互事件
            const interactionEvents = ['dragstart', 'zoomstart', 'click', 'dblclick'];

            interactionEvents.forEach((eventType) => {
                this.map.addEventListener(eventType, () => {
                    this.userInteracting = true;
                    this.lastUserInteraction = Date.now();
                });
            });

            // 监听交互结束事件
            const endEvents = ['dragend', 'zoomend'];
            endEvents.forEach((eventType) => {
                this.map.addEventListener(eventType, () => {
                    // 延迟重置交互状态，避免连续操作被误判
                    setTimeout(() => {
                        this.userInteracting = false;
                    }, 500);
                });
            });
        } catch (error) {
            console.warn('绑定地图事件监听器失败:', error);
        }
    }

    /**
     * 初始化城市坐标映射
     * 包含主要城市的经纬度坐标
     */
    initializeCityCoordinates() {
        return {
            // 直辖市
            北京: { lng: 116.4074, lat: 39.9042, zoom: 11 },
            上海: { lng: 121.4737, lat: 31.2304, zoom: 11 },
            天津: { lng: 117.1901, lat: 39.1084, zoom: 11 },
            重庆: { lng: 106.5516, lat: 29.563, zoom: 11 },

            // 省会城市
            合肥: { lng: 117.27, lat: 31.86, zoom: 12 },
            南京: { lng: 118.7969, lat: 32.0603, zoom: 11 },
            杭州: { lng: 120.1551, lat: 30.2741, zoom: 11 },
            济南: { lng: 117.0009, lat: 36.6758, zoom: 11 },
            广州: { lng: 113.2644, lat: 23.1291, zoom: 11 },
            深圳: { lng: 114.0579, lat: 22.5431, zoom: 11 },
            武汉: { lng: 114.2985, lat: 30.5844, zoom: 11 },
            成都: { lng: 104.0665, lat: 30.5723, zoom: 11 },
            西安: { lng: 108.9398, lat: 34.3412, zoom: 11 },
            郑州: { lng: 113.6254, lat: 34.7466, zoom: 11 },
            长沙: { lng: 112.9388, lat: 28.2282, zoom: 11 },
            南昌: { lng: 115.8921, lat: 28.6765, zoom: 11 },
            福州: { lng: 119.3063, lat: 26.0745, zoom: 11 },
            石家庄: { lng: 114.5149, lat: 38.0428, zoom: 11 },
            太原: { lng: 112.5489, lat: 37.8706, zoom: 11 },
            沈阳: { lng: 123.4315, lat: 41.8057, zoom: 11 },
            长春: { lng: 125.3245, lat: 43.8171, zoom: 11 },
            哈尔滨: { lng: 126.5358, lat: 45.8023, zoom: 11 },
            昆明: { lng: 102.8329, lat: 24.8801, zoom: 11 },
            贵阳: { lng: 106.7135, lat: 26.5783, zoom: 11 },
            拉萨: { lng: 91.1409, lat: 29.6456, zoom: 11 },
            乌鲁木齐: { lng: 87.6168, lat: 43.8256, zoom: 11 },
            银川: { lng: 106.2309, lat: 38.4872, zoom: 11 },
            西宁: { lng: 101.7782, lat: 36.6171, zoom: 11 },
            呼和浩特: { lng: 111.7519, lat: 40.8427, zoom: 11 },
            南宁: { lng: 108.3669, lat: 22.817, zoom: 11 },
            海口: { lng: 110.3312, lat: 20.0311, zoom: 11 },

            // 其他重要城市
            苏州: { lng: 120.6197, lat: 31.3117, zoom: 12 },
            无锡: { lng: 120.3019, lat: 31.5747, zoom: 12 },
            常州: { lng: 119.9463, lat: 31.7728, zoom: 12 },
            宁波: { lng: 121.544, lat: 29.8683, zoom: 12 },
            温州: { lng: 120.6994, lat: 27.9944, zoom: 12 },
            青岛: { lng: 120.3826, lat: 36.0671, zoom: 11 },
            大连: { lng: 121.6147, lat: 38.914, zoom: 11 },
            厦门: { lng: 118.1689, lat: 24.4797, zoom: 12 },
            珠海: { lng: 113.5767, lat: 22.2707, zoom: 12 },
            东莞: { lng: 113.7518, lat: 23.0489, zoom: 12 },
            佛山: { lng: 113.122, lat: 23.0288, zoom: 12 },
        };
    }

    /**
     * 根据位置数据更新地图中心
     * @param {Object} locationData - 位置数据
     * @param {number} locationData.latitude - 纬度
     * @param {number} locationData.longitude - 经度
     * @param {string} locationData.city - 城市名称
     * @param {string} locationData.source - 位置来源 ('gps'|'ip'|'default')
     * @param {Object} options - 更新选项
     * @returns {Promise<boolean>} - 更新是否成功
     */
    async updateCenterFromLocation(locationData, options = {}) {
        if (!this.map || !locationData) {
            console.warn('地图实例或位置数据无效');
            return false;
        }

        try {
            // 检查用户是否正在交互
            if (this.shouldSkipUpdate()) {
                console.log('用户正在交互，跳过地图中心更新');
                return false;
            }

            const updateOptions = {
                enableAnimation: this.options.enableAnimation,
                zoomLevel: this.options.defaultZoomLevel,
                ...options,
            };

            let targetCoordinates;
            let targetZoom = updateOptions.zoomLevel;

            // 优先使用城市坐标映射
            if (locationData.city) {
                const cityCoords = this.getCityCoordinates(locationData.city);
                if (cityCoords) {
                    targetCoordinates = { lng: cityCoords.lng, lat: cityCoords.lat };
                    targetZoom = cityCoords.zoom || targetZoom;
                    console.log(`使用城市坐标映射: ${locationData.city}`, targetCoordinates);
                }
            }

            // 如果没有城市映射，使用精确坐标
            if (!targetCoordinates && locationData.longitude && locationData.latitude) {
                targetCoordinates = {
                    lng: parseFloat(locationData.longitude),
                    lat: parseFloat(locationData.latitude),
                };
                // GPS坐标使用更高的缩放级别
                if (locationData.source === 'gps') {
                    targetZoom = Math.max(targetZoom, 13);
                }
                console.log('使用精确坐标:', targetCoordinates);
            }

            // 如果都没有，使用默认中心
            if (!targetCoordinates) {
                targetCoordinates = this.defaultCenter;
                console.log('使用默认中心点:', targetCoordinates);
            }

            // 执行地图中心更新
            const success = await this.animateToCenter(targetCoordinates, targetZoom, updateOptions);

            if (success) {
                console.log(`地图中心已更新到: ${locationData.city || '指定位置'} (${locationData.source})`);
            }

            return success;
        } catch (error) {
            console.error('更新地图中心失败:', error);
            return false;
        }
    }

    /**
     * 获取城市坐标
     * @param {string} cityName - 城市名称
     * @returns {Object|null} - 城市坐标信息
     */
    getCityCoordinates(cityName) {
        if (!cityName || typeof cityName !== 'string') {
            return null;
        }

        // 直接匹配
        if (this.cityCoordinates[cityName]) {
            return this.cityCoordinates[cityName];
        }

        // 模糊匹配 - 去掉"市"、"区"等后缀
        const cleanCityName = cityName.replace(/[市区县]/g, '');
        for (const [key, coords] of Object.entries(this.cityCoordinates)) {
            if (key.includes(cleanCityName) || cleanCityName.includes(key)) {
                return coords;
            }
        }

        return null;
    }

    /**
     * 平滑动画到指定中心点
     * @param {Object} coordinates - 目标坐标 {lng, lat}
     * @param {number} zoomLevel - 缩放级别
     * @param {Object} options - 动画选项
     * @returns {Promise<boolean>} - 动画是否成功完成
     */
    async animateToCenter(coordinates, zoomLevel = this.options.defaultZoomLevel, options = {}) {
        if (!this.map || !coordinates) {
            return false;
        }

        try {
            const { lng, lat } = coordinates;
            const targetPoint = new T.LngLat(lng, lat);

            // 检查坐标有效性
            if (isNaN(lng) || isNaN(lat)) {
                console.warn('无效的坐标:', coordinates);
                return false;
            }

            // 如果禁用动画或用户正在交互，直接设置
            if (!options.enableAnimation || this.userInteracting) {
                this.map.centerAndZoom(targetPoint, zoomLevel);
                return true;
            }

            // 使用天地图的平滑动画
            return new Promise((resolve) => {
                try {
                    // 天地图的panTo方法提供平滑过渡
                    this.map.panTo(targetPoint);

                    // 如果需要调整缩放级别
                    const currentZoom = this.map.getZoom();
                    if (Math.abs(currentZoom - zoomLevel) > 0.5) {
                        setTimeout(() => {
                            this.map.setZoom(zoomLevel);
                        }, this.options.animationDuration / 2);
                    }

                    // 等待动画完成
                    setTimeout(() => {
                        resolve(true);
                    }, this.options.animationDuration);
                } catch (error) {
                    console.warn('动画执行失败，使用直接设置:', error);
                    this.map.centerAndZoom(targetPoint, zoomLevel);
                    resolve(true);
                }
            });
        } catch (error) {
            console.error('地图中心动画失败:', error);
            return false;
        }
    }

    /**
     * 检查是否应该跳过更新
     * @returns {boolean} - 是否应该跳过
     */
    shouldSkipUpdate() {
        // 如果用户正在交互，跳过更新
        if (this.userInteracting) {
            return true;
        }

        // 如果最近有用户交互（5秒内），跳过更新
        const timeSinceLastInteraction = Date.now() - this.lastUserInteraction;
        if (timeSinceLastInteraction < 5000) {
            return true;
        }

        return false;
    }

    /**
     * 强制更新地图中心（忽略用户交互状态）
     * @param {Object} coordinates - 目标坐标
     * @param {number} zoomLevel - 缩放级别
     * @returns {Promise<boolean>} - 更新是否成功
     */
    async forceUpdateCenter(coordinates, zoomLevel = this.options.defaultZoomLevel) {
        if (!this.map || !coordinates) {
            return false;
        }

        try {
            const { lng, lat } = coordinates;
            const targetPoint = new T.LngLat(lng, lat);
            this.map.centerAndZoom(targetPoint, zoomLevel);
            return true;
        } catch (error) {
            console.error('强制更新地图中心失败:', error);
            return false;
        }
    }

    /**
     * 获取当前地图中心
     * @returns {Object|null} - 当前中心坐标
     */
    getCurrentCenter() {
        if (!this.map) {
            return null;
        }

        try {
            const center = this.map.getCenter();
            return {
                lng: center.getLng(),
                lat: center.getLat(),
                zoom: this.map.getZoom(),
            };
        } catch (error) {
            console.error('获取当前地图中心失败:', error);
            return null;
        }
    }

    /**
     * 重置到默认中心
     * @param {boolean} animate - 是否使用动画
     * @returns {Promise<boolean>} - 重置是否成功
     */
    async resetToDefault(animate = true) {
        const options = { enableAnimation: animate };
        return await this.animateToCenter(this.defaultCenter, this.options.defaultZoomLevel, options);
    }

    /**
     * 添加城市坐标映射
     * @param {string} cityName - 城市名称
     * @param {Object} coordinates - 坐标信息 {lng, lat, zoom?}
     */
    addCityCoordinates(cityName, coordinates) {
        if (cityName && coordinates && coordinates.lng && coordinates.lat) {
            this.cityCoordinates[cityName] = {
                lng: coordinates.lng,
                lat: coordinates.lat,
                zoom: coordinates.zoom || this.options.defaultZoomLevel,
            };
        }
    }

    /**
     * 销毁管理器，清理事件监听器
     */
    destroy() {
        // 清理事件监听器
        if (this.map) {
            try {
                // 天地图的事件清理方式可能有限，这里做基本清理
                this.map = null;
            } catch (error) {
                console.warn('清理地图事件监听器失败:', error);
            }
        }

        this.userInteracting = false;
        this.lastUserInteraction = 0;
    }
}

export default MapCenterManager;
