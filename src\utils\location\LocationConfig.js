/**
 * Location Detection Configuration
 * Centralizes all location detection settings from environment variables
 */

class LocationConfig {
    constructor() {
        this.config = this.loadConfiguration();
    }

    /**
     * Load configuration from environment variables
     * @returns {Object} Configuration object
     */
    loadConfiguration() {
        return {
            // Feature flags
            enabled: this.parseBoolean(process.env.VUE_APP_ENABLE_LOCATION_DETECTION, true),
            enableCache: this.parseBoolean(process.env.VUE_APP_ENABLE_LOCATION_CACHE, true),
            enableIPFallback: this.parseBoolean(process.env.VUE_APP_ENABLE_IP_FALLBACK, true),
            enableHighAccuracy: this.parseBoolean(process.env.VUE_APP_LOCATION_HIGH_ACCURACY, false),

            // Timeout settings
            timeout: this.parseNumber(process.env.VUE_APP_LOCATION_TIMEOUT, 10000),
            gpsTimeout: this.parseNumber(process.env.VUE_APP_GPS_TIMEOUT, 8000),
            ipTimeout: this.parseNumber(process.env.VUE_APP_IP_TIMEOUT, 5000),

            // API configuration
            ipApiKey: process.env.VUE_APP_IP_LOCATION_API_KEY || '',
            ipApiEndpoints: this.getIPApiEndpoints(),

            // Cache settings
            cacheMaxAge: this.parseNumber(process.env.VUE_APP_LOCATION_CACHE_MAX_AGE, 3600000), // 1 hour default
            sessionKey: 'user_location_cache',

            // Map settings
            defaultCenter: {
                lng: this.parseNumber(process.env.VUE_APP_DEFAULT_MAP_LNG, 117.27),
                lat: this.parseNumber(process.env.VUE_APP_DEFAULT_MAP_LAT, 31.86),
            },
            defaultZoom: this.parseNumber(process.env.VUE_APP_DEFAULT_MAP_ZOOM, 12),

            // Debug settings
            enableDebugLogging: this.parseBoolean(process.env.VUE_APP_LOCATION_DEBUG, false),
            enablePerformanceMonitoring: this.parseBoolean(process.env.VUE_APP_LOCATION_PERFORMANCE_MONITORING, false),
        };
    }

    /**
     * Get IP geolocation API endpoints configuration
     * @returns {Array} Array of API endpoint configurations
     */
    getIPApiEndpoints() {
        const defaultEndpoints = [
            {
                url: 'https://ipapi.co/json/',
                requiresKey: false,
                timeout: 5000,
                priority: 1,
            },
            {
                url: 'https://ip-api.com/json/',
                requiresKey: false,
                timeout: 5000,
                priority: 2,
            },
            {
                url: 'https://ipinfo.io/json',
                requiresKey: true,
                timeout: 5000,
                priority: 3,
            },
        ];

        // Allow custom endpoints via environment variable
        const customEndpoints = process.env.VUE_APP_IP_API_ENDPOINTS;
        if (customEndpoints) {
            try {
                return JSON.parse(customEndpoints);
            } catch (error) {
                console.warn('Invalid IP API endpoints configuration, using defaults:', error);
            }
        }

        return defaultEndpoints;
    }

    /**
     * Parse boolean value from environment variable
     * @param {string} value - Environment variable value
     * @param {boolean} defaultValue - Default value if parsing fails
     * @returns {boolean}
     */
    parseBoolean(value, defaultValue = false) {
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') {
            const lowerValue = value.toLowerCase();
            if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes') {
                return true;
            }
            if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no') {
                return false;
            }
        }
        return defaultValue;
    }

    /**
     * Parse number value from environment variable
     * @param {string} value - Environment variable value
     * @param {number} defaultValue - Default value if parsing fails
     * @returns {number}
     */
    parseNumber(value, defaultValue = 0) {
        if (value === null || value === undefined) {
            return defaultValue;
        }
        const parsed = Number(value);
        return isNaN(parsed) ? defaultValue : parsed;
    }

    /**
     * Get current configuration
     * @returns {Object} Current configuration object
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * Get specific configuration value
     * @param {string} key - Configuration key
     * @param {*} defaultValue - Default value if key not found
     * @returns {*} Configuration value
     */
    get(key, defaultValue = null) {
        return this.config[key] !== undefined ? this.config[key] : defaultValue;
    }

    /**
     * Check if location detection is enabled
     * @returns {boolean}
     */
    isEnabled() {
        return this.config.enabled;
    }

    /**
     * Check if IP fallback is enabled
     * @returns {boolean}
     */
    isIPFallbackEnabled() {
        return this.config.enableIPFallback;
    }

    /**
     * Check if caching is enabled
     * @returns {boolean}
     */
    isCacheEnabled() {
        return this.config.enableCache;
    }

    /**
     * Get timeout configuration for specific operation
     * @param {string} operation - Operation type ('gps', 'ip', 'total')
     * @returns {number} Timeout in milliseconds
     */
    getTimeout(operation = 'total') {
        switch (operation) {
            case 'gps':
                return this.config.gpsTimeout;
            case 'ip':
                return this.config.ipTimeout;
            case 'total':
            default:
                return this.config.timeout;
        }
    }

    /**
     * Get geolocation options for browser API
     * @returns {Object} Geolocation options
     */
    getGeolocationOptions() {
        return {
            enableHighAccuracy: this.config.enableHighAccuracy,
            timeout: this.config.gpsTimeout,
            maximumAge: this.config.enableCache ? this.config.cacheMaxAge : 0,
        };
    }

    /**
     * Validate configuration
     * @returns {Object} Validation result with isValid flag and errors array
     */
    validate() {
        const errors = [];

        // Validate timeout values
        if (this.config.timeout <= 0) {
            errors.push('Location timeout must be greater than 0');
        }

        if (this.config.gpsTimeout <= 0) {
            errors.push('GPS timeout must be greater than 0');
        }

        if (this.config.ipTimeout <= 0) {
            errors.push('IP timeout must be greater than 0');
        }

        // Validate API endpoints
        if (this.config.enableIPFallback && this.config.ipApiEndpoints.length === 0) {
            errors.push('IP fallback is enabled but no API endpoints configured');
        }

        // Validate default center coordinates
        const { lng, lat } = this.config.defaultCenter;
        if (lng < -180 || lng > 180) {
            errors.push('Default longitude must be between -180 and 180');
        }

        if (lat < -90 || lat > 90) {
            errors.push('Default latitude must be between -90 and 90');
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    /**
     * Log current configuration (for debugging)
     */
    logConfiguration() {
        if (this.config.enableDebugLogging) {
            console.group('Location Detection Configuration');
            console.log('Enabled:', this.config.enabled);
            console.log('Cache Enabled:', this.config.enableCache);
            console.log('IP Fallback Enabled:', this.config.enableIPFallback);
            console.log('High Accuracy:', this.config.enableHighAccuracy);
            console.log('Timeout:', this.config.timeout + 'ms');
            console.log('GPS Timeout:', this.config.gpsTimeout + 'ms');
            console.log('IP Timeout:', this.config.ipTimeout + 'ms');
            console.log('Default Center:', this.config.defaultCenter);
            console.log('API Endpoints:', this.config.ipApiEndpoints.length);
            console.groupEnd();
        }
    }
}

// Create singleton instance
const locationConfig = new LocationConfig();

// Validate configuration on startup
const validation = locationConfig.validate();
if (!validation.isValid) {
    console.error('Location Detection Configuration Errors:', validation.errors);
}

// Log configuration in development
if (process.env.NODE_ENV === 'development') {
    locationConfig.logConfiguration();
}

export default locationConfig;
