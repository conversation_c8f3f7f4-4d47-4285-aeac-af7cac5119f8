# 页面标题
VUE_APP_TITLE = 辉采科技物联网云平台

# 生产环境配置
ENV = 'production'

# 生产环境
VUE_APP_BASE_API = '/prod-api'
VIDEO_BASE_API = '/video-api'

# 视频服务地址 - 生产环境
VUE_APP_VIDEO_SERVER_URL = 'https://iot.hclight.com:19090'

# 生产环境
VUE_APP_SERVER_API_URL = 'https://iot.hclight.com/prod-api/'
# VUE_APP_SERVER_API_URL = 'https://iot.hclight.com/prod-api'
# Mqtt消息服务器连接地址
VUE_APP_MQTT_SERVER_URL = 'wss://iot.hclight.com/websocket-iot/mqtt'
# 百度地图AK
VUE_APP_BAI_DU_AK = 'ZxCQ6XAWHJTWinn4HuJJPg04cIve2G4t'

 # 天地图API KEY，需要去天地图开发者中心申请
# VUE_APP_TIANDITU_KEY='a139a28d19e779d05cd34fb7fd28db08'
VUE_APP_TIANDITU_KEY = 'a139a28d19e779d05cd34fb7fd28db08'

# 位置检测配置
VUE_APP_ENABLE_LOCATION_DETECTION = true
VUE_APP_LOCATION_TIMEOUT = 8000
VUE_APP_ENABLE_LOCATION_CACHE = true
VUE_APP_ENABLE_IP_FALLBACK = true
VUE_APP_LOCATION_HIGH_ACCURACY = false
VUE_APP_IP_LOCATION_API_KEY = ''
