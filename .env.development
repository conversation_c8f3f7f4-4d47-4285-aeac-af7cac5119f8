# 页面标题
VUE_APP_TITLE = 辉采科技物联网云平台

# 开发环境配置
ENV = 'development'

# 开发环境
VUE_APP_BASE_API = '/dev-api'
VIDEO_BASE_API = '/video-api'

# 视频服务地址
VUE_APP_VIDEO_SERVER_URL = 'http://************:19090'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# 后端接口地址
# 闫yefa
# VUE_APP_SERVER_API_URL = 'http://*************:8081/'

# 孙wei
VUE_APP_SERVER_API_URL = 'http://************:8081/'

# 生成
# VUE_APP_SERVER_API_URL = 'http://*************:8081/'

# Mqtt消息服务器连接地址
VUE_APP_MQTT_SERVER_URL = 'ws://localhost:8083/mqtt'

# 百度地图AK
VUE_APP_BAI_DU_AK = 'ZxCQ6XAWHJTWinn4HuJJPg04cIve2G4t'


 # 天地图API KEY，需要去天地图开发者中心申请
# VUE_APP_TIANDITU_KEY='a139a28d19e779d05cd34fb7fd28db08'
VUE_APP_TIANDITU_KEY=a139a28d19e779d05cd34fb7fd28db08

# 位置检测配置
VUE_APP_ENABLE_LOCATION_DETECTION = true
VUE_APP_LOCATION_TIMEOUT = 10000
VUE_APP_ENABLE_LOCATION_CACHE = true
VUE_APP_ENABLE_IP_FALLBACK = true
VUE_APP_LOCATION_HIGH_ACCURACY = false
VUE_APP_IP_LOCATION_API_KEY = ''