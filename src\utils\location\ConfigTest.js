/**
 * Configuration Test Utility
 * Provides methods to test and verify location detection configuration
 */

import LocationConfig from './LocationConfig.js';
import FeatureFlags from './FeatureFlags.js';
import ConfigValidator from './ConfigValidator.js';

class ConfigTest {
    /**
     * Run comprehensive configuration test
     * @returns {Object} Test results
     */
    static async runConfigurationTest() {
        const results = {
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
            tests: {},
            summary: {
                passed: 0,
                failed: 0,
                warnings: 0,
            },
        };

        console.group('🧪 Location Detection Configuration Test');

        // Test 1: Configuration Loading
        results.tests.configLoading = this.testConfigurationLoading();

        // Test 2: Feature Flags
        results.tests.featureFlags = this.testFeatureFlags();

        // Test 3: Environment Variables
        results.tests.environmentVariables = this.testEnvironmentVariables();

        // Test 4: API Endpoints
        results.tests.apiEndpoints = await this.testApiEndpoints();

        // Test 5: Browser Compatibility
        results.tests.browserCompatibility = this.testBrowserCompatibility();

        // Test 6: Configuration Validation
        results.tests.configValidation = this.testConfigurationValidation();

        // Calculate summary
        Object.values(results.tests).forEach((test) => {
            if (test.passed) {
                results.summary.passed++;
            } else {
                results.summary.failed++;
            }
            results.summary.warnings += test.warnings?.length || 0;
        });

        console.log('📊 Test Summary:', results.summary);
        console.groupEnd();

        return results;
    }

    /**
     * Test configuration loading
     * @returns {Object} Test result
     */
    static testConfigurationLoading() {
        const test = {
            name: 'Configuration Loading',
            passed: false,
            errors: [],
            warnings: [],
        };

        try {
            const config = LocationConfig.getConfig();

            // Check required properties
            const requiredProps = ['enabled', 'timeout', 'defaultCenter', 'ipApiEndpoints'];
            const missingProps = requiredProps.filter((prop) => config[prop] === undefined);

            if (missingProps.length > 0) {
                test.errors.push(`Missing required properties: ${missingProps.join(', ')}`);
            } else {
                test.passed = true;
            }

            console.log('✅ Configuration loaded successfully');
        } catch (error) {
            test.errors.push(`Configuration loading failed: ${error.message}`);
            console.error('❌ Configuration loading failed:', error.message);
        }

        return test;
    }

    /**
     * Test feature flags
     * @returns {Object} Test result
     */
    static testFeatureFlags() {
        const test = {
            name: 'Feature Flags',
            passed: false,
            errors: [],
            warnings: [],
        };

        try {
            const flags = FeatureFlags.getFeatureFlagStatus();

            // Check if at least one detection method is available
            if (!flags.locationDetection) {
                test.warnings.push('Location detection is disabled');
            } else if (!flags.gpsDetection && !flags.ipDetection) {
                test.errors.push('No location detection methods are available');
            } else {
                test.passed = true;
            }

            // Check browser support
            if (!flags.browserSupport.geolocation) {
                test.warnings.push('Geolocation API not supported');
            }

            if (!flags.browserSupport.secureContext) {
                test.warnings.push('Not running in secure context - GPS may not work');
            }

            console.log('✅ Feature flags checked');
        } catch (error) {
            test.errors.push(`Feature flag check failed: ${error.message}`);
            console.error('❌ Feature flag check failed:', error.message);
        }

        return test;
    }

    /**
     * Test environment variables
     * @returns {Object} Test result
     */
    static testEnvironmentVariables() {
        const test = {
            name: 'Environment Variables',
            passed: true,
            errors: [],
            warnings: [],
        };

        const envVars = ['VUE_APP_ENABLE_LOCATION_DETECTION', 'VUE_APP_LOCATION_TIMEOUT', 'VUE_APP_ENABLE_LOCATION_CACHE', 'VUE_APP_ENABLE_IP_FALLBACK'];

        envVars.forEach((varName) => {
            const value = process.env[varName];
            if (value === undefined) {
                test.warnings.push(`Environment variable ${varName} is not set`);
            }
        });

        // Check for conflicting settings
        if (process.env.VUE_APP_ENABLE_LOCATION_DETECTION === 'false' && process.env.VUE_APP_ENABLE_IP_FALLBACK === 'true') {
            test.warnings.push('Location detection is disabled but IP fallback is enabled');
        }

        console.log('✅ Environment variables checked');
        return test;
    }

    /**
     * Test API endpoints availability
     * @returns {Promise<Object>} Test result
     */
    static async testApiEndpoints() {
        const test = {
            name: 'API Endpoints',
            passed: false,
            errors: [],
            warnings: [],
            endpointResults: [],
        };

        try {
            const config = LocationConfig.getConfig();
            const endpoints = config.ipApiEndpoints || [];

            if (endpoints.length === 0) {
                test.errors.push('No API endpoints configured');
                return test;
            }

            let workingEndpoints = 0;

            for (const endpoint of endpoints.slice(0, 2)) {
                // Test only first 2 to avoid rate limits
                const endpointTest = {
                    url: endpoint.url,
                    available: false,
                    responseTime: null,
                    error: null,
                };

                try {
                    const startTime = Date.now();
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000);

                    const response = await fetch(endpoint.url, {
                        method: 'HEAD', // Use HEAD to avoid consuming API quota
                        signal: controller.signal,
                    });

                    clearTimeout(timeoutId);
                    endpointTest.responseTime = Date.now() - startTime;
                    endpointTest.available = response.ok;

                    if (response.ok) {
                        workingEndpoints++;
                    }
                } catch (error) {
                    endpointTest.error = error.message;
                }

                test.endpointResults.push(endpointTest);
            }

            if (workingEndpoints > 0) {
                test.passed = true;
                console.log(`✅ ${workingEndpoints} API endpoints are available`);
            } else {
                test.errors.push('No API endpoints are responding');
                console.error('❌ No API endpoints are responding');
            }

            if (workingEndpoints < endpoints.length) {
                test.warnings.push(`Only ${workingEndpoints}/${endpoints.length} endpoints are working`);
            }
        } catch (error) {
            test.errors.push(`API endpoint test failed: ${error.message}`);
            console.error('❌ API endpoint test failed:', error.message);
        }

        return test;
    }

    /**
     * Test browser compatibility
     * @returns {Object} Test result
     */
    static testBrowserCompatibility() {
        const test = {
            name: 'Browser Compatibility',
            passed: true,
            errors: [],
            warnings: [],
            capabilities: {},
        };

        // Test geolocation API
        test.capabilities.geolocation = 'geolocation' in navigator;
        if (!test.capabilities.geolocation) {
            test.warnings.push('Geolocation API not supported');
        }

        // Test fetch API
        test.capabilities.fetch = typeof fetch !== 'undefined';
        if (!test.capabilities.fetch) {
            test.errors.push('Fetch API not supported');
            test.passed = false;
        }

        // Test sessionStorage
        test.capabilities.sessionStorage = typeof sessionStorage !== 'undefined';
        if (!test.capabilities.sessionStorage) {
            test.warnings.push('sessionStorage not available - caching disabled');
        }

        // Test secure context
        test.capabilities.secureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
        if (!test.capabilities.secureContext) {
            test.warnings.push('Not in secure context - geolocation may not work');
        }

        console.log('✅ Browser compatibility checked');
        return test;
    }

    /**
     * Test configuration validation
     * @returns {Object} Test result
     */
    static testConfigurationValidation() {
        const test = {
            name: 'Configuration Validation',
            passed: false,
            errors: [],
            warnings: [],
        };

        try {
            const config = LocationConfig.getConfig();
            const validation = ConfigValidator.validateConfiguration(config);

            test.passed = validation.isValid;
            test.errors = validation.errors;
            test.warnings = validation.warnings;

            if (validation.isValid) {
                console.log('✅ Configuration validation passed');
            } else {
                console.error('❌ Configuration validation failed');
            }
        } catch (error) {
            test.errors.push(`Configuration validation failed: ${error.message}`);
            console.error('❌ Configuration validation failed:', error.message);
        }

        return test;
    }

    /**
     * Generate test report
     * @param {Object} results - Test results
     * @returns {string} Formatted test report
     */
    static generateTestReport(results) {
        let report = '=== Location Detection Configuration Test Report ===\n\n';

        report += `Environment: ${results.environment}\n`;
        report += `Timestamp: ${results.timestamp}\n\n`;

        report += `Summary: ${results.summary.passed} passed, ${results.summary.failed} failed, ${results.summary.warnings} warnings\n\n`;

        Object.values(results.tests).forEach((test) => {
            report += `${test.passed ? '✅' : '❌'} ${test.name}\n`;

            if (test.errors.length > 0) {
                report += '  Errors:\n';
                test.errors.forEach((error) => (report += `    - ${error}\n`));
            }

            if (test.warnings.length > 0) {
                report += '  Warnings:\n';
                test.warnings.forEach((warning) => (report += `    - ${warning}\n`));
            }

            report += '\n';
        });

        return report;
    }

    /**
     * Quick configuration check for development
     */
    static quickCheck() {
        console.group('🔍 Quick Configuration Check');

        try {
            const config = LocationConfig.getConfig();
            const flags = FeatureFlags.getFeatureFlagStatus();

            console.log('Configuration Status:');
            console.log('  - Enabled:', config.enabled);
            console.log('  - GPS Available:', flags.gpsDetection);
            console.log('  - IP Fallback:', flags.ipDetection);
            console.log('  - Cache Enabled:', flags.locationCache);
            console.log('  - API Endpoints:', config.ipApiEndpoints.length);
            console.log('  - Timeout:', config.timeout + 'ms');

            const validation = ConfigValidator.validateConfiguration(config);
            if (!validation.isValid) {
                console.warn('⚠️ Configuration Issues:');
                validation.errors.forEach((error) => console.warn('  -', error));
            } else {
                console.log('✅ Configuration looks good!');
            }
        } catch (error) {
            console.error('❌ Configuration check failed:', error.message);
        }

        console.groupEnd();
    }
}

export default ConfigTest;
