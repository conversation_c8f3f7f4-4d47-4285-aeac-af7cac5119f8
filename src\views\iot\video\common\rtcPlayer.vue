<template>
  <div class="rtc-player">
    <video ref="videoElement" controls autoplay style="text-align:left;">
      Your browser is too old which doesn't support HTML5 video.
    </video>
  </div>
</template>

<script>
// 使用对象存储多个播放器实例，避免全局变量冲突
const webrtcPlayers = {}

export default {
  name: 'RtcPlayer',
  props: ['videoUrl', 'error', 'hasaudio'],
  data() {
    return {
      timer: null,
      playerId: null // 当前实例的播放器ID
    }
  },
  watch: {
    videoUrl(newData, oldData) {
      this.pause()
      this.play(newData)
    },
    immediate: true
  },
  mounted() {
    // 为每个组件实例生成唯一ID
    this.playerId = `rtc_player_${this._uid}_${Date.now()}`

    const paramUrl = decodeURIComponent(this.$route.params.url)
    this.$nextTick(() => {
      if (typeof (this.videoUrl) === 'undefined') {
        this.videoUrl = paramUrl
      }
      console.log('初始化时的地址为: ' + this.videoUrl)
      this.play(this.videoUrl)
    })
  },
  destroyed() {
    clearTimeout(this.timer)
    // 清理当前实例的播放器
    this.pause()
  },
  methods: {
    play: function (url) {
      if (!url) return

      // 先清理之前的播放器实例
      this.pause()

      // 为当前实例创建独立的播放器
      webrtcPlayers[this.playerId] = new ZLMRTCClient.Endpoint({
        element: this.$refs.videoElement, // 使用ref引用而不是固定ID
        debug: true, // 是否打印日志
        zlmsdpUrl: url, // 流地址
        simulecast: false,
        useCamera: false,
        audioEnable: true,
        videoEnable: true,
        recvOnly: true,
        usedatachannel: false
      })

      const currentPlayer = webrtcPlayers[this.playerId]

      currentPlayer.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR, (e) => { // ICE 协商出错
        console.error('ICE 协商出错', this.playerId)
        this.eventcallbacK('ICE ERROR', 'ICE 协商出错')
      })

      currentPlayer.on(ZLMRTCClient.Events.WEBRTC_ON_REMOTE_STREAMS, (e) => { // 获取到了远端流，可以播放
        console.log('播放成功', this.playerId, e.streams)
        this.eventcallbacK('playing', '播放成功')
      })

      currentPlayer.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED, (e) => { // offer anwser 交换失败
        console.error('offer anwser 交换失败', this.playerId, e)
        this.eventcallbacK('OFFER ANSWER ERROR ', 'offer anwser 交换失败')
        if (e.code == -400 && e.msg == '流不存在') {
          console.log('流不存在', this.playerId)
          this.timer = setTimeout(() => {
            if (webrtcPlayers[this.playerId]) {
              webrtcPlayers[this.playerId].close()
              this.play(url)
            }
          }, 100)
        }
      })

      currentPlayer.on(ZLMRTCClient.Events.WEBRTC_ON_LOCAL_STREAM, (s) => { // 获取到了本地流
        this.eventcallbacK('LOCAL STREAM', '获取到了本地流')
      })
    },
    pause: function () {
      if (this.playerId && webrtcPlayers[this.playerId]) {
        webrtcPlayers[this.playerId].close()
        delete webrtcPlayers[this.playerId]
      }
    },
    resize: function () {
      // 添加resize方法，供父组件调用
      if (this.playerId && webrtcPlayers[this.playerId] && webrtcPlayers[this.playerId].resize) {
        webrtcPlayers[this.playerId].resize()
      }
    },
    eventcallbacK: function (type, message) {
      console.log('player 事件回调')
      console.log(type)
      console.log(message)
    }
  }
}
</script>

<style>
.LodingTitle {
  min-width: 70px;
}

.rtc-player {
  width: 100%;
}

.rtc-player video {
  width: 100%;
  max-height: 56vh;
  background-color: #000;
}

/* 隐藏logo */
/* .iconqingxiLOGO {
        display: none !important;
    } */
</style>
