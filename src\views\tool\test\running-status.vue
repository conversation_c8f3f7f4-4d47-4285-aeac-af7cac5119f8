<template>
  <div class="apple-container">


    <!-- 前三条记录独占第一行 -->
    <div class="top-cards-container">
      <div v-for="(item, index) in topThreeItems" :key="'top-' + item.id" class="card-wrapper top-card">
        <div class="apple-card">
          <div
            style="border: 1px solid #ccc; width: 220px; text-align: center; margin: 0 auto; margin-top: -15px">
            <vue-qr :text= item.value :size="200"></vue-qr>
          </div>
          <div class="card-header top-card-header">
            <span class="card-title">{{ item.name }}</span>
          </div>
          <div class="card-content">
            <div class="card-value">{{ item.value }}</div>
            <div class="card-time">更新时间: {{ item.ts }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 剩余记录每行4个排列 -->
    <div class="remaining-cards-container">
      <div v-for="(item, index) in remainingItems" :key="'remain-' + item.id" class="card-wrapper regular-card">
        <div class="apple-card">
          <div class="card-header">
            <span class="card-title">{{ item.name }}</span>
          </div>
          <div class="card-content">
            <div class="card-value">{{ item.value }}</div>
            <div class="card-time">更新时间: {{ item.ts }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加或修改产品固件对话框 -->
    <el-dialog :close-on-click-modal="false" title="设备固件升级" :visible.sync="openFirmware" width="600px" append-to-body
      class="apple-dialog">
      <div v-if="firmware == null" style="text-align: center; font-size: 16px">
        <i class="el-icon-success" style="color: #67c23a"></i>
        已经是最新版本，不需要升级
      </div>
      <el-descriptions :column="1" border size="large"
        v-if="firmware != null && deviceInfo.firmwareVersion < firmware.version"
        :labelStyle="{ width: '150px', 'font-weight': 'bold' }">
        <template slot="title">
          <el-link icon="el-icon-success" type="success" :underline="false">可以升级到以下版本</el-link>
        </template>
        <el-descriptions-item label="固件名称">{{ firmware.firmwareName }}</el-descriptions-item>
        <el-descriptions-item label="所属产品">{{ firmware.productName }}</el-descriptions-item>
        <el-descriptions-item label="固件版本">Version {{ firmware.version }}</el-descriptions-item>
        <el-descriptions-item label="下载地址">
          <el-link :href="getDownloadUrl(firmware.filePath)" :underline="false" type="primary">{{
            getDownloadUrl(firmware.filePath) }}</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="固件描述">{{ firmware.remark }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="otaUpgrade"
          v-if="firmware != null && deviceInfo.firmwareVersion < firmware.version">升 级</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLatestFirmware } from '@/api/iot/firmware';
import { serviceInvoke } from '@/api/iot/runstatus';
import vueQr from 'vue-qr';

export default {
  name: 'running-status',
  props: {
    device: {
      type: Object,
      default: null,
    },
  },
  watch: {
    // 获取到父组件传递的device后，刷新列表
    device: {
      handler(newVal) {
        if (newVal && newVal.deviceId != 0) {
          this.deviceInfo = newVal;
          this.updateDeviceStatus(this.deviceInfo);
          this.$nextTick(function () {
            this.MonitorChart();
          });
          this.mqttCallback();
        }
      },
    }
  },
  data() {
    return {
      qrText:'seefy',
      // 控制模块标题
      title: '设备控制 ',
      // 未启用设备影子
      shadowUnEnable: false,
      // 控制项标题背景
      statusColor: {
        background: '#67C23A',
        color: '#fff',
        minWidth: '100px',
      },
      // 最新固件信息
      firmware: {},
      // 打开固件对话框
      openFirmware: false,
      // 遮罩层
      loading: true,
      // 设备信息
      deviceInfo: {
        boolList: [],
        enumList: [],
        stringList: [],
        integerList: [],
        decimalList: [],
        arrayList: [],
        thingsModels: [],
        chartList: [],
      },
      // 监测图表
      monitorChart: [
        {
          chart: {},
          data: {
            id: '',
            name: '',
            value: '',
          },
        },
      ],
      remoteCommand: {},
    };
  },
  components: {
    vueQr,
  },
  computed: {
    // 前三条记录
    topThreeItems() {
      return this.deviceInfo.thingsModels.slice(0, 3);
    },
    // 剩余记录
    remainingItems() {
      return this.deviceInfo.thingsModels.slice(3);
    }
  },
  created() { },
  methods: {
    /* Mqtt回调处理 */
    mqttCallback() {
      this.$mqttTool.client.on('message', (topic, message, buffer) => {
        let topics = topic.split('/');
        let productId = topics[1];
        let deviceNum = topics[2];
        message = JSON.parse(message.toString());
        if (!message) {
          return;
        }
        if (topics[3] == 'status') {
          console.log('接收到【设备状态-运行】主题：', topic);
          console.log('接收到【设备状态-运行】内容：', message);
          // 更新列表中设备的状态
          if (this.deviceInfo.serialNumber == deviceNum) {
            this.deviceInfo.status = message.status;
            this.deviceInfo.isShadow = message.isShadow;
            this.deviceInfo.rssi = message.rssi;
            this.updateDeviceStatus(this.deviceInfo);
          }
        }
        //兼容设备回复
        if (topics[4] == 'reply') {
          this.$modal.notifySuccess(message);
        }
        if (topic.endsWith('ws/service')) {
          console.log('接收到【物模型】主题1：', topic);
          console.log('接收到【物模型】内容：', message);
          // 更新列表中设备的属性
          if (this.deviceInfo.serialNumber == deviceNum) {
            for (let j = 0; j < message.length; j++) {
              let isComplete = false;
              // 设备状态
              for (let k = 0; k < this.deviceInfo.thingsModels.length && !isComplete; k++) {
                if (this.deviceInfo.thingsModels[k].id == message[j].id) {
                  // 普通类型(小数/整数/字符串/布尔/枚举)
                  if (this.deviceInfo.thingsModels[k].datatype.type == 'decimal' || this.deviceInfo.thingsModels[k].datatype.type == 'integer') {
                    this.deviceInfo.thingsModels[k].shadow = Number(message[j].value);
                  } else {
                    this.deviceInfo.thingsModels[k].shadow = message[j].value;
                  }
                  isComplete = true;
                  break;
                } else if (this.deviceInfo.thingsModels[k].datatype.type == 'object') {
                  // 对象类型
                  for (let n = 0; n < this.deviceInfo.thingsModels[k].datatype.params.length; n++) {
                    if (this.deviceInfo.thingsModels[k].datatype.params[n].id == message[j].id) {
                      this.deviceInfo.thingsModels[k].datatype.params[n].shadow = message[j].value;
                      isComplete = true;
                      break;
                    }
                  }
                } else if (this.deviceInfo.thingsModels[k].datatype.type == 'array') {
                  // 数组类型
                  if (this.deviceInfo.thingsModels[k].datatype.arrayType == 'object') {
                    // 1.对象类型数组,id为数组中一个元素,例如：array_01_gateway_temperature
                    if (String(message[j].id).indexOf('array_') == 0) {
                      for (let n = 0; n < this.deviceInfo.thingsModels[k].datatype.arrayParams.length; n++) {
                        for (let m = 0; m < this.deviceInfo.thingsModels[k].datatype.arrayParams[n].length; m++) {
                          if (this.deviceInfo.thingsModels[k].datatype.arrayParams[n][m].id == message[j].id) {
                            this.deviceInfo.thingsModels[k].datatype.arrayParams[n][m].shadow = message[j].value;
                            isComplete = true;
                            break;
                          }
                        }
                        if (isComplete) {
                          break;
                        }
                      }
                    } else {
                      // 2.对象类型数组，例如：gateway_temperature,消息ID添加前缀后匹配
                      for (let n = 0; n < this.deviceInfo.thingsModels[k].datatype.arrayParams.length; n++) {
                        for (let m = 0; m < this.deviceInfo.thingsModels[k].datatype.arrayParams[n].length; m++) {
                          let index = n > 9 ? String(n) : '0' + k;
                          let prefix = 'array_' + index + '_';
                          if (this.deviceInfo.thingsModels[k].datatype.arrayParams[n][m].id == prefix + message[j].id) {
                            this.deviceInfo.thingsModels[k].datatype.arrayParams[n][m].shadow = message[j].value;
                            isComplete = true;
                          }
                        }
                        if (isComplete) {
                          break;
                        }
                      }
                    }
                  } else {
                    // 整数、小数和字符串类型数组
                    for (let n = 0; n < this.deviceInfo.thingsModels[k].datatype.arrayModel.length; n++) {
                      if (this.deviceInfo.thingsModels[k].datatype.arrayModel[n].id == message[j].id) {
                        this.deviceInfo.thingsModels[k].datatype.arrayModel[n].shadow = message[j].value;
                        isComplete = true;
                        break;
                      }
                    }
                  }
                }
              }
              // 图表数据
              for (let k = 0; k < this.deviceInfo.chartList.length; k++) {
                if (this.deviceInfo.chartList[k].id.indexOf('array_') == 0) {
                  // 数组类型匹配,例如：array_00_gateway_temperature
                  if (this.deviceInfo.chartList[k].id == message[j].id) {
                    // let shadows = message[j].value.split(",");
                    this.deviceInfo.chartList[k].shadow = message[j].value;
                    // 更新图表
                    for (let m = 0; m < this.monitorChart.length; m++) {
                      if (message[j].id == this.monitorChart[m].data.id) {
                        let data = [
                          {
                            value: message[j].value,
                            name: this.monitorChart[m].data.name,
                          },
                        ];
                        this.monitorChart[m].chart.setOption({
                          series: [
                            {
                              data: data,
                            },
                          ],
                        });
                        break;
                      }
                    }
                  }
                } else {
                  // 普通类型匹配
                  if (this.deviceInfo.chartList[k].id == message[j].id) {
                    this.deviceInfo.chartList[k].shadow = message[j].value;
                    // 更新图表
                    for (let m = 0; m < this.monitorChart.length; m++) {
                      if (message[j].id == this.monitorChart[m].data.id) {
                        isComplete = true;
                        let data = [
                          {
                            value: message[j].value,
                            name: this.monitorChart[m].data.name,
                          },
                        ];
                        this.monitorChart[m].chart.setOption({
                          series: [
                            {
                              data: data,
                            },
                          ],
                        });
                        break;
                      }
                    }
                  }
                }
                if (isComplete) {
                  break;
                }
              }
            }
          }
        }
      });
    },

    //发送指令
    mqttPublish(device, model) {
      const command = {};
      command[model.id] = model.shadow;
      const data = {
        serialNumber: device.serialNumber,
        productId: device.productId,
        remoteCommand: command,
        identifier: model.id,
        modelName: model.name,
        isShadow: device.status != 3,
        type: model.type,
      };
      serviceInvoke(data).then((response) => {
        if (response.code === 200) {
          this.$message({
            type: 'success',
            message: '服务调用成功!',
          });
        }
      });
    },

    /**
     * Mqtt发布消息
     * @device 设备
     * @model 物模型(id/name/type/name/isReadonly/value/shadow)，type 类型(1=属性，2=功能，3=OTA升级，4=实时监测)
     * */
    // mqttPublish(device, model) {
    //     let topic = "";
    //     let message = ""
    //     if (model.type == 1) {
    //         if (device.status == 3) {
    //             // 属性,在线模式
    //             topic = "/" + device.productId + "/" + device.serialNumber + "/property-online/get";
    //         } else if (device.isShadow) {
    //             // 属性,离线模式
    //             topic = "/" + device.productId + "/" + device.serialNumber + "/property-offline/post";
    //         }
    //         message = '[{"id":"' + model.id + '","value":"' + model.shadow + '"}]';
    //     } else if (model.type == 2) {
    //         if (device.status == 3) {
    //             // 功能,在线模式
    //             topic = "/" + device.productId + "/" + device.serialNumber + "/function-online/get";
    //
    //         } else if (device.isShadow) {
    //             // 功能,离线模式
    //             topic = "/" + device.productId + "/" + device.serialNumber + "/function-offline/post";
    //         }
    //         message = '[{"id":"' + model.id + '","value":"' + model.shadow + '"}]';
    //     } else if (model.type == 3) {
    //         // OTA升级
    //         topic = "/" + device.productId + "/" + device.serialNumber + "/ota/get";
    //         message = '{"version":' + this.firmware.version + ',"downloadUrl":"' + this.getDownloadUrl(this.firmware.filePath) + '"}';
    //     } else {
    //         return;
    //     }
    //     if (topic != "") {
    //         // 发布
    //         this.$mqttTool.publish(topic, message, model.name).then(res => {
    //             this.$modal.notifySuccess(res);
    //         }).catch(res => {
    //             this.$modal.notifyError(res);
    //         });
    //     }
    // },

    /** 枚举类型按钮单击 */
    enumButtonClick(device, model, value) {
      model.shadow = value;
      this.mqttPublish(device, model);
    },

    /** 更新设备状态 */
    updateDeviceStatus(device) {
      if (device.status == 3) {
        this.statusColor.background = '#12d09f';
        this.title = '在线模式';
      } else {
        if (device.isShadow == 1) {
          this.statusColor.background = '#409EFF';
          this.title = '影子模式';
        } else {
          this.statusColor.background = '#909399';
          this.title = '离线模式';
          this.shadowUnEnable = true;
        }
      }
      this.$emit('statusEvent', this.deviceInfo.status);
    },
    /** 物模型数组元素值改变事件 */
    arrayItemChange(value, thingsModel) {
      let shadow = '';
      for (let i = 0; i < thingsModel.datatype.arrayCount; i++) {
        shadow += thingsModel.datatype.arrayModel[i].shadow + ',';
      }
      shadow = shadow.substring(0, shadow.length - 1);
      thingsModel.shadow = shadow;
    },
    /** 物模型中数组值改变事件 */
    arrayInputChange(value, thingsModel) {
      let arrayModels = value.split(',');
      if (arrayModels.length != thingsModel.datatype.arrayCount) {
        this.$modal.alertWarning('元素个数不匹配，数组元素个数为' + thingsModel.datatype.arrayCount + '个，以英文逗号分隔。');
      } else {
        for (let i = 0; i < thingsModel.datatype.arrayCount; i++) {
          thingsModel.datatype.arrayModel[i].shadow = arrayModels[i];
        }
      }
    },
    /**用户是否拥有分享设备权限*/
    // hasShrarePerm(permission) {
    //   if (this.deviceInfo.isOwner == 0) {
    //     // 分享设备权限
    //     if (this.deviceInfo.userPerms.indexOf(permission) == -1) {
    //       return false;
    //     }
    //   }
    //   return true;
    // },
    /** 设备升级 */
    otaUpgrade() {
      // OTA升级
      let topic = '/' + this.deviceInfo.productId + '/' + this.deviceInfo.serialNumber + '/ota/get';
      let message = '{"version":' + this.firmware.version + ',"downloadUrl":"' + this.getDownloadUrl(this.firmware.filePath) + '"}';
      // 发布
      this.$mqttTool
        .publish(topic, message, '设备升级')
        .then((res) => {
          this.$modal.notifySuccess(res);
        })
        .catch((res) => {
          this.$modal.notifyError(res);
        });
      this.openFirmware = false;
    },
    /** 获取最新固件 */
    getLatestFirmware(deviceId) {
      getLatestFirmware(deviceId).then((response) => {
        this.firmware = response.data;
        this.openFirmware = true;
      });
    },
    // 取消按钮
    cancel() {
      this.openFirmware = false;
    },
    // 获取下载路径前缀
    getDownloadUrl(path) {
      return window.location.origin + process.env.VUE_APP_BASE_API + path;
    },
    /**图表展示*/
    MonitorChart() {
      for (let i = 0; i < this.deviceInfo.chartList.length; i++) {
        this.monitorChart[i] = {
          chart: this.$echarts.init(this.$refs.map[i]),
          data: {
            id: this.deviceInfo.chartList[i].id,
            name: this.deviceInfo.chartList[i].name,
            value: this.deviceInfo.chartList[i].shadow ? this.deviceInfo.chartList[i].shadow : this.deviceInfo.chartList[i].datatype.min,
          },
        };
        var option;
        option = {
          tooltip: {
            formatter: ' {b} <br/> {c}' + this.deviceInfo.chartList[i].datatype.unit,
          },
          series: [
            {
              name: this.deviceInfo.chartList[i].datatype.type,
              type: 'gauge',
              min: this.deviceInfo.chartList[i].datatype.min,
              max: this.deviceInfo.chartList[i].datatype.max,
              colorBy: 'data',
              splitNumber: 10,
              radius: '100%',
              // 分割线
              splitLine: {
                distance: 4,
              },
              axisLabel: {
                fontSize: 10,
                distance: 10,
              },
              // 刻度线
              axisTick: {
                distance: 4,
              },
              // 仪表盘轴线
              axisLine: {
                lineStyle: {
                  width: 8,
                  color: [
                    [0.2, '#409EFF'], // 0~20%
                    [0.8, '#12d09f'], // 40~60%
                    [1, '#F56C6C'], // 80~100%
                  ],
                  opacity: 0.3,
                },
              },
              pointer: {
                icon: 'triangle',
                length: '60%',
                width: 7,
              },
              progress: {
                show: true,
                width: 8,
              },
              detail: {
                valueAnimation: true,
                formatter: '{value}' + ' ' + this.deviceInfo.chartList[i].datatype.unit,
                offsetCenter: [0, '80%'],
                fontSize: 20,
              },
              data: [
                {
                  value: this.deviceInfo.chartList[i].shadow ? this.deviceInfo.chartList[i].shadow : this.deviceInfo.chartList[i].datatype.min,
                  name: this.deviceInfo.chartList[i].name + "\n" + this.deviceInfo.chartList[i].ts,
                },
              ],
              title: {
                offsetCenter: [0, '115%'],
                fontSize: 16,
              },
            },
          ],
        };
        option && this.monitorChart[i].chart.setOption(option);
      }
    },
  },
};
</script>

<style>
/* 苹果风格设计 */
.apple-container {
  padding: 24px;
  background-color: #f5f5f7;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.top-cards-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  gap: 24px;
}

.remaining-cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 24px;
}

.card-wrapper {
  margin-bottom: 24px;
}

.top-card {
  flex: 0 0 calc(33.333% - 16px);
  max-width: calc(33.333% - 16px);
}

.regular-card {
  flex: 0 0 calc(25% - 18px);
  max-width: calc(25% - 18px);
}

.apple-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.apple-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.top-card-header {
  background-color: rgba(0, 122, 255, 0.1);
  border-left: 4px solid #007AFF;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d1d1f;
}

.card-content {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #1d1d1f;
  text-align: center;
  margin-bottom: 20px;
}

.card-time {
  color: #86868b;
  font-size: 14px;
  text-align: left;
}

/* 响应式布局调整 */
@media screen and (max-width: 1400px) {
  .regular-card {
    flex: 0 0 calc(33.333% - 16px);
    max-width: calc(33.333% - 16px);
  }
}

@media screen and (max-width: 1100px) {
  .top-cards-container {
    flex-wrap: wrap;
  }

  .top-card {
    flex: 0 0 calc(50% - 12px);
    max-width: calc(50% - 12px);
  }

  .regular-card {
    flex: 0 0 calc(50% - 12px);
    max-width: calc(50% - 12px);
  }
}

@media screen and (max-width: 768px) {

  .top-card,
  .regular-card {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* 重写Element UI组件样式，使其符合苹果风格 */
.apple-dialog .el-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.apple-dialog .el-dialog__header {
  padding: 20px;
  background-color: #f5f5f7;
}

.apple-dialog .el-dialog__title {
  font-weight: 500;
  color: #1d1d1f;
}

.apple-dialog .el-dialog__body {
  padding: 24px;
}

.apple-dialog .el-button {
  border-radius: 8px;
  font-weight: 500;
}

.apple-dialog .el-button--success {
  background-color: #34c759;
  border-color: #34c759;
}

/* 重写滑动块样式 */
.el-slider__bar {
  height: 18px;
}

.el-slider__runway {
  height: 18px;
  margin: 5px 0;
}

.el-slider__button {
  height: 18px;
  width: 18px;
  border-radius: 10%;
}

.el-slider__button-wrapper {
  top: -9px;
}
</style>
