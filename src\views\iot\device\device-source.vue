<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker
                    v-model="dateRange"
                    size="small"
                    style="width: 340px"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetimerange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
            </el-form-item>
        </el-form>

        <!-- 数据表格 -->
        <el-table v-loading="loading" :data="dataList" border>
            <el-table-column label="设备编号" align="center" prop="serialNumber" width="150" />
            <el-table-column label="日志内容" align="left" prop="message" min-width="300">
                <template slot-scope="scope">
                    <div class="message-content">{{ scope.row.message }}</div>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="ts" width="180" />
        </el-table>

        <!-- 分页组件 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script>
import { listDeviceSourceLog } from '@/api/iot/deviceLog';

export default {
    name: 'DeviceSource',
    props: {
        device: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 原始数据表格数据
            dataList: [],
            // 日期范围
            dateRange: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                serialNumber: undefined,
                beginTime: undefined,
                endTime: undefined,
            },
        };
    },
    watch: {
        device: {
            handler(val) {
                if (val && val.serialNumber) {
                    this.queryParams.serialNumber = val.serialNumber;
                    this.getList();
                }
            },
            immediate: true,
        },
    },
    methods: {
        /** 查询原始数据列表 */
        getList() {
            this.loading = true;

            // 处理日期范围
            if (this.dateRange && this.dateRange.length > 0) {
                this.queryParams.beginTime = this.dateRange[0];
                this.queryParams.endTime = this.dateRange[1];
            } else {
                this.queryParams.beginTime = undefined;
                this.queryParams.endTime = undefined;
            }

            listDeviceSourceLog(this.queryParams)
                .then((response) => {
                    this.dataList = response.rows;
                    this.total = response.total;
                    this.loading = false;
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateRange = [];
            this.resetForm('queryForm');
            this.queryParams.serialNumber = this.device.serialNumber;
            this.handleQuery();
        },
        /** 导出按钮操作 */
        handleExport() {
            // 处理日期范围
            if (this.dateRange && this.dateRange.length > 0) {
                this.queryParams.beginTime = this.dateRange[0];
                this.queryParams.endTime = this.dateRange[1];
            } else {
                this.queryParams.beginTime = undefined;
                this.queryParams.endTime = undefined;
            }

            // 修复：确保时间戳能正确显示
            const timestamp = new Date().getTime();
            const fileName = `device_source_${timestamp}.xlsx`;

            this.download(
                '/iot/deviceLog/source/export',
                {
                    ...this.queryParams,
                },
                fileName // 使用包含时间戳的文件名
            );
        },
    },
};
</script>

<style scoped>
.app-container {
    padding: 20px;
}

.message-content {
    word-wrap: break-word;
    word-break: break-all;
    white-space: pre-wrap;
    line-height: 1.5;
    max-width: 100%;
    padding: 8px 0;
}
</style>
