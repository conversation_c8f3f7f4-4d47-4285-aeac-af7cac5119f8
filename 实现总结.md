# Http自定义推送功能实现总结

## 实现概述

成功在 `src/views/iot/metadata/index.vue` 页面中实现了Http自定义推送功能（type=7），包含完整的表单配置、数据验证、动态组件和数据转换逻辑。

## 主要功能特性

### 1. 表单配置项
- ✅ **请求地址**: 支持http://和https://协议前缀选择
- ✅ **请求方法**: 支持GET和POST方法选择
- ✅ **请求头**: 动态键值对配置，支持添加/删除
- ✅ **Token请求地址**: 独立的Token获取地址配置
- ✅ **请求参数**: 动态查询参数配置
- ✅ **请求配置**: 动态配置项
- ✅ **请求体**: JSON格式配置，支持占位符
- ✅ **计算公式**: 动态字段计算公式配置

### 2. 用户体验优化
- ✅ **提示信息**: 计算公式和请求体的详细使用说明
- ✅ **表单验证**: 必填项检查和JSON格式验证
- ✅ **动态操作**: 支持动态添加/删除配置项
- ✅ **数据回显**: 编辑时正确回显所有配置
- ✅ **错误处理**: 完善的错误提示和异常处理

### 3. 数据处理逻辑
- ✅ **格式转换**: 动态列表与对象的相互转换
- ✅ **URL处理**: 协议前缀的智能拼接
- ✅ **JSON处理**: 字符串与对象的转换
- ✅ **占位符支持**: &{}和%s占位符的处理

## 技术实现细节

### 1. 表单结构扩展
```javascript
// 新增数据结构
form: {
  // 协议前缀
  httpProtocol: 'http://',
  tokenProtocol: 'http://',
  // 动态列表
  requestHeadersList: [{ key: '', value: '' }],
  requestQueryList: [{ key: '', value: '' }],
  requestConfigList: [{ key: '', value: '' }],
  formulaList: [{ key: '', value: '' }],
  // Http自定义推送数据
  dataJson: {
    hostUrl: '',
    method: 'POST',
    requestHeaders: {},
    tokenUrl: '',
    requestQuery: {},
    requestConfig: {},
    requestBody: '',
    formula: {},
  }
}
```

### 2. 核心方法实现
- `selectAction()`: 类型选择时的数据初始化
- `convertListToObject()`: 动态列表转对象
- `convertObjectToList()`: 对象转动态列表
- `addRequestHeader/Query/Config/Formula()`: 动态添加配置项
- `removeRequestHeader/Query/Config/Formula()`: 动态删除配置项

### 3. 数据验证规则
```javascript
rules: {
  dataJson: {
    hostUrl: [{ required: true, message: '请求地址不能为空', trigger: 'blur' }],
    method: [{ required: true, message: '请求方法不能为空', trigger: 'change' }],
    tokenUrl: [{ required: true, message: 'Token请求地址不能为空', trigger: 'blur' }],
  }
}
```

## 文件修改清单

### 主要修改文件
1. **src/views/iot/metadata/index.vue**
   - 扩展表单模板，添加Http自定义推送配置项
   - 增加数据结构和验证规则
   - 实现动态组件操作方法
   - 完善数据提交和回显逻辑
   - 添加CSS样式优化

### 新增文件
1. **Http自定义推送功能说明.md** - 详细的功能说明文档
2. **test-http-custom-push.html** - 功能测试页面
3. **实现总结.md** - 本总结文档

## 数据流程

### 1. 新增流程
```
用户选择type=7 → 显示Http自定义推送表单 → 填写配置项 → 
数据验证 → 列表转对象 → URL拼接 → JSON解析 → 提交保存
```

### 2. 编辑流程
```
获取数据 → JSON解析 → URL分离 → 对象转列表 → 表单回显 → 
用户修改 → 数据验证 → 重新转换 → 提交更新
```

## 最终数据格式示例

```json
{
  "requestHeaders": {
    "Authorization": "token"
  },
  "tokenUrl": "http://*************/dev-api/loginWithoutCode",
  "requestConfig": {
    "username": "admin",
    "password": "seefy@2024!"
  },
  "formula": {
    "batteryVoltage": "%s%100.00",
    "batteryValue": "%s%1000.00"
  },
  "method": "POST",
  "hostUrl": "http://localhost:8081/iot/tool/testPost",
  "requestBody": {
    "serviceId": 1,
    "messageType": "dataReport",
    "eventType": 1,
    "productId": "0001",
    "deviceId": "demoData",
    "timestamp": 1703816276671,
    "payload": {
      "time": "&{collectTime}",
      "ch4_value": "&{CH4Val}",
      "temperature": "&{temperature}",
      "battery_voltage": "&{batteryVoltage}"
    }
  }
}
```

## 使用前准备

### 1. 字典数据配置
需要在系统字典管理中为 `rule_script_action` 字典类型添加：
- 字典标签: `Http自定义推送`
- 字典键值: `7`
- 显示排序: `7`

### 2. 后端接口
确保现有的元数据管理接口支持新的数据结构。

## 测试建议

1. **功能测试**: 使用提供的测试页面验证各项功能
2. **数据验证**: 测试各种数据格式和边界情况
3. **用户体验**: 验证表单操作的流畅性
4. **兼容性**: 确保不影响现有功能

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加新的配置项类型
- 支持更多HTTP方法和协议
- 可以扩展更复杂的数据转换逻辑
- 支持更多占位符类型

## 注意事项

1. 字典数据需要后端配合添加
2. 请求体必须是有效的JSON格式
3. 占位符使用需要按照规范格式
4. 建议在生产环境部署前进行充分测试
