# Http自定义推送功能实现说明

## 功能概述

在规则引擎元数据管理页面中新增了"Http自定义推送"类型（type=7），支持灵活配置HTTP请求参数，实现自定义数据推送功能。

## 功能特性

### 1. 基础配置
- **请求地址**: 支持http://和https://协议前缀选择
- **请求方法**: 支持GET和POST方法
- **Token请求地址**: 支持独立的Token获取地址配置

### 2. 动态配置项
- **请求头**: 支持动态添加多个键值对
- **请求参数**: 支持动态添加多个查询参数
- **请求配置**: 支持动态添加多个配置项
- **计算公式**: 支持动态添加多个字段计算公式

### 3. 高级功能
- **请求体**: 支持JSON格式的请求体配置，支持占位符
- **计算公式**: 支持设备数据计算转换
- **提示信息**: 提供详细的使用说明和示例

## 数据格式说明

### 占位符说明
- `&{}`: 名称位占位符，用于引用设备字段名称
- `%s`: 计算占位符，用于设备上行数据计算

### 计算公式示例
- 加法: `%s+10`
- 减法: `%s-10`
- 乘法: `%s*10`
- 除法: `%s/10`
- 除法(保留小数): `%s%10.00`

### 最终数据结构示例
```json
{
  "requestHeaders": {
    "Authorization": "token"
  },
  "tokenUrl": "http://*************/dev-api/loginWithoutCode",
  "requestConfig": {
    "username": "admin",
    "password": "seefy@2024!"
  },
  "formula": {
    "batteryVoltage": "%s%100.00",
    "batteryValue": "%s%1000.00"
  },
  "method": "POST",
  "hostUrl": "http://localhost:8081/iot/tool/testPost",
  "requestBody": {
    "serviceId": 1,
    "messageType": "dataReport",
    "eventType": 1,
    "productId": "0001",
    "deviceId": "demoData",
    "timestamp": 1703816276671,
    "payload": {
      "time": "&{collectTime}",
      "ch4_value": "&{CH4Val}",
      "temperature": "&{temperature}",
      "battery_voltage": "&{batteryVoltage}"
    }
  }
}
```

## 使用步骤

### 1. 添加字典数据
首先需要在系统字典管理中为`rule_script_action`字典类型添加新的选项：
- 字典标签: `Http自定义推送`
- 字典键值: `7`
- 显示排序: `7`

### 2. 配置Http自定义推送
1. 进入规则引擎元数据管理页面
2. 点击"新增"按钮
3. 填写名称
4. 选择类型为"Http自定义推送"
5. 配置各项参数：
   - 请求地址（选择协议前缀）
   - 请求方法
   - 请求头（可添加多个）
   - Token请求地址
   - 请求参数（可添加多个）
   - 请求配置（可添加多个）
   - 请求体（JSON格式）
   - 计算公式（可添加多个）
6. 填写备注信息
7. 点击"确定"保存

### 3. 编辑已有配置
1. 在列表中点击"修改"按钮
2. 系统会自动回显所有配置信息
3. 修改需要的配置项
4. 点击"确定"保存修改

## 技术实现要点

### 1. 表单验证
- 名称、类型为必填项
- 请求地址、请求方法、Token请求地址为必填项
- 请求体需要是有效的JSON格式

### 2. 数据转换
- 动态列表与对象之间的相互转换
- URL协议前缀的处理
- JSON字符串与对象的转换

### 3. 用户体验优化
- 提供详细的提示信息和示例
- 支持动态添加和删除配置项
- 表单验证和错误提示
- 数据回显和状态保持

## 注意事项

1. 请求体必须是有效的JSON格式
2. 占位符的使用需要按照规范格式
3. 计算公式中的%s是固定占位符
4. 动态配置项至少保留一个空行供用户填写
5. 删除操作只对非第一行生效，确保至少有一个输入框

## 扩展性

该实现具有良好的扩展性：
- 可以轻松添加新的配置项
- 支持更多的HTTP方法
- 可以扩展更多的占位符类型
- 支持更复杂的数据转换逻辑
