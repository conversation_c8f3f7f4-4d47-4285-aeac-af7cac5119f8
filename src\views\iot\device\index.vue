<template>
    <div style="padding: 6px">
        <el-card style="margin-bottom: 6px">
            <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="75px" style="margin-bottom: -20px" v-show="showSearch">
                <el-form-item label="归属机构" prop="deptId">
                    <treeselect :appendToBody="true" z-index="9999" v-model="queryParams.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属机构" @select="handleDeptChange" style="width: 250px" />
                </el-form-item>

                <el-form-item label="所属场景" prop="scenarioId">
                    <el-select
                        v-model="queryParams.scenarioId"
                        :placeholder="!queryParams.deptId ? '请先选择归属机构' : scenarioOptions.length === 0 ? '该机构下暂无场景' : '请选择所属场景'"
                        clearable
                        style="width: 100%"
                        @change="handleScenarioChange"
                        :disabled="!queryParams.deptId"
                    >
                        <el-option v-for="scenario in scenarioOptions" :key="scenario.scenarioId" :label="scenario.scenarioName" :value="scenario.scenarioId" />
                    </el-select>
                </el-form-item>

                <el-form-item label="设备名称" prop="deviceName">
                    <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable size="small" @keyup.enter.native="handleQuery" style="width: 150px" />
                </el-form-item>
                <el-form-item label="设备编号" prop="serialNumber">
                    <el-input v-model="queryParams.serialNumber" placeholder="请输入设备编号" clearable size="small" @keyup.enter.native="handleQuery" style="width: 150px" />
                </el-form-item>
                <el-form-item label="产品名称" prop="productId">
                    <el-select v-model="queryParams.productId" placeholder="请选择产品名称" clearable size="small" style="width: 150px">
                        <el-option v-for="item in productList" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="设备状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable size="small" style="width: 150px">
                        <el-option v-for="dict in dict.type.iot_device_status" :key="dict.value" :label="dict.label" :value="dict.value" />
                    </el-select>
                </el-form-item>
                <!-- <el-form-item label="我的分组">
                    <el-select v-model="queryParams.groupId" placeholder="请选择我的分组" clearable size="small" style="width: 150px">
                        <el-option v-for="group in myGroupList" :key="group.groupId" :label="group.groupName" :value="group.groupId" />
                    </el-select>
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>

                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-dropdown @command="(command) => handleCommand(command)" v-hasPermi="['iot:device:add']">
                            <el-button size="mini" type="primary" plain>
                                新增设备
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="handleEditDevice" v-hasPermi="['iot:device:add']">手动添加</el-dropdown-item>
                                <el-dropdown-item command="handleBatchImport" v-hasPermi="['iot:device:add']">批量导入</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-col>
                    <el-col :span="1.5">
                        <el-dropdown @command="(command) => handleCommand1(command)" v-hasPermi="['iot:device:assignment']">
                            <el-button size="mini" type="primary" plain>
                                分配设备
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="handleSelectAllot" v-hasPermi="['iot:device:assignment']">选择分配</el-dropdown-item>
                                <el-dropdown-item command="handleImportAllot" v-hasPermi="['iot:device:assignment']">导入分配</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-col>
                    <!--                    <el-col :span="1.5">-->
                    <!--                        <el-button size="mini" type="primary" plain @click="recycleDevice" v-hasPermi="['iot:device:recovery']">回收设备</el-button>-->
                    <!--                    </el-col>-->
                    <el-col :span="1.5">
                        <el-button size="mini" type="danger" plain @click="batchDeleteDevice" v-hasPermi="['iot:device:batchdelete']">批量删除</el-button>
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-dropdown @command="(command) => handleCommandMore(command)"
                            v-hasPermi="['iot:device:assignment']">
                            <el-button size="mini" type="primary" plain>更多操作<i
                                    class="el-icon-arrow-down el-icon--right"></i></el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="importRecord"
                                    v-hasPermi="['iot:device:assignment']">导入记录</el-dropdown-item>
                                <el-dropdown-item command="exportDevice">导出设备
                                    <el-tooltip content="最多每次下载10000条数据" placement="top" style="margin-left:5px;"> <i
                                            class="el-icon-question"></i>
                                    </el-tooltip></el-dropdown-item>
                                <el-dropdown-item command="recycleRecord"
                                    v-hasPermi="['iot:device:assignment']">设备回收记录</el-dropdown-item>
                                <el-dropdown-item command="allotRecord"
                                    v-hasPermi="['iot:device:assignment']">设备分配记录</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </el-col> -->
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="el-icon-s-grid" size="mini" @click="handleChangeShowType">切换展示</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-checkbox v-model="queryParams.showChild" style="margin: 5px 0 0" @change="handleQuery">显示下级机构数据</el-checkbox>
                        <el-tooltip content="选中后，本级可以看下级的数据" placement="top"><i class="el-icon-question"></i></el-tooltip>
                    </el-col>
                    <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
            </el-form>
        </el-card>

        <el-card style="padding-bottom: 100px" v-if="showType == 'list'">
            <el-table v-loading="loading" :data="deviceList" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="编号" align="center" header-align="center" prop="deviceId" width="50" />
                <el-table-column label="设备名称" align="center" header-align="center" prop="deviceName" min-width="120" />
                <el-table-column label="设备编号" align="center" prop="serialNumber" min-width="130" />
                <el-table-column label="所属产品" align="center" prop="productName" min-width="120" />
                <el-table-column label="协议" align="center" prop="transport" min-width="50" />
                <el-table-column label="通讯协议" align="center" prop="protocolCode" min-width="100" />
                <el-table-column label="子设备数" align="center" prop="subDeviceCount" width="80">
                    <template slot-scope="scope">
                        {{ scope.row.subDeviceCount }}
                    </template>
                </el-table-column>
                <el-table-column label="设备影子" align="center" prop="isShadow" width="80">
                    <template slot-scope="scope">
                        <el-tag type="success" size="small" v-if="scope.row.isShadow == 1">启用</el-tag>
                        <el-tag type="info" size="small" v-else>禁用</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="状态" align="center" prop="status" width="80">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.iot_device_status" :value="scope.row.status" size="small" />
                    </template>
                </el-table-column>
                <el-table-column label="信号" align="center" prop="rssi" width="60">
                    <template slot-scope="scope">
                        <svg-icon v-if="scope.row.status == 3 && scope.row.rssi >= '-55'" icon-class="wifi_4" />
                        <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-70' && scope.row.rssi < '-55'" icon-class="wifi_3" />
                        <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-85' && scope.row.rssi < '-70'" icon-class="wifi_2" />
                        <svg-icon v-else-if="scope.row.status == 3 && scope.row.rssi >= '-100' && scope.row.rssi < '-85'" icon-class="wifi_1" />
                        <svg-icon v-else icon-class="wifi_0" />
                    </template>
                </el-table-column>
                <el-table-column label="定位方式" align="center" prop="locationWay">
                    <template slot-scope="scope">
                        <dict-tag :options="dict.type.iot_location_way" :value="scope.row.locationWay" size="small" />
                    </template>
                </el-table-column>
                <el-table-column label="固件版本" align="center" prop="firmwareVersion">
                    <template slot-scope="scope">
                        <el-tag size="mini" type="info">Ver {{ scope.row.firmwareVersion }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="上报时间" align="center" prop="lastTime">
                    <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.lastTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime">
                    <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                    <template slot-scope="scope">
                        <el-button type="danger" size="small" style="padding: 5px" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['iot:device:remove']">删除</el-button>
                        <el-button type="primary" size="small" style="padding: 5px" icon="el-icon-view" @click="handleEditDevice(scope.row)" v-hasPermi="['iot:device:query']">查看</el-button>
                        <el-button type="primary" size="small" style="padding: 5px" @click="openSummaryDialog(scope.row)" v-if="form.deviceId != 0" v-hasPermi="['iot:device:query']">二维码</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" :pageSizes="[12, 24, 36, 60]" @pagination="getList" />
        </el-card>

        <el-card style="padding-bottom: 100px" v-if="showType == 'card'">
            <el-row :gutter="30" v-loading="loading">
                <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="6" v-for="(item, index) in deviceList" :key="index" style="margin-bottom: 30px; text-align: center">
                    <div @click="choicedevice(item)">
                        <el-card :body-style="{ padding: '20px' }" shadow="always" :class="['card-item', { 'selected-card': selectedDeviceId.includes(item.deviceId) }]">
                            <el-row type="flex" :gutter="10" justify="space-between">
                                <el-col :span="20" style="text-align: left">
                                    <el-link type="" :underline="false" style="font-weight: bold; font-size: 16px; line-height: 32px">
                                        <el-tooltip class="item" effect="dark" content="分享的设备" placement="top-start">
                                            <svg-icon icon-class="share" style="font-size: 20px" v-if="item.isOwner != 1" />
                                        </el-tooltip>
                                        <svg-icon icon-class="device" v-if="item.isOwner == 1" />
                                        <span style="margin: 0 5px">{{ item.deviceName }}</span>
                                        <!-- <el-tag size="mini" type="info">Ver {{item.firmwareVersion}}</el-tag>-->
                                        <!-- <el-text v-if="item.protocolCode" type="info" size="mini" style="font-size: 14px; color: #ccc">{{ item.protocolCode }}</el-text> -->
                                    </el-link>
                                </el-col>
                                <el-col :span="1.5" style="font-size: 20px; padding-top: 5px; cursor: pointer">
                                    <svg-icon icon-class="qrcode" @click="openSummaryDialog(item)" v-hasPermi="['iot:device:query']" />
                                </el-col>
                                <el-col :span="3">
                                    <div style="font-size: 28px; color: #ccc">
                                        <svg-icon v-if="item.status == 3 && item.rssi >= '-55'" icon-class="wifi_4" />
                                        <svg-icon v-else-if="item.status == 3 && item.rssi >= '-70' && item.rssi < '-55'" icon-class="wifi_3" />
                                        <svg-icon v-else-if="item.status == 3 && item.rssi >= '-85' && item.rssi < '-70'" icon-class="wifi_2" />
                                        <svg-icon v-else-if="item.status == 3 && item.rssi >= '-100' && item.rssi < '-85'" icon-class="wifi_1" />
                                        <svg-icon v-else icon-class="wifi_0" />
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="17">
                                    <div style="text-align: left; line-height: 40px; white-space: nowrap">
                                        <dict-tag :options="dict.type.iot_device_status" :value="item.status" size="small" style="display: inline-block" />
                                        <span style="display: inline-block; margin: 0 10px">
                                            <!-- <el-tag type="success" size="small" v-if="item.isShadow == 1">影子</el-tag>
                                        <el-tag type="info" size="small" v-else>影子</el-tag> -->
                                            <el-tag type="primary" size="small" v-if="item.protocolCode">{{ item.protocolCode }}</el-tag>
                                        </span>
                                        <el-tag type="primary" size="small" v-if="item.transport">{{ item.transport }}</el-tag>
                                        <!-- <dict-tag :options="dict.type.iot_location_way" :value="item.locationWay" size="small" style="display:inline-block;" />
                                        <dict-tag :options="dict.type.iot_transport_type" :value="item.transport" size="small" style="display: inline-block" /> -->
                                    </div>
                                    <el-descriptions :column="1" size="mini" style="white-space: nowrap">
                                        <el-descriptions-item label="编号">
                                            {{ item.serialNumber }}
                                        </el-descriptions-item>
                                        <el-descriptions-item label="产品">
                                            {{ item.productName }}
                                        </el-descriptions-item>
                                        <el-descriptions-item label="上报时间">
                                            {{ parseTime(item.lastTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                                        </el-descriptions-item>
                                    </el-descriptions>
                                </el-col>
                                <el-col :span="7">
                                    <div style="margin-top: 10px">
                                        <el-image
                                            style="width: 100%; height: 100px; border-radius: 10px"
                                            lazy
                                            :preview-src-list="[baseUrl + item.imgUrl]"
                                            :src="baseUrl + item.imgUrl"
                                            fit="cover"
                                            v-if="item.imgUrl != null && item.imgUrl != ''"
                                        ></el-image>
                                        <el-image
                                            style="width: 100%; height: 100px; border-radius: 10px"
                                            :preview-src-list="[require('@/assets/images/gateway.png')]"
                                            :src="require('@/assets/images/gateway.png')"
                                            fit="cover"
                                            v-else-if="item.deviceType == 2"
                                        ></el-image>
                                        <el-image
                                            style="width: 100%; height: 100px; border-radius: 10px"
                                            :preview-src-list="[require('@/assets/images/video.png')]"
                                            :src="require('@/assets/images/video.png')"
                                            fit="cover"
                                            v-else-if="item.deviceType == 3"
                                        ></el-image>
                                        <el-image
                                            style="width: 100%; height: 100px; border-radius: 10px"
                                            :preview-src-list="[require('@/assets/images/product.png')]"
                                            :src="require('@/assets/images/product.png')"
                                            fit="cover"
                                            v-else
                                        ></el-image>
                                    </div>
                                </el-col>
                            </el-row>
                            <el-button-group style="margin-top: 15px">
                                <el-button type="danger" size="mini" style="padding: 5px 10px" icon="el-icon-delete" @click="handleDelete(item, $event)" v-hasPermi="['iot:device:remove']">删除</el-button>
                                <el-button type="primary" size="mini" style="padding: 5px 15px" icon="el-icon-view" @click="handleEditDevice(item, 'basic', $event)" v-hasPermi="['iot:device:query']">查看</el-button>
                                <el-button type="success" size="mini" style="padding: 5px 15px" icon="el-icon-odometer" @click="handleRunDevice(item, $event)" v-hasPermi="['iot:device:query']">运行状态</el-button>
                            </el-button-group>
                        </el-card>
                    </div>
                </el-col>
            </el-row>
            <el-empty description="暂无数据，请添加设备" v-if="total == 0"></el-empty>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" :pageSizes="[12, 24, 36, 60]" @pagination="getList" />
        </el-card>
        <!-- 二维码 -->
        <el-dialog :close-on-click-modal="false" :visible.sync="openSummary" width="300px" append-to-body>
            <div style="border: 1px solid #ccc; width: 220px; text-align: center; margin: 0 auto; margin-top: -15px">
                <vue-qr :text="qrText" :size="200"></vue-qr>
                <div style="padding-bottom: 10px">设备二维码</div>
            </div>
        </el-dialog>
        <!-- 批量导入设备 -->
        <batchImport ref="batchImport" @save="saveDialog"></batchImport>
        <!-- 导入分配 -->
        <allotImport ref="allotImport" @save="saveAllotDialog"></allotImport>
        <!-- 导入记录 -->
        <importRecord ref="importRecord"></importRecord>
        <!-- 设备回收记录 -->
        <recycleRecord ref="recycleRecord"></recycleRecord>
        <!-- 设备分配记录 -->
        <allotRecord ref="allotRecord"></allotRecord>
    </div>
</template>

<script>
import vueQr from 'vue-qr';
import { listDeviceShort, delDevice } from '@/api/iot/device';
import { deptsTreeSelect } from '@/api/system/user';
import { listScenario } from '@/api/iot/scenario';
import { listShortProduct } from '@/api/iot/product';
import { listGroup } from '@/api/iot/group';
import { delSipDeviceBySipId } from '@/api/iot/sipdevice';
import auth from '@/plugins/auth';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import batchImport from './batch-import-dialog';
import allotImport from './allot-import-dialog';
import importRecord from './import-record';
import recycleRecord from './recycle-record';
import allotRecord from './allot-record';
export default {
    name: 'Device',
    dicts: ['iot_device_status', 'iot_is_enable', 'iot_location_way', 'iot_transport_type'],
    components: {
        vueQr,
        Treeselect,
        batchImport,
        allotImport,
        importRecord,
        recycleRecord,
        allotRecord,
    },
    data() {
        return {
            // 机构列表
            deptOptions: [],
            // 场景列表
            scenarioOptions: [],
            // 二维码内容
            qrText: 'seefy',
            // 打开设备配置对话框
            openSummary: false,
            // 显示搜索条件
            showSearch: true,
            // 展示方式
            showType: 'card',
            // 遮罩层
            loading: true,
            // 总条数
            total: 0,
            // 设备列表数据
            deviceList: [],
            // 我的分组列表数据
            myGroupList: [],
            // 根路径
            baseUrl: process.env.VUE_APP_BASE_API,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 12,
                showChild: true,
                deviceName: null,
                productId: null,
                groupId: null,
                productName: null,
                userId: null,
                userName: null,
                tenantId: null,
                tenantName: null,
                serialNumber: null,
                status: null,
                networkAddress: null,
                activeTime: null,
                scenarioId: null,
                deptId: null,
                scenarioName: null,
            },
            // 表单参数
            form: {
                productId: 0,
                status: 1,
                locationWay: 1,
                firmwareVersion: 1.0,
                serialNumber: '',
                deviceType: 1,
                isSimulate: 0,
            },
            productList: [],
            // 批量导入参数
            isSubDev: false,
            selectedDeviceId: [], // 修改：将 selectedDeviceId 改为数组
        };
    },
    created() {
        //获取机构下拉树
        this.getDeptTree();
        // 产品筛选
        let productId = this.$route.query.productId;
        if (productId != null) {
            this.queryParams.productId = Number(productId);
            this.queryParams.groupId = null;
            this.queryParams.serialNumber = null;
        }
        //产品筛选
        let query1 = {
            pageSize: 999,
        };
        listShortProduct(query1).then((response) => {
            console.log(response, '产品');
            this.productList = response.data;
        });
        // 分组筛选
        let groupId = this.$route.query.groupId;
        if (groupId != null) {
            this.queryParams.groupId = Number(groupId);
            this.queryParams.productId = null;
            this.queryParams.serialNumber = null;
        }
        // 设备编号筛选
        let sn = this.$route.query.sn;
        if (sn != null) {
            this.queryParams.serialNumber = sn;
            this.queryParams.productId = null;
            this.queryParams.groupId = null;
        }
        this.connectMqtt();
        console.log('created函数内');
    },
    activated() {
        const time = this.$route.query.t;
        if (time != null && time != this.uniqueId) {
            this.uniqueId = time;
            // 页码筛选
            let pageNum = this.$route.query.pageNum;
            if (pageNum != null) {
                this.queryParams.pageNum = Number(pageNum);
            }
            // 产品筛选
            let productId = this.$route.query.productId;
            if (productId != null) {
                this.queryParams.productId = Number(productId);
                this.queryParams.groupId = null;
                this.queryParams.serialNumber = null;
            }
            // 分组筛选
            let groupId = this.$route.query.groupId;
            if (groupId != null) {
                this.queryParams.groupId = Number(groupId);
                this.queryParams.productId = null;
                this.queryParams.serialNumber = null;
            }
            // 设备编号筛选
            let sn = this.$route.query.sn;
            if (sn != null) {
                this.queryParams.serialNumber = sn;
                this.queryParams.productId = null;
                this.queryParams.groupId = null;
            }
            this.getList();
            console.log('activated函数');
        }
    },
    methods: {
        /** 获取机构下拉树 */
        getDeptTree() {
            deptsTreeSelect().then((response) => {
                this.deptOptions = response.data;
            });
        },
        /* 连接Mqtt消息服务器 */
        async connectMqtt() {
            if (this.$mqttTool.client == null) {
                await this.$mqttTool.connect();
            }
            this.mqttCallback();
            const conditions = JSON.parse(localStorage.getItem('queryConditions'));
            const results = JSON.parse(localStorage.getItem('queryResults'));
            if (conditions) {
                this.queryParams = conditions; // 假设有一个方法用于根据条件获取数据，如fetchData(conditions) {}。确保该方法存在并正确实现。
                if (results) {
                    // 如果需要恢复结果到UI，可以在这里操作。例如更新列表等。
                    this.getList(); // 如果没有保存的查询条件，则执行默认的查询。
                }
            } else {
                this.getList(); // 如果没有保存的查询条件，则执行默认的查询。
            }
        },
        /* Mqtt回调处理  */
        mqttCallback() {
            this.$mqttTool.client.on('message', (topic, message, buffer) => {
                let topics = topic.split('/');
                let productId = topics[1];
                let deviceNum = topics[2];
                message = JSON.parse(message.toString());
                if (!message) {
                    return;
                }
                if (topics[3] == 'status') {
                    console.log('接收到【设备状态】主题：', topic);
                    console.log('接收到【设备状态】内容：', message);
                    // 更新列表中设备的状态
                    for (let i = 0; i < this.deviceList.length; i++) {
                        if (this.deviceList[i].serialNumber == deviceNum) {
                            this.deviceList[i].status = message.status;
                            this.deviceList[i].isShadow = message.isShadow;
                            this.deviceList[i].rssi = message.rssi;
                            return;
                        }
                    }
                }
            });
        },
        // 新增设备更多操作触发
        handleCommand(command) {
            switch (command) {
                case 'handleEditDevice':
                    this.handleEditDevice(0);
                    break;
                case 'handleBatchImport':
                    this.handleBatchImport();
                    break;
                default:
                    break;
            }
        },
        //批量导入设备
        handleBatchImport() {
            this.$refs.batchImport.upload.importDeviceDialog = true;
            this.$refs.batchImport.importForm.productId = null;
        },
        // 新增：批量删除设备
        batchDeleteDevice() {
            if (this.selectedDeviceId.length === 0) {
                this.$modal.msgWarning('请选择设备');
                return;
            }
            this.$modal
                .confirm('是否确认删除已选择的设备？')
                .then(() => {
                    return delDevice(this.selectedDeviceId.join(','));
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                    this.selectedDeviceId = []; // 清空选中设备列表
                })
                .catch(() => {});
        },
        // 新增：处理表格复选框选中变化
        handleSelectionChange(selection) {
            this.selectedDeviceId = selection.map((item) => item.deviceId);
        },
        //导入分配设备
        handleImportAllot() {
            this.$refs.allotImport.upload.importAllotDialog = true;
            this.$refs.allotImport.allotForm.productId = null;
            this.$refs.allotImport.allotForm.deptId = null;
        },
        // dialog 保存响应
        saveDialog() {
            this.getList();
        },
        // dialog 保存响应
        saveAllotDialog() {
            this.getList();
        },
        // 分配设备更多操作触发
        handleCommand1(command) {
            switch (command) {
                case 'handleSelectAllot':
                    this.handleSelectAllot();
                    break;
                case 'handleImportAllot':
                    this.handleImportAllot();
                    break;
                default:
                    break;
            }
        },
        //跳转选择分配设备页面
        handleSelectAllot() {
            this.$router.push({
                path: '/iot/device-select-allot',
            });
        },
        //跳转回收设备页面
        recycleDevice() {
            this.$router.push({
                path: '/iot/device-recycle',
            });
        },
        //更多操作
        handleCommandMore(command) {
            switch (command) {
                case 'importRecord':
                    this.handleImportRecord();
                    break;
                case 'exportDevice':
                    this.handleexportDevice();
                    break;
                case 'recycleRecord':
                    this.handleRecycleRecord();
                    break;
                case 'allotRecord':
                    this.handleAllotRecord();
                    break;
                default:
                    break;
            }
        },
        //导入记录
        handleImportRecord() {
            this.$refs.importRecord.open = true;
        },
        //设备回收记录
        handleRecycleRecord() {
            this.$refs.recycleRecord.open = true;
        },
        //设备分配记录
        handleAllotRecord() {
            this.$refs.allotRecord.open = true;
        },

        openSummaryDialog(row) {
            let json = {
                type: 1, // 1=扫码关联设备
                deviceNumber: row.serialNumber,
                productId: row.productId,
                productName: row.productName,
            };
            this.qrText = JSON.stringify(json);
            this.openSummary = true;
        },
        /* 订阅消息 */
        mqttSubscribe(list) {
            // 订阅当前页面设备状态和实时监测
            let topics = [];
            for (let i = 0; i < list.length; i++) {
                let topicStatus = '/' + '+' + '/' + list[i].serialNumber + '/status/post';
                topics.push(topicStatus);
            }
            this.$mqttTool.subscribe(topics);
        },
        /** 查询设备分组列表 */
        getGroupList() {
            this.loading = true;
            let queryParams = {
                pageSize: 30,
                pageNum: 1,
                userId: this.$store.state.user.userId,
            };
            listGroup(queryParams).then((response) => {
                this.myGroupList = response.rows;
            });
        },
        /** 查询所有简短设备列表 */
        getList() {
            this.loading = true;
            this.queryParams.params = {};
            this.getGroupList();
            listDeviceShort(this.queryParams).then((response) => {
                this.deviceList = response.rows;
                this.total = response.total;
                // 订阅消息
                if (this.deviceList && this.deviceList.length > 0) {
                    this.mqttSubscribe(this.deviceList);
                }
                this.saveQueryConditions(this.queryParams);
                this.saveQueryResults(this.deviceList);

                this.loading = false;
            });
        },

        saveQueryConditions(conditions) {
            localStorage.setItem('queryConditions', JSON.stringify(conditions));
        },
        saveQueryResults(results) {
            localStorage.setItem('queryResults', JSON.stringify(results));
        },

        /** 机构选择改变 */
        handleDeptChange(node) {
            // 清空场景选择
            this.queryParams.scenarioId = null;
            this.queryParams.scenarioName = null;
            this.scenarioOptions = [];

            if (node && node.id) {
                // 根据选中的机构获取对应的场景列表
                this.getScenarioList(node.id);
            }
        },
        /** 获取场景列表 */
        getScenarioList(tenantId) {
            const params = {
                tenantId: tenantId,
            };
            listScenario(params).then((response) => {
                this.scenarioOptions = response.rows || response.data || [];
            });
        },
        /** 场景选择改变 */
        handleScenarioChange(value) {
            if (value) {
                // 设置场景名称
                const selectedScenario = this.scenarioOptions.find((item) => item.scenarioId === value);
                this.queryParams.scenarioName = selectedScenario ? selectedScenario.scenarioName : '';
            } else {
                this.queryParams.scenarioName = '';
            }
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.queryParams.productId = null;
            this.queryParams.groupId = null;
            this.queryParams.serialNumber = null;
            this.resetForm('queryForm');
            this.handleQuery();
        },
        /** 切换显示方式 */
        handleChangeShowType() {
            this.selectedDeviceId = [];
            this.showType = this.showType == 'card' ? 'list' : 'card';
        },
        // 点击名称查看
        handleDeviceDetail(item) {
            if (auth.hasPermi('iot:device:query')) {
                this.handleEditDevice(item);
            }
        },
        /** 修改按钮操作 */
        handleEditDevice(row, activeName, $event) {
            event.stopPropagation();
            let deviceId = 0;
            let isSubDev = 0;
            if (row != 0) {
                deviceId = row.deviceId || this.ids;
                isSubDev = row.subDeviceCount > 0 ? 1 : 0;
            }
            this.$router.push({
                path: '/iot/device-edit',
                query: {
                    deviceId: deviceId,
                    isSubDev: isSubDev,
                    pageNum: this.queryParams.pageNum,
                    activeName: activeName,
                },
            });
        },
        /** 运行状态按钮操作 */
        handleRunDevice(row, event) {
            event.stopPropagation();
            let deviceId = 0;
            let isSubDev = 0;
            if (row != 0) {
                deviceId = row.deviceId || this.ids;
                isSubDev = row.subDeviceCount > 0 ? 1 : 0;
            }
            if (row.deviceType === 3) {
                this.$router.push({
                    path: '/iot/device-edit',
                    query: {
                        deviceId: deviceId,
                        isSubDev: isSubDev,
                        pageNum: this.queryParams.pageNum,
                        activeName: 'sipChannel',
                    },
                });
            } else {
                this.$router.push({
                    path: '/iot/device-edit',
                    query: {
                        deviceId: deviceId,
                        isSubDev: isSubDev,
                        pageNum: this.queryParams.pageNum,
                        activeName: 'runningStatus',
                    },
                });
            }
        },
        /** 删除按钮操作 */
        handleDelete(row, event) {
            if (event) {
                event.stopPropagation();
            }
            const deviceIds = row.deviceId || this.ids;
            this.$modal
                .confirm('是否确认删除设备编号为"' + deviceIds + '"的数据项？')
                .then(function () {
                    if (row.deviceType === 3) {
                        delSipDeviceBySipId(row.serialNumber);
                    }
                    return delDevice(deviceIds);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 未启用设备影子*/
        shadowUnEnable(device, thingsModel) {
            // 1-未激活，2-禁用，3-在线，4-离线
            if (device.status != 3 && device.isShadow == 0) {
                return true;
            }
            if (thingsModel.isReadonly) {
                return true;
            }
            return false;
        },
        choicedevice(item) {
            // 修改：将 item.deviceId 添加到 selectedDeviceId 数组中
            if (!this.selectedDeviceId.includes(item.deviceId)) {
                this.selectedDeviceId.push(item.deviceId);
            } else {
                // 如果已经选中，则取消选中
                this.selectedDeviceId = this.selectedDeviceId.filter((id) => id !== item.deviceId);
            }
        },
    },
};
</script>

<style scoped>
.parent-container {
    overflow: visible; /* 或者 auto 或 scroll，取决于你的需求 */
}

.treeselect-dropdown {
    z-index: 1000 !important;
}

.treeselect-dropdown {
    z-index: 1000; /* 确保这个值高于其他可能覆盖的元素 */
}

.treeselect-dropdown {
    position: absolute; /* 或者 relative，取决于你的布局 */
    left: 0; /* 根据需要调整 */
    top: 100%; /* 通常设置为相对于触发元素的底部 */
}

.card-item {
    border-radius: 15px;
}

.selected-card {
    border: 1px solid red;
    /* 新增：选中时的样式 */
}

::v-deep .el-upload-dragger {
    width: 510px;
}

.el-dropdown-menu__item {
    font-size: 12px;
}
</style>
