<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['iot:metadata:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['iot:metadata:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['iot:metadata:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['iot:metadata:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="metadataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="名称" align="center" prop="name" />
      <el-table-column label="类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rule_script_action" :value="scope.row.type" size="small" />
        </template>
      </el-table-column>
      <el-table-column label="数据json" align="center" prop="dataJson" />
      <el-table-column label="是否生效" align="center" prop="enable">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.iot_is_enable" :value="scope.row.enable" size="small" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['iot:metadata:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['iot:metadata:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改规则引擎元数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择脚本动作" @change="selectAction()" style="width: 100%">
            <el-option v-for="dict in dict.type.rule_script_action" :key="dict.label" :label="dict.label"
              :value="Number(dict.value)"></el-option>
          </el-select>
        </el-form-item>

        <!-- 原有类型的表单项 -->
        <el-form-item label="推送地址" prop="dataJson.url" v-if="form.type == 3">
          <el-input v-model="form.dataJson.url" placeholder="请输入推送地址" />
        </el-form-item>
        <el-form-item label="推送地址" prop="dataJson.broker" v-if="form.type == 4">
          <el-input v-model="form.dataJson.broker" placeholder="请输入推送地址" />
        </el-form-item>
        <el-form-item label="客户端id" prop="dataJson.clientId" v-if="form.type == 4">
          <el-input v-model="form.dataJson.clientId" placeholder="请输入客户端id" />
        </el-form-item>
        <el-form-item label="用户名" prop="dataJson.username" v-if="form.type == 4">
          <el-input v-model="form.dataJson.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="dataJson.password" v-if="form.type == 4">
          <el-input v-model="form.dataJson.password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="主题" prop="dataJson.routeTopic" v-if="form.type == 4">
          <el-input v-model="form.dataJson.routeTopic" placeholder="请输入主题" />
        </el-form-item>
        <el-form-item label="消息质量" prop="dataJson.qos" v-if="form.type == 4">
          <el-input v-model="form.dataJson.qos" placeholder="请输入消息质量" />
        </el-form-item>
        <!-- 请求体 -->
        <el-form-item label="消息模板" v-if="form.type == 4">
            <span class="form-label-tooltip">
              <el-tooltip content="&{}为名称位占位符" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
          <el-input v-model="form.dataJson.requestBody" type="textarea" :rows="8"
                    placeholder='请输入JSON格式的请求体，例如：{"serviceId": 1, "deviceId": "&{deviceId}", "payload": {"temperature": "&{temperature}"}}' />
        </el-form-item>
        <el-form-item label="推送地址" prop="dataJson.url" v-if="form.type == 6">
          <el-input v-model="form.dataJson.url" placeholder="请输入推送地址" />
        </el-form-item>
<!--        <el-form-item label="主题" prop="dataJson.tempTopic" v-if="form.type == 6">-->
<!--          <el-input v-model="form.dataJson.tempTopic" placeholder="请输入主题" />-->
<!--        </el-form-item>-->
        <el-form-item label="定时表达式" prop="dataJson.cron" v-if="form.type == 6">
          <el-input v-model="form.dataJson.cron" placeholder="请输入定时表达式" />
        </el-form-item>

        <!-- Http自定义推送 (type=7) 的表单项 -->
        <template v-if="form.type == 7 || form.type == 8">
          <el-form-item label="定时表达式" prop="dataJson.cron" v-if="form.type == 8">
            <el-input v-model="form.dataJson.cron" placeholder="请输入定时表达式" />
          </el-form-item>
          <!-- 请求地址 -->
          <el-form-item label="请求地址" prop="dataJson.hostUrl">
            <el-input v-model="form.dataJson.hostUrl" placeholder="请输入请求地址">
              <el-select v-model="form.httpProtocol" slot="prepend" style="width: 100px">
                <el-option label="http://" value="http://"></el-option>
                <el-option label="https://" value="https://"></el-option>
              </el-select>
            </el-input>
          </el-form-item>

          <!-- 请求方法 -->
          <el-form-item label="请求方法" prop="dataJson.method">
            <el-select v-model="form.dataJson.method" placeholder="请选择请求方法" style="width: 100%">
              <el-option label="GET" value="GET"></el-option>
              <el-option label="POST" value="POST"></el-option>
            </el-select>
          </el-form-item>

          <!-- 请求头 -->
          <el-form-item label="请求头">
            <div v-for="(item, index) in form.requestHeadersList" :key="'header_' + item.id"
              style="margin-bottom: 10px">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-input v-model="item.key" placeholder="请输入键名" />
                </el-col>
                <el-col :span="10">
                  <el-input v-model="item.value" placeholder="请输入键值" />
                </el-col>
                <el-col :span="4">
                  <el-button v-if="form.requestHeadersList.length > 1" type="danger" size="small" icon="el-icon-delete"
                    @click="removeRequestHeader(index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addRequestHeader">添加请求头</el-button>
            </div>
          </el-form-item>

          <!-- Token请求地址 -->
          <el-form-item label="Token请求地址" prop="dataJson.tokenUrl">
            <el-input v-model="form.dataJson.tokenUrl" placeholder="请输入Token请求地址">
              <el-select v-model="form.tokenProtocol" slot="prepend" style="width: 100px">
                <el-option label="http://" value="http://"></el-option>
                <el-option label="https://" value="https://"></el-option>
              </el-select>
            </el-input>
          </el-form-item>

          <!-- 请求配置 -->
          <el-form-item label="请求配置" v-if="form.tokenUrl != ''">
            <div v-for="(item, index) in form.requestConfigList" :key="'config_' + item.id" style="margin-bottom: 10px">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-input v-model="item.key" placeholder="请输入配置名" />
                </el-col>
                <el-col :span="10">
                  <el-input v-model="item.value" placeholder="请输入配置值" />
                </el-col>
                <el-col :span="4">
                  <el-button v-if="form.requestConfigList.length > 1" type="danger" size="small" icon="el-icon-delete"
                             @click="removeRequestConfig(index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addRequestConfig">添加请求配置</el-button>
            </div>
          </el-form-item>

          <!-- 请求参数 -->
          <el-form-item label="请求参数">
            <div v-for="(item, index) in form.requestQueryList" :key="'query_' + item.id" style="margin-bottom: 10px">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-input v-model="item.key" placeholder="请输入参数名" />
                </el-col>
                <el-col :span="10">
                  <el-input v-model="item.value" placeholder="请输入参数值" />
                </el-col>
                <el-col :span="4">
                  <el-button v-if="form.requestQueryList.length > 1" type="danger" size="small" icon="el-icon-delete"
                    @click="removeRequestQuery(index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addRequestQuery">添加请求参数</el-button>
            </div>
          </el-form-item>

          <!-- 请求体 -->
          <el-form-item label="请求体">
            <span class="form-label-tooltip">
              <el-tooltip content="&{}为名称位占位符" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
            <el-input v-model="form.dataJson.requestBody" type="textarea" :rows="8"
              placeholder='请输入JSON格式的请求体，例如：{"serviceId": 1, "deviceId": "&{deviceId}", "payload": {"temperature": "&{temperature}"}}' />
          </el-form-item>

          <!-- 计算公式 -->
          <el-form-item label="计算公式">
            <span class="form-label-tooltip">
              <el-tooltip placement="top">
                <div slot="content">
                  %s为计算占位符(设备上行数据经计算公式计算后显示)<br />
                  公式中的%s为占位符，是固定字段。<br />
                  如：<br />
                  加：%s+10<br />
                  减：%s-10<br />
                  乘：%s*10<br />
                  除：%s/10<br />
                  除(保留小数)：%s%10.00
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
            <div v-for="(item, index) in form.formulaList" :key="'formula_' + item.id" style="margin-bottom: 10px">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-input v-model="item.key" placeholder="请输入字段名" />
                </el-col>
                <el-col :span="10">
                  <el-input v-model="item.value" placeholder="请输入计算公式，如：%s*10" />
                </el-col>
                <el-col :span="4">
                  <el-button v-if="form.formulaList.length > 1" type="danger" size="small" icon="el-icon-delete"
                    @click="removeFormula(index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
            <div>
              <el-button type="text" icon="el-icon-plus" @click="addFormula">添加计算公式</el-button>
            </div>
          </el-form-item>
        </template>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMetadata, getMetadata, delMetadata, addMetadata, updateMetadata } from '@/api/iot/metadata';

export default {
  name: 'Metadata',
  dicts: ['rule_script_action', 'iot_is_enable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 规则引擎元数据表格数据
      metadataList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 唯一ID计数器
      idCounter: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        type: null,
        dataJson: null,
        enable: null,
      },
      // 表单参数
      form: {
        id: null,
        type: null,
        name: null,
        enable: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        // 协议前缀
        httpProtocol: 'http://',
        tokenProtocol: 'http://',
        // 动态列表
        requestHeadersList: [],
        requestQueryList: [],
        requestConfigList: [],
        formulaList: [],
        dataJson: {
          url: '',
          broker: '',
          clientId: '',
          username: '',
          password: '',
          routeTopic: '',
          qos: null,
          // tempTopic: '',
          cron: '',
          // Http自定义推送字段
          hostUrl: '',
          method: 'POST',
          requestHeaders: {},
          tokenUrl: '',
          requestQuery: {},
          requestConfig: {},
          requestBody: '',
          formula: {},
        },
        // httpJson
        httpJson: {
          url: '',
        },
        // mqttJson
        mqttJson: {
          broker: '',
          clientId: '',
          username: '',
          password: '',
          routeTopic: '',
          qos: 0,
          requestBody: '',
        },
        // timePushJson
        timePushJson: {
          url: '',
          // tempTopic: '',
          cron: '',
        },
        // httpCustomJson - Http自定义推送
        httpCustomJson: {
          hostUrl: '',
          method: 'POST',
          requestHeaders: {},
          tokenUrl: '',
          requestQuery: {},
          requestConfig: {},
          requestBody: '',
          formula: {},
        },
      },
      // 表单校验
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
        // 修改: 校验规则绑定到 form.dataJson 的对应字段
        dataJson: {
          url: [{ required: true, message: '推送地址不能为空', trigger: 'blur' }],
          broker: [{ required: true, message: '推送地址不能为空', trigger: 'blur' }],
          clientId: [{ required: true, message: '客户端id不能为空', trigger: 'blur' }],
          username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
          password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
          routeTopic: [{ required: true, message: '主题不能为空', trigger: 'blur' }],
          qos: [{ required: true, message: '消息质量不能为空', trigger: 'blur' }],
          // tempTopic: [{ required: true, message: '主题不能为空', trigger: 'blur' }],
          cron: [{ required: true, message: '定时表达式不能为空', trigger: 'blur' }],
          // Http自定义推送验证规则
          hostUrl: [{ required: true, message: '请求地址不能为空', trigger: 'blur' }],
          method: [{ required: true, message: '请求方法不能为空', trigger: 'change' }],
          // tokenUrl: [{ required: true, message: 'Token请求地址不能为空', trigger: 'blur' }],
        },
      },
    };
  },
  created() {
    this.getList();
    // 初始化动态列表
    this.initDynamicLists();
  },
  methods: {
    /** 初始化动态列表 */
    initDynamicLists() {
      this.form.requestHeadersList = [this.$options._base.observable({ id: this.generateId(), key: '', value: '' })];
      this.form.requestQueryList = [this.$options._base.observable({ id: this.generateId(), key: '', value: '' })];
      this.form.requestConfigList = [this.$options._base.observable({ id: this.generateId(), key: '', value: '' })];
      this.form.formulaList = [this.$options._base.observable({ id: this.generateId(), key: '', value: '' })];
    },

    /** 查询规则引擎元数据列表 */
    getList() {
      this.loading = true;
      listMetadata(this.queryParams).then((response) => {
        this.metadataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        type: null,
        name: null,
        dataJson: null,
        enable: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        // 协议前缀
        httpProtocol: 'http://',
        tokenProtocol: 'http://',
        // 动态列表
        requestHeadersList: [],
        requestQueryList: [],
        requestConfigList: [],
        formulaList: [],
        // httpJson
        httpJson: {
          url: '',
        },
        // mqttJson
        mqttJson: {
          broker: '',
          clientId: '',
          username: '',
          password: '',
          routeTopic: '',
          qos: 0,
          requestBody: '',
        },
        // timePushJson
        timePushJson: {
          url: '',
          // tempTopic: '',
          cron: '',
        },
        // httpCustomJson - Http自定义推送
        httpCustomJson: {
          hostUrl: '',
          method: 'POST',
          requestHeaders: {},
          tokenUrl: '',
          requestQuery: {},
          requestConfig: {},
          requestBody: '',
          formula: {},
        },
      };
      this.resetForm('form');
      // 重新初始化动态列表
      this.initDynamicLists();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm');
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /**选择产品 */
    selectAction() {
      if (3 === this.form.type) {
        // HTTP
        this.form.dataJson = this.form.httpJson;
      }
      if (4 === this.form.type) {
        // MQTT
        this.form.dataJson = this.form.mqttJson;
      }
      if (6 === this.form.type) {
        // 定时推送
        this.form.dataJson = this.form.timePushJson;
      }
      if (7 === this.form.type || 8 === this.form.type) {
        // Http自定义推送
        this.form.dataJson = this.form.httpCustomJson;
        // 初始化动态列表
        this.initDynamicLists();
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = '添加规则引擎元数据';
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMetadata(id).then((response) => {
        this.form = response.data;
        this.form.dataJson = JSON.parse(this.form.dataJson);

        // 处理Http自定义推送的数据回显
        if (this.form.type == 7 || this.form.type == 8) {
          // 解析URL前缀
          if (this.form.dataJson.hostUrl) {
            if (this.form.dataJson.hostUrl.startsWith('https://')) {
              this.form.httpProtocol = 'https://';
              this.form.dataJson.hostUrl = this.form.dataJson.hostUrl.replace('https://', '');
            } else if (this.form.dataJson.hostUrl.startsWith('http://')) {
              this.form.httpProtocol = 'http://';
              this.form.dataJson.hostUrl = this.form.dataJson.hostUrl.replace('http://', '');
            }
          }

          if (this.form.dataJson.tokenUrl) {
            if (this.form.dataJson.tokenUrl.startsWith('https://')) {
              this.form.tokenProtocol = 'https://';
              this.form.dataJson.tokenUrl = this.form.dataJson.tokenUrl.replace('https://', '');
            } else if (this.form.dataJson.tokenUrl.startsWith('http://')) {
              this.form.tokenProtocol = 'http://';
              this.form.dataJson.tokenUrl = this.form.dataJson.tokenUrl.replace('http://', '');
            }
          }

          // 将对象转换为动态列表
          this.form.requestHeadersList = this.convertObjectToList(this.form.dataJson.requestHeaders);
          this.form.requestQueryList = this.convertObjectToList(this.form.dataJson.requestQuery);
          this.form.requestConfigList = this.convertObjectToList(this.form.dataJson.requestConfig);
          this.form.formulaList = this.convertObjectToList(this.form.dataJson.formula);

          // 处理请求体，如果是对象则转换为JSON字符串
          if (this.form.dataJson.requestBody && typeof this.form.dataJson.requestBody === 'object') {
            this.form.dataJson.requestBody = JSON.stringify(this.form.dataJson.requestBody, null, 2);
          }
        }

        // 修改: 确保 dataJson 中的字段被正确验证
        if (this.form.type == 3 && !this.form.dataJson.url) {
          this.$message.error('推送地址不能为空');
          return;
        }
        if (
          this.form.type == 4 &&
          (!this.form.dataJson.broker || !this.form.dataJson.clientId || !this.form.dataJson.username || !this.form.dataJson.password || !this.form.dataJson.routeTopic || this.form.dataJson.qos === null)
        ) {
          this.$message.error('MQTT相关字段不能为空');
          return;
        }
        if (this.form.type == 6 && (!this.form.dataJson.url || !this.form.dataJson.cron)) {
          this.$message.error('定时推送相关字段不能为空');
          return;
        }
        this.open = true;
        this.title = '修改规则引擎元数据';
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // 处理Http自定义推送的数据
          if (7 === this.form.type || 8 === this.form.type) {
            // 组装完整的URL（避免重复拼接协议）
            if (this.form.dataJson.hostUrl && !this.form.dataJson.hostUrl.startsWith('http://') && !this.form.dataJson.hostUrl.startsWith('https://')) {
              this.form.dataJson.hostUrl = this.form.httpProtocol + this.form.dataJson.hostUrl;
            }
            if (this.form.dataJson.tokenUrl && !this.form.dataJson.tokenUrl.startsWith('http://') && !this.form.dataJson.tokenUrl.startsWith('https://')) {
              this.form.dataJson.tokenUrl = this.form.tokenProtocol + this.form.dataJson.tokenUrl;
            }

            // 将动态列表转换为对象
            this.form.dataJson.requestHeaders = this.convertListToObject(this.form.requestHeadersList);
            this.form.dataJson.requestQuery = this.convertListToObject(this.form.requestQueryList);
            this.form.dataJson.requestConfig = this.convertListToObject(this.form.requestConfigList);
            this.form.dataJson.formula = this.convertListToObject(this.form.formulaList);

            // 处理请求体，如果是字符串则尝试解析为JSON
            if (this.form.dataJson.requestBody && typeof this.form.dataJson.requestBody === 'string') {
              try {
                this.form.dataJson.requestBody = JSON.parse(this.form.dataJson.requestBody);
              } catch (e) {
                this.$message.error('请求体格式不正确，请输入有效的JSON格式');
                return;
              }
            }
          }

          this.form.dataJson = JSON.stringify(this.form.dataJson);
          if (this.form.id != null) {
            updateMetadata(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功');
              this.open = false;
              this.getList();
            });
          } else {
            addMetadata(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功');
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除规则引擎元数据编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMetadata(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess('删除成功');
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'iot/metadata/export',
        {
          ...this.queryParams,
        },
        `metadata_${new Date().getTime()}.xlsx`
      );
    },

    // Http自定义推送相关方法
    /** 生成唯一ID */
    generateId() {
      return ++this.idCounter;
    },

    /** 添加请求头 */
    addRequestHeader() {
      const newItem = this.$options._base.observable({ id: this.generateId(), key: '', value: '' });
      this.form.requestHeadersList.push(newItem);
      // 强制更新视图
      this.$forceUpdate();
    },
    /** 删除请求头 */
    removeRequestHeader(index) {
      this.form.requestHeadersList.splice(index, 1);
      // 强制更新视图
      this.$forceUpdate();
    },

    /** 添加请求参数 */
    addRequestQuery() {
      const newItem = this.$options._base.observable({ id: this.generateId(), key: '', value: '' });
      this.form.requestQueryList.push(newItem);
      // 强制更新视图
      this.$forceUpdate();
    },
    /** 删除请求参数 */
    removeRequestQuery(index) {
      this.form.requestQueryList.splice(index, 1);
      // 强制更新视图
      this.$forceUpdate();
    },

    /** 添加请求配置 */
    addRequestConfig() {
      const newItem = this.$options._base.observable({ id: this.generateId(), key: '', value: '' });
      this.form.requestConfigList.push(newItem);
      // 强制更新视图
      this.$forceUpdate();
    },
    /** 删除请求配置 */
    removeRequestConfig(index) {
      this.form.requestConfigList.splice(index, 1);
      // 强制更新视图
      this.$forceUpdate();
    },

    /** 添加计算公式 */
    addFormula() {
      const newItem = this.$options._base.observable({ id: this.generateId(), key: '', value: '' });
      this.form.formulaList.push(newItem);
      // 强制更新视图
      this.$forceUpdate();
    },
    /** 删除计算公式 */
    removeFormula(index) {
      this.form.formulaList.splice(index, 1);
      // 强制更新视图
      this.$forceUpdate();
    },

    /** 将动态列表转换为对象 */
    convertListToObject(list) {
      const obj = {};
      list.forEach(item => {
        if (item.key && item.value) {
          obj[item.key] = item.value;
        }
      });
      return obj;
    },

    /** 将对象转换为动态列表 */
    convertObjectToList(obj) {
      const list = [];
      if (obj && typeof obj === 'object') {
        Object.keys(obj).forEach(key => {
          // 使用Vue.observable确保对象的响应性
          const item = this.$options._base.observable({ id: this.generateId(), key: key, value: obj[key] });
          list.push(item);
        });
      }
      if (list.length === 0) {
        const item = this.$options._base.observable({ id: this.generateId(), key: '', value: '' });
        list.push(item);
      }
      return list;
    },
  },
};
</script>

<style scoped>
.form-label-tooltip {
  margin-left: 5px;
}

.form-label-tooltip .el-icon-question {
  color: #909399;
  cursor: help;
}

.form-label-tooltip .el-icon-question:hover {
  color: #409eff;
}
</style>
