/**
 * Performance Optimization Example
 * Demonstrates the performance improvements implemented in task 9
 */

import AsyncLocationDetector from './AsyncLocationDetector.js';
import PerformanceMonitor from './PerformanceMonitor.js';

/**
 * Example usage of optimized location detection with performance monitoring
 */
export async function demonstratePerformanceOptimizations() {
    console.log('=== Location Detection Performance Optimization Demo ===');

    // Initialize the optimized async location detector
    const asyncDetector = new AsyncLocationDetector({
        enablePerformanceMonitoring: true,
        enableSmartCaching: true,
        enableTimeoutOptimization: true,
        enableProgressiveTimeout: true,
        maxConcurrentDetections: 1,
    });

    try {
        console.log('\n1. Non-blocking location detection...');
        const startTime = performance.now();

        // This won't block the UI thread
        const locationData = await asyncDetector.detectLocationNonBlocking({
            priority: 'background',
            queueIfBusy: true,
        });

        const endTime = performance.now();
        console.log(`✓ Location detected in ${(endTime - startTime).toFixed(2)}ms`);
        console.log(`  - City: ${locationData.city}`);
        console.log(`  - Source: ${locationData.source}`);
        console.log(`  - Coordinates: ${locationData.latitude}, ${locationData.longitude}`);

        // Demonstrate caching optimization
        console.log('\n2. Testing cache optimization...');
        const cacheStartTime = performance.now();

        // This should use cached result
        const cachedLocationData = await asyncDetector.detectLocation();

        const cacheEndTime = performance.now();
        console.log(`✓ Cached location retrieved in ${(cacheEndTime - cacheStartTime).toFixed(2)}ms`);

        // Get performance report
        console.log('\n3. Performance metrics...');
        const performanceReport = asyncDetector.getPerformanceReport();

        console.log('Summary:');
        console.log(`  - Total attempts: ${performanceReport.summary.totalAttempts}`);
        console.log(`  - Success rate: ${performanceReport.summary.successRate.toFixed(1)}%`);
        console.log(`  - Average time: ${performanceReport.summary.averageTime.toFixed(0)}ms`);
        console.log(`  - Cache hit rate: ${performanceReport.summary.cacheHitRate.toFixed(1)}%`);

        if (performanceReport.recommendations.length > 0) {
            console.log('\nRecommendations:');
            performanceReport.recommendations.forEach((rec, index) => {
                console.log(`  ${index + 1}. [${rec.priority}] ${rec.message}`);
                console.log(`     Metric: ${rec.metric}`);
            });
        }

        // Demonstrate concurrent detection management
        console.log('\n4. Testing concurrent detection management...');
        const promises = [];

        for (let i = 0; i < 3; i++) {
            promises.push(
                asyncDetector
                    .detectLocation({ queueIfBusy: true })
                    .then((result) => ({ index: i, success: true, result }))
                    .catch((error) => ({ index: i, success: false, error: error.message }))
            );
        }

        const results = await Promise.all(promises);
        results.forEach((result) => {
            if (result.success) {
                console.log(`  ✓ Detection ${result.index}: ${result.result.city} (${result.result.source})`);
            } else {
                console.log(`  ✗ Detection ${result.index}: ${result.error}`);
            }
        });

        // Show current status
        console.log('\n5. Current system status...');
        const status = asyncDetector.getStatus();
        console.log(`  - Active detections: ${status.activeDetections}`);
        console.log(`  - Queued detections: ${status.queuedDetections}`);
        console.log(`  - Consecutive failures: ${status.consecutiveFailures}`);
        console.log(`  - Performance monitoring: ${status.performanceMonitor.enabled ? 'enabled' : 'disabled'}`);

        return {
            success: true,
            locationData,
            performanceReport,
            status,
        };
    } catch (error) {
        console.error('❌ Performance optimization demo failed:', error);
        return {
            success: false,
            error: error.message,
        };
    } finally {
        // Cleanup
        asyncDetector.destroy();
        console.log('\n✓ Cleanup completed');
    }
}

/**
 * Example of timeout optimization in action
 */
export async function demonstrateTimeoutOptimization() {
    console.log('\n=== Timeout Optimization Demo ===');

    const detector = new AsyncLocationDetector({
        enableTimeoutOptimization: true,
        timeoutOptimization: {
            enabled: true,
            baseTimeout: 5000,
            minTimeout: 2000,
            maxTimeout: 10000,
            adaptiveTimeout: true,
        },
    });

    try {
        // Simulate multiple detection attempts to show adaptive timeout
        for (let i = 0; i < 3; i++) {
            console.log(`\nAttempt ${i + 1}:`);
            const startTime = performance.now();

            try {
                const result = await detector.detectLocation();
                const endTime = performance.now();
                console.log(`  ✓ Success in ${(endTime - startTime).toFixed(0)}ms`);
                console.log(`  - Optimal timeout calculated: ${detector.calculateOptimalTimeout()}ms`);
            } catch (error) {
                const endTime = performance.now();
                console.log(`  ✗ Failed in ${(endTime - startTime).toFixed(0)}ms: ${error.message}`);
                console.log(`  - Consecutive failures: ${detector.consecutiveFailures}`);
                console.log(`  - Next timeout will be: ${detector.calculateOptimalTimeout()}ms`);
            }
        }
    } finally {
        detector.destroy();
    }
}

/**
 * Example of smart caching optimization
 */
export async function demonstrateSmartCaching() {
    console.log('\n=== Smart Caching Demo ===');

    const detector = new AsyncLocationDetector({
        enableSmartCaching: true,
        smartCache: {
            enabled: true,
            adaptiveExpiration: true,
            baseExpiration: 30 * 60 * 1000, // 30 minutes
            maxExpiration: 2 * 60 * 60 * 1000, // 2 hours
            minExpiration: 5 * 60 * 1000, // 5 minutes
        },
    });

    try {
        console.log('\n1. First detection (will be cached):');
        const result1 = await detector.detectLocation();
        console.log(`  ✓ Location: ${result1.city} (${result1.source})`);

        console.log('\n2. Second detection (should use cache):');
        const result2 = await detector.detectLocation();
        console.log(`  ✓ Location: ${result2.city} (${result2.source})`);

        console.log('\n3. Cache performance:');
        const report = detector.getPerformanceReport();
        console.log(`  - Cache hit rate: ${report.summary.cacheHitRate.toFixed(1)}%`);

        // Show cache statistics if available
        if (detector.locationService.cache && detector.locationService.cache.getStats) {
            const cacheStats = detector.locationService.cache.getStats();
            console.log(`  - Cache access count: ${cacheStats.accessCount}`);
            console.log(`  - Cache hit count: ${cacheStats.hitCount}`);
            console.log(`  - Cache exists: ${cacheStats.exists}`);
        }
    } finally {
        detector.destroy();
    }
}

// Export for use in development/testing
export default {
    demonstratePerformanceOptimizations,
    demonstrateTimeoutOptimization,
    demonstrateSmartCaching,
};
