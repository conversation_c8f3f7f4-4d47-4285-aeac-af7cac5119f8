<template>
    <div class="location-detection-ui">
        <!-- Loading indicator during location detection -->
        <transition name="fade">
            <div v-if="isDetecting" class="location-loading-indicator">
                <div class="loading-content">
                    <div class="loading-spinner">
                        <i class="el-icon-location-outline spinning"></i>
                    </div>
                    <span class="loading-text">正在定位...</span>
                </div>
            </div>
        </transition>

        <!-- Success notification when location is detected -->
        <transition name="slide-down">
            <div v-if="showSuccessNotification" class="location-success-notification">
                <div class="notification-content">
                    <i class="el-icon-location success-icon"></i>
                    <span class="notification-text">已定位到 {{ detectedLocation }}</span>
                    <button @click="hideSuccessNotification" class="close-btn">
                        <i class="el-icon-close"></i>
                    </button>
                </div>
            </div>
        </transition>

        <!-- Manual refresh button -->
        <div class="location-controls">
            <el-tooltip content="重新定位" placement="top">
                <el-button type="text" icon="el-icon-refresh" @click="refreshLocation" :loading="isDetecting"
                    class="refresh-location-btn" :disabled="isDetecting">
                    定位
                </el-button>
            </el-tooltip>
        </div>

        <!-- Error notification for graceful degradation -->
        <transition name="slide-down">
            <div v-if="showErrorNotification" class="location-error-notification">
                <div class="notification-content">
                    <i class="el-icon-warning error-icon"></i>
                    <span class="notification-text">{{ errorMessage }}</span>
                    <button @click="hideErrorNotification" class="close-btn">
                        <i class="el-icon-close"></i>
                    </button>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
export default {
    name: 'LocationDetectionUI',
    props: {
        // Whether location detection is currently in progress
        isDetecting: {
            type: Boolean,
            default: false
        },
        // The detected location name for success notification
        detectedLocation: {
            type: String,
            default: ''
        },
        // Error message for error notification
        errorMessage: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            showSuccessNotification: false,
            showErrorNotification: false,
            successNotificationTimer: null,
            errorNotificationTimer: null
        }
    },
    watch: {
        // Show success notification when location is detected
        detectedLocation(newLocation, oldLocation) {
            if (newLocation && newLocation !== oldLocation && !this.isDetecting) {
                this.showLocationSuccess()
            }
        },
        // Show error notification when error occurs
        errorMessage(newError, oldError) {
            if (newError && newError !== oldError) {
                this.showLocationError()
            }
        },
        // Hide notifications when detection starts
        isDetecting(isDetecting) {
            if (isDetecting) {
                this.hideAllNotifications()
            }
        }
    },
    methods: {
        /**
         * Show success notification with auto-hide
         */
        showLocationSuccess() {
            this.hideAllNotifications()
            this.showSuccessNotification = true

            // Auto-hide after 4 seconds
            this.successNotificationTimer = setTimeout(() => {
                this.hideSuccessNotification()
            }, 4000)
        },

        /**
         * Show error notification with auto-hide
         */
        showLocationError() {
            this.hideAllNotifications()
            this.showErrorNotification = true

            // Auto-hide after 6 seconds (longer for error messages)
            this.errorNotificationTimer = setTimeout(() => {
                this.hideErrorNotification()
            }, 6000)
        },

        /**
         * Hide success notification
         */
        hideSuccessNotification() {
            this.showSuccessNotification = false
            if (this.successNotificationTimer) {
                clearTimeout(this.successNotificationTimer)
                this.successNotificationTimer = null
            }
        },

        /**
         * Hide error notification
         */
        hideErrorNotification() {
            this.showErrorNotification = false
            if (this.errorNotificationTimer) {
                clearTimeout(this.errorNotificationTimer)
                this.errorNotificationTimer = null
            }
        },

        /**
         * Hide all notifications
         */
        hideAllNotifications() {
            this.hideSuccessNotification()
            this.hideErrorNotification()
        },

        /**
         * Emit refresh location event
         */
        refreshLocation() {
            if (!this.isDetecting) {
                this.$emit('refresh-location')
            }
        }
    },
    beforeDestroy() {
        // Clean up timers
        this.hideAllNotifications()
    }
}
</script>

<style scoped>
.location-detection-ui {
    position: relative;
}

/* Loading indicator styles */
.location-loading-indicator {
    position: fixed;
    top: 80px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    backdrop-filter: blur(8px);
}

.loading-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinning {
    animation: spin 1s linear infinite;
    color: #409eff;
    font-size: 16px;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
}

/* Success notification styles */
.location-success-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
    z-index: 1000;
    max-width: 300px;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.success-icon {
    color: #0ea5e9;
    font-size: 16px;
    flex-shrink: 0;
}

.notification-text {
    font-size: 14px;
    color: #0c4a6e;
    flex: 1;
}

.close-btn {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    transition: all 0.2s;
    flex-shrink: 0;
}

.close-btn:hover {
    background: rgba(100, 116, 139, 0.1);
    color: #475569;
}

/* Error notification styles */
.location-error-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border: 1px solid #f87171;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(248, 113, 113, 0.2);
    z-index: 1000;
    max-width: 300px;
}

.error-icon {
    color: #f87171;
    font-size: 16px;
    flex-shrink: 0;
}

/* Location controls */
.location-controls {
    position: absolute;
    top: 0;
    right: 0;
}

.refresh-location-btn {
    color: #64748b;
    font-size: 12px;
    padding: 4px 8px;
    transition: all 0.2s;
}

.refresh-location-btn:hover:not(:disabled) {
    color: #409eff;
    background: rgba(64, 158, 255, 0.1);
}

.refresh-location-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

.slide-down-enter-active {
    transition: all 0.3s ease;
}

.slide-down-leave-active {
    transition: all 0.3s ease;
}

.slide-down-enter {
    transform: translateY(-20px);
    opacity: 0;
}

.slide-down-leave-to {
    transform: translateY(-20px);
    opacity: 0;
}

/* Responsive design */
@media (max-width: 768px) {

    .location-loading-indicator,
    .location-success-notification,
    .location-error-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .notification-text {
        font-size: 13px;
    }
}
</style>