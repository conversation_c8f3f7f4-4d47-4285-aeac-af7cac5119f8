<template>
  <div id="regionTree" style="border-right: 1px solid #EBEEF5; height: 100%">
    <div style="padding: 0 20px 0 10px;">
      <el-input size="small" v-model="searchStr" @input="searchChange" suffix-icon="el-icon-search" placeholder="请输入搜索内容"
        clearable>
        <!--        <el-select v-model="searchType" slot="prepend" placeholder="搜索类型" style="width: 80px">-->
        <!--          <el-option label="目录" :value="0"></el-option>-->
        <!--          <el-option label="通道" :value="1"></el-option>-->
        <!--        </el-select>-->
      </el-input>
    </div>
    <div v-if="!searchStr">
      <el-alert v-if="showAlert && edit" title="操作提示" description="你可以使用右键菜单管理节点" type="info" style="text-align: left" />
      <div v-if="edit" style="float: right;margin-right: 24px;margin-top: 18px; font-size: 14px">
        显示编号： <el-checkbox v-model="showCode" />
      </div>

      <vue-easy-tree ref="veTree" class="flow-tree" node-key="treeId" :height="treeHeight ? treeHeight : '78vh'" lazy
        :load="loadNode" :data="treeData" :props="props" :default-expanded-keys="[]"
        @node-contextmenu="contextmenuEventHandler" @node-click="nodeClickHandler">
        <template v-slot:default="{ node, data }" class="custom-tree-node">
          <span class="custom-tree-node">
            <span v-if="node.data.type === 0 && chooseId !== node.data.deviceId" style="color: #409EFF"
              class="iconfont icon-bianzubeifen3" />
            <span v-if="node.data.type === 0 && chooseId === node.data.deviceId" style="color: #c60135;"
              class="iconfont icon-bianzubeifen3" />
            <span v-if="node.data.type === 1 && node.data.status === 'ON'" style="color: #409EFF"
              class="iconfont icon-shexiangtou2" />
            <span v-if="node.data.type === 1 && node.data.status !== 'ON'" style="color: #808181"
              class="iconfont icon-shexiangtou2" />
            <span v-if="node.data.deviceId !== '' && showCode && node.data.type === 0" style=" padding-left: 1px"
              :title="`部门ID: ${node.data.deviceId}`">{{
                node.label }}（部门编号：{{ node.data.deviceId }}）</span>
            <span v-if="node.data.deviceId !== '' && showCode && node.data.type === 1" style=" padding-left: 1px"
              :title="`设备ID: ${node.data.deviceId}`">{{
                node.label }}（编号：{{ node.data.deviceId }}）</span>
            <span v-if="node.data.deviceId === '' || !showCode" style=" padding-left: 1px"
              :title="node.data.type === 0 ? `部门: ${node.label}` : `设备: ${node.label}`">{{
                node.label }}</span>
          </span>
        </template>
      </vue-easy-tree>
    </div>
    <div v-if="searchStr" style="color: #606266; height: calc(100% - 32px); overflow: auto !important;">
      <ul v-if="regionList.length > 0" style="list-style: none; margin: 0; padding: 10px">
        <li v-for="item in regionList" :key="item.id" class="channel-list-li"
          style="height: 26px; align-items: center;cursor: pointer;" @click="listClickHandler(item)">
          <span v-if="chooseId !== item.deviceId" style="color: #409EFF;  font-size: 20px"
            class="iconfont icon-bianzubeifen3" />
          <span v-if="chooseId === item.deviceId" style="color: #c60135;  font-size: 20px"
            class="iconfont icon-bianzubeifen3" />
          <div>
            <div style="margin-left: 4px; margin-bottom: 3px; font-size: 15px">{{ item.name }}</div>
            <div style="margin-left: 4px; font-size: 13px; color: #808181">部门编号：{{ item.deviceId }}</div>
          </div>
        </li>
      </ul>

      <ul v-if="channelList.length > 0" style="list-style: none; margin: 0; padding: 10px; overflow: auto">
        <li v-for="item in channelList" :key="item.id" class="channel-list-li" @click="channelLstClickHandler(item)">
          <span v-if="item.gbStatus === 'ON'" style="color: #409EFF; font-size: 20px"
            class="iconfont icon-shexiangtou2" />
          <span v-if="item.gbStatus !== 'ON'" style="color: #808181; font-size: 20px"
            class="iconfont icon-shexiangtou2" />
          <div>
            <div style="margin-left: 4px; margin-bottom: 3px; font-size: 15px">{{ item.gbName }}</div>
            <div style="margin-left: 4px; font-size: 13px; color: #808181">{{ item.gbDeviceId }}</div>
          </div>

        </li>
      </ul>
      <div v-if="this.currentPage * this.count < this.total" style="text-align: center;">
        <el-button type="text" @click="loadListMore">加载更多</el-button>
      </div>
    </div>
    <regionEdit ref="regionEdit" />
    <gbDeviceSelect ref="gbDeviceSelect" />
    <GbChannelSelect ref="gbChannelSelect" data-type="civilCode" />
  </div>
</template>

<script>
import VueEasyTree from '@wchbrad/vue-easy-tree'
import regionEdit from './../dialog/regionEdit.vue'
import gbDeviceSelect from './../dialog/GbDeviceSelect'
import GbChannelSelect from './../dialog/GbChannelSelect.vue'
import chooseCivilCode from './../dialog/chooseCivilCode.vue'

export default {
  name: 'DeviceTree',
  components: {
    GbChannelSelect,
    VueEasyTree, regionEdit, gbDeviceSelect
  },
  props: ['edit', 'enableAddChannel', 'showHeader', 'hasChannel', 'addChannelToCivilCode', 'treeHeight'],
  data() {
    return {
      props: {
        label: 'name',
        children: 'children'
      },
      searchType: 0,
      showCode: false,
      showAlert: true,
      searchStr: '',
      chooseId: '',
      treeData: [],
      currentPage: this.defaultPage | 1,
      count: this.defaultCount | 15,
      total: 0,
      regionList: [],
      channelList: [],
      searchTimeout: null,
      // Error handling state
      errorState: {
        hasError: false,
        errorMessage: '',
        errorType: null,
        retryCount: 0,
        maxRetries: 3,
        isRetrying: false,
        fallbackMode: false
      },
      // Loading states for better UX during error recovery
      loadingStates: {
        tree: false,
        search: false,
        channels: false
      }
    }
  },
  created() {
    // Check API availability on component creation
    this.checkApiAvailability();
  },
  destroyed() {
    // Clean up search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
      this.searchTimeout = null
    }
    // if (this.jessibuca) {
    //   this.jessibuca.destroy();
    // }
    // this.playing = false;
    // this.loaded = false;
    // this.performance = "";
  },
  methods: {
    /**
     * Handle errors with appropriate user feedback and fallback behavior
     * @param {Object} error - Error object with type and message
     * @param {string} context - Context where error occurred
     * @param {Function} retryCallback - Function to call for retry
     */
    handleError(error, context = '', retryCallback = null) {
      console.error(`部门树错误 [${context}]:`, error);

      // Update error state
      this.errorState.hasError = true;
      this.errorState.errorType = error.type || 'API_ERROR';
      this.errorState.errorMessage = error.message || '操作失败，请稍后重试';

      // Store error in Vuex for debugging
      this.$store.commit('video/department/SET_LAST_ERROR', {
        ...error,
        context,
        timestamp: new Date().toISOString()
      });

      // Determine user feedback based on error type
      let userMessage = this.errorState.errorMessage;
      let messageType = 'error';

      switch (error.type) {
        case 'NETWORK_ERROR':
          userMessage = '网络连接异常，请检查网络后重试';
          messageType = 'warning';
          break;
        case 'TIMEOUT_ERROR':
          userMessage = '请求超时，请稍后重试';
          messageType = 'warning';
          break;
        case 'AUTH_ERROR':
          userMessage = '认证失败，请刷新页面重新登录';
          messageType = 'error';
          break;
        case 'DATA_ERROR':
          userMessage = '数据格式异常，请联系管理员';
          messageType = 'error';
          break;
        default:
          userMessage = error.message || '操作失败，请稍后重试';
      }

      // Show user feedback
      this.$message({
        message: userMessage,
        type: messageType,
        duration: 5000,
        showClose: true
      });

      // Offer retry option for retryable errors
      if (retryCallback && this.isRetryableError(error) && this.errorState.retryCount < this.errorState.maxRetries) {
        this.$confirm(
          `${userMessage}\n\n是否重试？`,
          '操作失败',
          {
            confirmButtonText: '重试',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          this.retryOperation(retryCallback, context);
        }).catch(() => {
          // User cancelled retry, try fallback
          this.tryFallback(context);
        });
      } else {
        // No retry available or max retries reached, try fallback
        this.tryFallback(context);
      }
    },

    /**
     * Check if an error is retryable
     * @param {Object} error - Error object
     * @returns {boolean} Whether the error is retryable
     */
    isRetryableError(error) {
      const retryableTypes = ['NETWORK_ERROR', 'TIMEOUT_ERROR'];
      return retryableTypes.includes(error.type);
    },

    /**
     * Retry an operation with exponential backoff
     * @param {Function} operation - Operation to retry
     * @param {string} context - Context for logging
     */
    retryOperation(operation, context) {
      this.errorState.isRetrying = true;
      this.errorState.retryCount++;

      const delay = Math.pow(2, this.errorState.retryCount - 1) * 1000; // Exponential backoff

      console.log(`重试操作 [${context}] (${this.errorState.retryCount}/${this.errorState.maxRetries})`);

      setTimeout(() => {
        operation();
      }, delay);
    },

    /**
     * Try fallback behavior when primary operation fails
     * @param {string} context - Context where fallback is needed
     */
    tryFallback(context) {
      console.log(`尝试备用方案 [${context}]`);

      switch (context) {
        case 'loadTree':
          this.loadFallbackTree();
          break;
        case 'searchDepartments':
          this.loadFallbackSearch();
          break;
        case 'loadChannels':
          this.loadFallbackChannels();
          break;
        default:
          console.warn(`没有可用的备用方案: ${context}`);
      }
    },

    /**
     * Load fallback tree data
     */
    loadFallbackTree() {
      const fallbackData = this.$store.getters['video/department/getFallbackDeptTrees'];

      if (fallbackData && fallbackData.length > 0) {
        console.log('使用备用部门树数据');
        const transformedData = this.transformDeptTreeData(fallbackData);
        this.treeData = transformedData;
        this.errorState.fallbackMode = true;
        this.$message({
          message: '已切换到离线模式，显示缓存数据',
          type: 'info',
          duration: 3000
        });
      } else {
        // Create minimal fallback structure
        this.createMinimalFallback();
      }
    },

    /**
     * Load fallback search results
     */
    loadFallbackSearch() {
      if (this.edit) {
        // For department search, use cached tree data
        const fallbackData = this.$store.getters['video/department/getFallbackDeptTrees'];
        if (fallbackData && fallbackData.length > 0) {
          const filteredDepts = this.filterDepartmentsBySearch(fallbackData, this.searchStr);
          const flattenedDepts = this.flattenDepartmentTree(filteredDepts);
          const transformedData = this.transformDeptTreeData(flattenedDepts);
          this.regionList = transformedData.slice(0, this.count);
          this.total = transformedData.length;
          this.errorState.fallbackMode = true;
        } else {
          this.regionList = [];
          this.total = 0;
        }
      } else {
        // For channel search, clear results
        this.channelList = [];
        this.total = 0;
      }
    },

    /**
     * Load fallback channel data
     */
    loadFallbackChannels() {
      // Clear channel list as fallback
      this.channelList = [];
      this.total = 0;
      this.$message({
        message: '无法加载通道数据，请稍后重试',
        type: 'warning',
        duration: 3000
      });
    },

    /**
     * Create minimal fallback tree structure
     */
    createMinimalFallback() {
      console.log('创建最小备用树结构');
      this.treeData = [{
        treeId: 'fallback-root',
        deviceId: 'fallback-root',
        name: '部门树暂时不可用',
        type: 0,
        isLeaf: true,
        children: []
      }];
      this.errorState.fallbackMode = true;
      this.$message({
        message: '部门树服务暂时不可用，请稍后刷新页面',
        type: 'warning',
        duration: 5000,
        showClose: true
      });
    },

    /**
     * Clear error state
     */
    clearError() {
      this.errorState.hasError = false;
      this.errorState.errorMessage = '';
      this.errorState.errorType = null;
      this.errorState.retryCount = 0;
      this.errorState.isRetrying = false;
      this.errorState.fallbackMode = false;
      this.$store.commit('video/department/CLEAR_LAST_ERROR');
    },

    /**
     * Reset component to initial state
     */
    resetToInitialState() {
      this.clearError();
      this.treeData = [];
      this.regionList = [];
      this.channelList = [];
      this.currentPage = 1;
      this.total = 0;
      this.searchStr = '';

      // Reset loading states
      Object.keys(this.loadingStates).forEach(key => {
        this.loadingStates[key] = false;
      });
    },

    /**
     * Transform department tree data to match existing tree structure
     * @param {Array} deptData - Department tree data from API
     * @returns {Array} Transformed tree data
     */
    transformDeptTreeData(deptData) {
      if (!Array.isArray(deptData)) {
        return []
      }

      return deptData.map(dept => {
        const hasChildren = dept.children && Array.isArray(dept.children) && dept.children.length > 0

        return {
          treeId: dept.deptId ? dept.deptId.toString() : (dept.id ? dept.id.toString() : ''),
          deviceId: dept.deptId ? dept.deptId.toString() : (dept.id ? dept.id.toString() : ''),
          name: dept.deptName || dept.label || '',
          type: 0, // Department type
          children: hasChildren ? this.transformDeptTreeData(dept.children) : undefined,
          // Department nodes are never leaf nodes because they might have channels
          // even if they don't have child departments
          isLeaf: false,
          id: dept.deptId || dept.id,
          // Store original children data for lazy loading
          _originalChildren: dept.children
        }
      })
    },

    /**
     * Transform channel data to match existing channel structure
     * @param {Array} channelData - Channel data from API
     * @returns {Array} Transformed channel data
     */
    transformChannelData(channelData) {
      if (!Array.isArray(channelData)) {
        return []
      }

      return channelData.map(channel => ({
        treeId: `channel_${channel.gbId || channel.id}`, // Ensure unique treeId for channels
        id: channel.gbId || channel.id,
        deviceId: channel.gbDeviceId || channel.deviceId || '',
        name: channel.gbName || channel.name || '',
        gbStatus: channel.gbStatus || 'OFF',
        gbName: channel.gbName || channel.name || '',
        gbDeviceId: channel.gbDeviceId || channel.deviceId || '',
        // Map status for tree display (tree uses 'status', search uses 'gbStatus')
        status: channel.gbStatus || 'OFF',
        type: 1, // Channel type
        isLeaf: true // Channels are always leaf nodes
      }))
    },

    /**
     * Filter departments recursively based on search string
     * @param {Array} departments - Department tree data
     * @param {string} searchStr - Search string
     * @returns {Array} Filtered departments
     */
    filterDepartmentsBySearch(departments, searchStr) {
      if (!Array.isArray(departments) || !searchStr) {
        return departments || []
      }

      const filtered = []

      for (const dept of departments) {
        // Check if current department matches search
        const matches = dept.label && dept.label.toLowerCase().includes(searchStr.toLowerCase())

        // Recursively filter children
        const filteredChildren = this.filterDepartmentsBySearch(dept.children, searchStr)

        // Include department if it matches or has matching children
        if (matches || (filteredChildren && filteredChildren.length > 0)) {
          filtered.push({
            ...dept,
            children: filteredChildren
          })
        }
      }

      return filtered
    },

    /**
     * Flatten department tree structure for search results display
     * @param {Array} departments - Department tree data
     * @returns {Array} Flattened department list
     */
    flattenDepartmentTree(departments) {
      if (!Array.isArray(departments)) {
        return []
      }

      const flattened = []

      for (const dept of departments) {
        // Add current department
        flattened.push({
          ...dept,
          children: undefined // Remove children for flat display
        })

        // Recursively add children
        if (dept.children && Array.isArray(dept.children)) {
          const flattenedChildren = this.flattenDepartmentTree(dept.children)
          flattened.push(...flattenedChildren)
        }
      }

      return flattened
    },

    /**
     * Get all department IDs from department tree
     * @param {Array} departments - Department tree data
     * @returns {Array} Array of department IDs
     */
    getAllDepartmentIds(departments) {
      if (!Array.isArray(departments)) {
        return []
      }

      const ids = []

      for (const dept of departments) {
        if (dept.id) {
          ids.push(dept.id)
        }

        // Recursively get children IDs
        if (dept.children && Array.isArray(dept.children)) {
          const childIds = this.getAllDepartmentIds(dept.children)
          ids.push(...childIds)
        }
      }

      return ids
    },

    /**
     * Get department IDs that match the search criteria
     * @param {Array} departments - Department tree data
     * @param {string} searchStr - Search string
     * @returns {Array} Array of matching department IDs
     */
    getMatchingDepartmentIds(departments, searchStr) {
      if (!Array.isArray(departments) || !searchStr) {
        return this.getAllDepartmentIds(departments)
      }

      const matchingIds = []

      for (const dept of departments) {
        // Check if current department matches search
        const matches = dept.label && dept.label.toLowerCase().includes(searchStr.toLowerCase())

        if (matches && dept.id) {
          matchingIds.push(dept.id)
        }

        // Recursively check children
        if (dept.children && Array.isArray(dept.children)) {
          const childIds = this.getMatchingDepartmentIds(dept.children, searchStr)
          matchingIds.push(...childIds)
        }
      }

      return matchingIds
    },

    /**
     * Search channels across multiple departments with enhanced error handling
     * @param {Array} deptIds - Array of department IDs
     * @returns {Promise<Array>} Promise resolving to array of all channels
     */
    async searchChannelsAcrossDepartments(deptIds) {
      const allChannels = [];
      const failedDepartments = [];
      let successCount = 0;

      // Limit concurrent requests to avoid overwhelming the server
      const batchSize = 3; // Reduced batch size for better error handling
      const batches = [];

      for (let i = 0; i < deptIds.length; i += batchSize) {
        batches.push(deptIds.slice(i, i + batchSize));
      }

      console.log(`开始搜索 ${deptIds.length} 个部门的通道，分 ${batches.length} 批处理`);

      // Process batches sequentially to control load and handle errors better
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`处理第 ${batchIndex + 1}/${batches.length} 批，包含 ${batch.length} 个部门`);

        const channelPromises = batch.map(deptId => {
          // Get fallback data for this department
          const fallbackData = this.$store.getters['video/department/getFallbackChannelList'](deptId);

          return this.$store.dispatch('video/department/getDeptChannelList',
            { deptId },
            { fallbackData }
          )
            .then(data => {
              try {
                const transformedData = this.transformChannelData(data);

                // Store successful data as fallback
                this.$store.commit('video/department/SET_FALLBACK_CHANNEL_LIST', {
                  deptId,
                  data
                });

                successCount++;
                return { deptId, channels: transformedData, success: true };
              } catch (transformError) {
                console.warn(`转换部门 ${deptId} 通道数据失败:`, transformError);
                failedDepartments.push({ deptId, error: transformError, type: 'transform' });
                return { deptId, channels: [], success: false };
              }
            })
            .catch(error => {
              console.warn(`获取部门 ${deptId} 的通道失败:`, error);
              failedDepartments.push({ deptId, error, type: 'api' });

              // Try to use fallback data
              if (fallbackData && fallbackData.length > 0) {
                try {
                  const transformedFallback = this.transformChannelData(fallbackData);
                  console.log(`使用部门 ${deptId} 的备用通道数据`);
                  return { deptId, channels: transformedFallback, success: true, fromFallback: true };
                } catch (fallbackError) {
                  console.warn(`部门 ${deptId} 备用数据也无法使用:`, fallbackError);
                }
              }

              return { deptId, channels: [], success: false };
            });
        });

        try {
          const results = await Promise.allSettled(channelPromises);

          // Collect all results from this batch
          results.forEach(result => {
            if (result.status === 'fulfilled' && result.value.success) {
              allChannels.push(...result.value.channels);
            }
          });

          // Add small delay between batches to avoid overwhelming the server
          if (batchIndex < batches.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }
        } catch (batchError) {
          console.error(`处理第 ${batchIndex + 1} 批时发生错误:`, batchError);
          failedDepartments.push({ batch: batchIndex + 1, error: batchError, type: 'batch' });
        }
      }

      // Log summary
      console.log(`通道搜索完成: 成功 ${successCount}/${deptIds.length} 个部门，获得 ${allChannels.length} 个通道`);

      if (failedDepartments.length > 0) {
        console.warn(`${failedDepartments.length} 个部门的通道获取失败:`, failedDepartments);

        // Show warning if too many departments failed
        const failureRate = failedDepartments.length / deptIds.length;
        if (failureRate > 0.5) {
          this.$message({
            message: `部分部门通道加载失败 (${failedDepartments.length}/${deptIds.length})，搜索结果可能不完整`,
            type: 'warning',
            duration: 5000,
            showClose: true
          });
        } else if (failureRate > 0.2) {
          this.$message({
            message: `少数部门通道加载失败，搜索结果可能不完整`,
            type: 'info',
            duration: 3000
          });
        }
      }

      return allChannels;
    },

    searchChange() {
      // Clear any existing search timeout
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout)
      }

      // Reset pagination and search state
      this.currentPage = 1
      this.total = 0

      // Clear previous search results immediately
      if (this.edit) {
        this.regionList = []
      } else {
        this.channelList = []
      }

      // Only search if there's a search string
      if (this.searchStr && this.searchStr.trim()) {
        // Debounce search to avoid too many API calls
        this.searchTimeout = setTimeout(() => {
          if (this.edit) {
            this.queryRegion()
          } else {
            this.queryChannelList()
          }
        }, 300) // 300ms debounce
      }
    },
    loadListMore: function () {
      // Only load more if there are more results available
      if (this.currentPage * this.count < this.total) {
        this.currentPage += 1
        if (this.edit) {
          this.queryRegion()
        } else {
          this.queryChannelList()
        }
      }
    },
    queryRegion: function () {
      this.loadingStates.search = true;

      const searchOperation = () => {
        // Get fallback data for retry scenarios
        const fallbackData = this.$store.getters['video/department/getFallbackDeptTrees'];

        // For department tree search, we'll use the department tree data
        this.$store.dispatch('video/department/getDeptTrees', { fallbackData })
          .then(data => {
            try {
              // Filter departments based on search string
              const filteredDepts = this.filterDepartmentsBySearch(data, this.searchStr);

              // Flatten the tree structure for search results display
              const flattenedDepts = this.flattenDepartmentTree(filteredDepts);
              const transformedData = this.transformDeptTreeData(flattenedDepts);

              // Apply pagination
              const startIndex = (this.currentPage - 1) * this.count;
              const endIndex = startIndex + this.count;
              const paginatedData = transformedData.slice(startIndex, endIndex);

              this.total = transformedData.length;

              // For first page, replace the list; for subsequent pages, append
              if (this.currentPage === 1) {
                this.regionList = paginatedData;
              } else {
                this.regionList = this.regionList.concat(paginatedData);
              }

              // Clear any previous errors on success
              this.clearError();

              console.log(`部门搜索完成: 找到 ${this.total} 个结果`);
            } catch (processingError) {
              console.error('处理搜索结果失败:', processingError);
              this.$message({
                message: '搜索结果处理失败，请重试',
                type: 'warning',
                duration: 3000
              });

              // Reset search results on processing error
              this.regionList = [];
              this.total = 0;
            }
          })
          .catch(error => {
            console.error('查询部门树失败:', error);

            // Handle error with retry capability
            this.handleError(error, 'searchDepartments', searchOperation);

            // Try to use fallback data for search
            const fallbackData = this.$store.getters['video/department/getFallbackDeptTrees'];
            if (fallbackData && fallbackData.length > 0) {
              try {
                console.log('使用备用数据进行搜索');
                const filteredDepts = this.filterDepartmentsBySearch(fallbackData, this.searchStr);
                const flattenedDepts = this.flattenDepartmentTree(filteredDepts);
                const transformedData = this.transformDeptTreeData(flattenedDepts);

                const startIndex = (this.currentPage - 1) * this.count;
                const endIndex = startIndex + this.count;
                const paginatedData = transformedData.slice(startIndex, endIndex);

                this.total = transformedData.length;

                if (this.currentPage === 1) {
                  this.regionList = paginatedData;
                } else {
                  this.regionList = this.regionList.concat(paginatedData);
                }

                this.$message({
                  message: '使用缓存数据显示搜索结果',
                  type: 'info',
                  duration: 3000
                });
              } catch (fallbackError) {
                console.error('备用数据搜索失败:', fallbackError);
                this.regionList = [];
                this.total = 0;
              }
            } else {
              // Reset search results on error
              this.regionList = [];
              this.total = 0;
            }
          })
          .finally(() => {
            this.loadingStates.search = false;
            this.errorState.isRetrying = false;
          });
      };

      searchOperation();
    },
    queryChannelList: function () {
      this.loadingStates.search = true;

      const channelSearchOperation = () => {
        // Get fallback data for retry scenarios
        const fallbackData = this.$store.getters['video/department/getFallbackDeptTrees'];

        // For department-based channel search, we need to search across departments
        // First get all departments, then search their channels
        this.$store.dispatch('video/department/getDeptTrees', { fallbackData })
          .then(deptData => {
            try {
              // Get department IDs that might contain relevant channels
              // This includes departments that match the search term or all departments if searching for channels
              const relevantDeptIds = this.getMatchingDepartmentIds(deptData, this.searchStr);

              // If no departments match the search term, search all departments for channels
              const deptIdsToSearch = relevantDeptIds.length > 0 ? relevantDeptIds : this.getAllDepartmentIds(deptData);

              if (deptIdsToSearch.length === 0) {
                console.warn('没有找到可搜索的部门');
                this.channelList = [];
                this.total = 0;
                return Promise.resolve([]);
              }

              console.log(`开始搜索 ${deptIdsToSearch.length} 个部门的通道`);

              // Search channels across relevant departments
              return this.searchChannelsAcrossDepartments(deptIdsToSearch);
            } catch (processingError) {
              console.error('处理部门数据失败:', processingError);
              throw processingError;
            }
          })
          .then(allChannels => {
            try {
              // Filter channels by search string
              const filteredChannels = this.searchStr
                ? allChannels.filter(channel =>
                  (channel.name && channel.name.toLowerCase().includes(this.searchStr.toLowerCase())) ||
                  (channel.deviceId && channel.deviceId.toLowerCase().includes(this.searchStr.toLowerCase())) ||
                  (channel.gbName && channel.gbName.toLowerCase().includes(this.searchStr.toLowerCase())) ||
                  (channel.gbDeviceId && channel.gbDeviceId.toLowerCase().includes(this.searchStr.toLowerCase()))
                )
                : allChannels;

              // Apply pagination
              const startIndex = (this.currentPage - 1) * this.count;
              const endIndex = startIndex + this.count;
              const paginatedChannels = filteredChannels.slice(startIndex, endIndex);

              this.total = filteredChannels.length;

              // For first page, replace the list; for subsequent pages, append
              if (this.currentPage === 1) {
                this.channelList = paginatedChannels;
              } else {
                this.channelList = this.channelList.concat(paginatedChannels);
              }

              // Clear any previous errors on success
              this.clearError();

              console.log(`通道搜索完成: 找到 ${this.total} 个通道`);
            } catch (processingError) {
              console.error('处理通道搜索结果失败:', processingError);
              this.$message({
                message: '通道搜索结果处理失败，请重试',
                type: 'warning',
                duration: 3000
              });

              // Reset search results on processing error
              this.channelList = [];
              this.total = 0;
            }
          })
          .catch(error => {
            console.error('查询部门通道失败:', error);

            // Handle error with retry capability
            this.handleError(error, 'loadChannels', channelSearchOperation);

            // Reset search results on error
            this.channelList = [];
            this.total = 0;
          })
          .finally(() => {
            this.loadingStates.search = false;
            this.errorState.isRetrying = false;
          });
      };

      channelSearchOperation();
    },
    loadNode: function (node, resolve) {
      if (node.level === 0) {
        // Clear any previous errors when loading root
        this.clearError();
        this.loadingStates.tree = true;

        // Load root department tree with error handling
        const loadTreeOperation = () => {
          // Get fallback data for retry scenarios
          const fallbackData = this.$store.getters['video/department/getFallbackDeptTrees'];

          this.$store.dispatch('video/department/getDeptTrees', { fallbackData })
            .then(data => {
              console.log('原始部门树数据:', data);
              const transformedData = this.transformDeptTreeData(data);
              console.log('转换后的部门树数据:', transformedData);

              if (transformedData.length > 0) {
                this.showAlert = false;
                // Store successful data as fallback for future use
                this.$store.commit('video/department/SET_FALLBACK_DEPT_TREES', data);
                console.log('部门树加载成功，共', transformedData.length, '个部门');
              } else {
                console.warn('部门树数据为空，原始数据:', data);
                this.$message({
                  message: '暂无部门数据',
                  type: 'info',
                  duration: 3000
                });
              }

              this.clearError();
              resolve(transformedData);
            })
            .catch(error => {
              console.error('加载部门树失败:', error);

              // Handle error with retry capability
              this.handleError(error, 'loadTree', loadTreeOperation);

              // Always resolve with some data to prevent tree from breaking
              const fallbackData = this.$store.getters['video/department/getFallbackDeptTrees'];
              if (fallbackData && fallbackData.length > 0) {
                const transformedFallback = this.transformDeptTreeData(fallbackData);
                resolve(transformedFallback);
              } else {
                // Provide minimal fallback structure
                resolve([{
                  treeId: 'error-node',
                  deviceId: 'error-node',
                  name: '加载失败，请刷新重试',
                  type: 0,
                  isLeaf: true,
                  children: []
                }]);
              }
            })
            .finally(() => {
              this.loadingStates.tree = false;
              this.errorState.isRetrying = false;
            });
        };

        loadTreeOperation();
      } else {
        // For department nodes, load both child departments and channels
        if (node.data.type === 0 && node.data.id) {
          // Load child departments first
          let childDepartments = [];

          try {
            // Check if we have original children data to transform
            if (node.data._originalChildren && Array.isArray(node.data._originalChildren)) {
              childDepartments = this.transformDeptTreeData(node.data._originalChildren);
            } else if (node.data.children && Array.isArray(node.data.children)) {
              // Already transformed children
              childDepartments = node.data.children;
            }
          } catch (error) {
            console.error('处理子部门数据失败:', error);
            childDepartments = [];
          }

          // Load channels for this department
          console.log(`开始加载部门 ${node.data.name} (ID: ${node.data.id}) 的通道`);
          this.$store.dispatch('video/department/getDeptChannelList', {
            deptId: node.data.id
          }).then(channelData => {
            try {
              const transformedChannels = this.transformChannelData(channelData);
              console.log(`部门 ${node.data.name} 加载了 ${transformedChannels.length} 个通道:`, transformedChannels);
              console.log(`子部门数量: ${childDepartments.length}, 通道数量: ${transformedChannels.length}`);

              // Combine child departments and channels
              const allChildren = [...childDepartments, ...transformedChannels];
              console.log(`总共返回 ${allChildren.length} 个子节点给树组件`);
              resolve(allChildren);
            } catch (error) {
              console.error('处理通道数据失败:', error);
              // If channel loading fails, still show child departments
              resolve(childDepartments);
            }
          }).catch(error => {
            console.error('加载部门通道失败:', error);
            // If channel loading fails, still show child departments
            resolve(childDepartments);
          });
        } else {
          // For leaf nodes or nodes without ID
          resolve([]);
        }
      }
    },
    reset: function () {
      this.$forceUpdate()
    },
    contextmenuEventHandler: function (event, data, node, element) {
      if (!this.edit) {
        return
      }
      if (node.data.type === 0) {
        const menuItem = [
          {
            label: '刷新节点',
            icon: 'el-icon-refresh',
            disabled: false,
            onClick: () => {
              this.refreshNode(node)
            }
          },
          {
            label: '新建部门',
            icon: 'el-icon-plus',
            disabled: false,
            onClick: () => {
              this.addDepartment(data.id, node)
            }
          },
          {
            label: '编辑部门',
            icon: 'el-icon-edit',
            disabled: node.level === 1,
            onClick: () => {
              this.editDepartment(data, node)
            }
          },
          {
            label: '删除部门',
            icon: 'el-icon-delete',
            disabled: node.level === 1,
            divided: true,
            onClick: () => {
              this.$confirm('确定删除此部门?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.removeDepartment(data.id, node)
              }).catch(() => {

              })
            }
          }
        ]
        if (this.enableAddChannel) {
          menuItem.push(
            {
              label: '添加设备',
              icon: 'el-icon-plus',
              disabled: node.level === 1,
              onClick: () => {
                this.addChannelToDepartment(data.id, node)
              }
            }
          )
          menuItem.push(
            {
              label: '移除设备',
              icon: 'el-icon-delete',
              disabled: node.level === 1,
              divided: true,
              onClick: () => {
                this.removeChannelFromDepartment(data.id, node)
              }
            }
          )
          menuItem.push(
            {
              label: '添加通道',
              icon: 'el-icon-plus',
              disabled: node.level === 1,
              onClick: () => {
                this.addChannelToDepartment(data.id, node)
              }
            }
          )
        }

        this.$contextmenu({
          items: menuItem,
          event, // 鼠标事件信息
          customClass: 'custom-class', // 自定义菜单 class
          zIndex: 3000 // 菜单样式 z-index
        })
      }

      return false
    },
    removeDepartment: function (id, node) {
      // For department operations, we'll use the system department API
      // Import the department API at the top of the file if not already imported
      import('@/api/system/dept').then(deptApi => {
        deptApi.delDept(node.data.id)
          .then((data) => {
            console.log('部门删除成功');
            this.$message.success('部门删除成功');
            this.$emit('onChannelChange', node.data.deviceId);
            // Refresh parent node to reflect changes
            if (node.parent) {
              node.parent.loaded = false;
              node.parent.expand();
            } else {
              // If it's a root node, refresh the entire tree
              this.refreshNode(node);
            }
          }).catch((error) => {
            console.error('删除部门失败:', error);

            // Provide detailed error handling
            let errorMessage = '删除部门失败';
            if (error.response) {
              const status = error.response.status;
              const data = error.response.data;

              switch (status) {
                case 400:
                  errorMessage = data?.msg || '无法删除该部门，请检查是否存在子部门或关联数据';
                  break;
                case 401:
                  errorMessage = '权限不足，请重新登录';
                  break;
                case 403:
                  errorMessage = '没有权限删除此部门';
                  break;
                case 404:
                  errorMessage = '部门不存在，可能已被删除';
                  break;
                case 409:
                  errorMessage = '部门下存在子部门或关联数据，无法删除';
                  break;
                case 500:
                  errorMessage = '服务器错误，请稍后重试';
                  break;
                default:
                  errorMessage = data?.msg || `删除失败 (${status})`;
              }
            } else if (error.message) {
              if (error.message.includes('Network Error')) {
                errorMessage = '网络连接异常，请检查网络后重试';
              } else if (error.message.includes('timeout')) {
                errorMessage = '请求超时，请稍后重试';
              } else {
                errorMessage = error.message;
              }
            }

            this.$message({
              message: errorMessage,
              type: 'error',
              duration: 5000,
              showClose: true
            });
          });
      }).catch((error) => {
        console.error('加载部门API失败:', error);
        this.$message({
          message: '系统模块加载失败，请刷新页面重试',
          type: 'error',
          duration: 5000,
          showClose: true
        });
      });
    },
    addChannelToDepartment: function (id, node) {
      this.$refs.gbDeviceSelect.openDialog((rows) => {
        const deviceIds = [];
        for (let i = 0; i < rows.length; i++) {
          deviceIds.push(rows[i].id);
        }

        // For department-based operations, we'll use department ID instead of civilCode
        // This may require a new API endpoint or adaptation of existing ones
        this.$store.dispatch('video/department/addDeviceToDepartment', {
          deptId: node.data.id,
          deviceIds: deviceIds
        }).then((data) => {
          this.$message.success({
            showClose: true,
            message: '设备添加成功'
          });
          this.$emit('onChannelChange', node.data.deviceId);
          node.loaded = false;
          node.expand();
        }).catch((error) => {
          console.error('添加设备到部门失败:', error);

          // Check if fallback is available
          if (error.fallbackAvailable && error.fallbackAction) {
            this.$confirm(
              `${error.message}\n\n是否使用区域管理功能作为替代？`,
              '功能暂不可用',
              {
                confirmButtonText: '使用替代方案',
                cancelButtonText: '取消',
                type: 'warning'
              }
            ).then(() => {
              // Fallback to region-based approach
              this.$store.dispatch('video/commonChanel/addDeviceToRegion', {
                civilCode: node.data.deviceId,
                deviceIds: deviceIds
              }).then((data) => {
                this.$message.success({
                  showClose: true,
                  message: '设备添加成功（使用区域管理）'
                });
                this.$emit('onChannelChange', node.data.deviceId);
                node.loaded = false;
                node.expand();
              }).catch((fallbackError) => {
                console.error('区域管理添加设备也失败:', fallbackError);
                this.handleError(fallbackError, 'addDevice');
              });
            }).catch(() => {
              // User cancelled fallback
              console.log('用户取消了替代方案');
            });
          } else {
            // No fallback available, show error
            this.handleError(error, 'addDevice');
          }
        }).finally(() => {
          this.loading = false;
        });
      });
    },
    removeChannelFromDepartment: function (id, node) {
      this.$refs.gbDeviceSelect.openDialog((rows) => {
        const deviceIds = [];
        for (let i = 0; i < rows.length; i++) {
          deviceIds.push(rows[i].id);
        }

        // For department-based operations, we'll try department-specific API first
        this.$store.dispatch('video/department/deleteDeviceFromDepartment', {
          deptId: node.data.id,
          deviceIds: deviceIds
        }).then((data) => {
          this.$message.success({
            showClose: true,
            message: '设备移除成功'
          });
          this.$emit('onChannelChange', node.data.deviceId);
          node.loaded = false;
          node.expand();
        }).catch((error) => {
          console.error('从部门移除设备失败:', error);

          // Check if fallback is available
          if (error.fallbackAvailable && error.fallbackAction) {
            this.$confirm(
              `${error.message}\n\n是否使用区域管理功能作为替代？`,
              '功能暂不可用',
              {
                confirmButtonText: '使用替代方案',
                cancelButtonText: '取消',
                type: 'warning'
              }
            ).then(() => {
              // Fallback to region-based approach
              this.$store.dispatch('video/commonChanel/deleteDeviceFromRegion', deviceIds)
                .then((data) => {
                  this.$message.success({
                    showClose: true,
                    message: '设备移除成功（使用区域管理）'
                  });
                  this.$emit('onChannelChange', node.data.deviceId);
                  node.loaded = false;
                  node.expand();
                }).catch((fallbackError) => {
                  console.error('区域管理移除设备也失败:', fallbackError);
                  this.handleError(fallbackError, 'removeDevice');
                });
            }).catch(() => {
              // User cancelled fallback
              console.log('用户取消了替代方案');
            });
          } else {
            // No fallback available, show error
            this.handleError(error, 'removeDevice');
          }
        }).finally(() => {
          this.loading = false;
        });
      });
    },
    addChannelToDepartment: function (id, node) {
      this.$refs.gbChannelSelect.openDialog((data) => {
        console.log('选择的通道数据')
        console.log(data)
        // For department-based operations, we need to adapt the channel addition
        // to work with department context instead of civilCode
        if (this.addChannelToCivilCode && typeof this.addChannelToCivilCode === 'function') {
          // Use the existing callback but with department context
          this.addChannelToCivilCode(node.data.deviceId, data, {
            deptId: node.data.id,
            deptName: node.data.name
          })
        } else {
          // Handle channel addition directly if no callback is provided
          console.log('添加通道到部门:', node.data.name, '通道数据:', data)
          this.$message.success('通道添加成功')
          this.$emit('onChannelChange', node.data.deviceId)
        }
      })
    },
    refreshNode: function (node) {
      // For department tree, we need to reload the department data
      if (node.level === 1) {
        // Root level - reload entire department tree
        this.$store.dispatch('video/department/getDeptTrees')
          .then(data => {
            const transformedData = this.transformDeptTreeData(data)
            node.childNodes = []
            node.loaded = false
            node.expand()
          })
          .catch(error => {
            console.error('刷新部门树失败:', error)
          })
      } else {
        // Child department node - just reload from existing data
        node.loaded = false
        node.expand()
      }
    },
    refresh: function (id) {
      console.log(id)
      // 查询node - for department tree, use department ID
      const node = this.$refs.veTree.getNode(id)
      if (node) {
        // For department nodes, refresh the department data
        if (node.data.type === 0) {
          this.refreshNode(node)
        } else {
          node.loaded = false
          node.expand()
        }
      }
    },
    addDepartment: function (id, node) {
      console.log('添加部门到节点:', node);

      // For department operations, we'll create a simplified dialog
      // since the regionEdit dialog is specifically for region codes
      this.$prompt('请输入部门名称', '新建部门', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '部门名称不能为空'
      }).then(({ value }) => {
        // Use system department API to create new department
        import('@/api/system/dept').then(deptApi => {
          const deptData = {
            deptName: value,
            parentId: node.data.id,
            orderNum: 0,
            status: '0' // Active status
          };

          deptApi.addDept(deptData)
            .then((data) => {
              this.$message.success('部门创建成功');
              // Refresh the parent node to show the new department
              node.loaded = false;
              node.expand();
              this.$emit('onChannelChange', node.data.deviceId);
            })
            .catch((error) => {
              console.error('创建部门失败:', error);

              // Provide detailed error handling
              let errorMessage = '创建部门失败';
              if (error.response) {
                const status = error.response.status;
                const data = error.response.data;

                switch (status) {
                  case 400:
                    errorMessage = data?.msg || '请求参数错误，请检查部门名称';
                    break;
                  case 401:
                    errorMessage = '权限不足，请重新登录';
                    break;
                  case 403:
                    errorMessage = '没有权限创建部门';
                    break;
                  case 409:
                    errorMessage = '部门名称已存在，请使用其他名称';
                    break;
                  case 500:
                    errorMessage = '服务器错误，请稍后重试';
                    break;
                  default:
                    errorMessage = data?.msg || `创建失败 (${status})`;
                }
              } else if (error.message) {
                if (error.message.includes('Network Error')) {
                  errorMessage = '网络连接异常，请检查网络后重试';
                } else if (error.message.includes('timeout')) {
                  errorMessage = '请求超时，请稍后重试';
                } else {
                  errorMessage = error.message;
                }
              }

              this.$message({
                message: errorMessage,
                type: 'error',
                duration: 5000,
                showClose: true
              });
            });
        }).catch((error) => {
          console.error('加载部门API失败:', error);
          this.$message({
            message: '系统模块加载失败，请刷新页面重试',
            type: 'error',
            duration: 5000,
            showClose: true
          });
        });
      }).catch(() => {
        // User cancelled
      });
    },
    editDepartment: function (data, node) {
      // For department editing, we'll use a simplified prompt dialog
      // since the regionEdit dialog is specifically for region codes
      this.$prompt('请输入部门名称', '编辑部门', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: data.name,
        inputPattern: /\S+/,
        inputErrorMessage: '部门名称不能为空'
      }).then(({ value }) => {
        // Use system department API to update department
        import('@/api/system/dept').then(deptApi => {
          const deptData = {
            deptId: data.id,
            deptName: value,
            parentId: data.parentId || 0,
            orderNum: 0,
            status: '0' // Active status
          };

          deptApi.updateDept(deptData)
            .then((response) => {
              this.$message.success('部门更新成功');
              // Update the node data locally
              node.data.name = value;
              // Refresh the parent node to reflect changes
              if (node.parent) {
                node.parent.loaded = false;
                node.parent.expand();
              }
              this.$emit('onChannelChange', node.data.deviceId);
            })
            .catch((error) => {
              console.error('更新部门失败:', error);

              // Provide detailed error handling
              let errorMessage = '更新部门失败';
              if (error.response) {
                const status = error.response.status;
                const data = error.response.data;

                switch (status) {
                  case 400:
                    errorMessage = data?.msg || '请求参数错误，请检查部门名称';
                    break;
                  case 401:
                    errorMessage = '权限不足，请重新登录';
                    break;
                  case 403:
                    errorMessage = '没有权限编辑此部门';
                    break;
                  case 404:
                    errorMessage = '部门不存在，可能已被删除';
                    break;
                  case 409:
                    errorMessage = '部门名称已存在，请使用其他名称';
                    break;
                  case 500:
                    errorMessage = '服务器错误，请稍后重试';
                    break;
                  default:
                    errorMessage = data?.msg || `更新失败 (${status})`;
                }
              } else if (error.message) {
                if (error.message.includes('Network Error')) {
                  errorMessage = '网络连接异常，请检查网络后重试';
                } else if (error.message.includes('timeout')) {
                  errorMessage = '请求超时，请稍后重试';
                } else {
                  errorMessage = error.message;
                }
              }

              this.$message({
                message: errorMessage,
                type: 'error',
                duration: 5000,
                showClose: true
              });
            });
        }).catch((error) => {
          console.error('加载部门API失败:', error);
          this.$message({
            message: '系统模块加载失败，请刷新页面重试',
            type: 'error',
            duration: 5000,
            showClose: true
          });
        });
      }).catch(() => {
        // User cancelled
      });
    },
    nodeClickHandler: function (data, node, tree) {
      // For department-based structure, use department ID for tracking selection
      // This ensures proper selection highlighting with department structure
      if (data.type === 0) {
        // Department node - use department ID for selection tracking
        this.chooseId = data.deviceId || data.id?.toString() || ''

        // For department nodes, trigger expansion to show channels
        if (data.id && !node.expanded) {
          console.log(`展开部门节点: ${data.name}`);
          // Force reload the node to include channels
          node.loaded = false;
          node.expand();
        }
      } else {
        // Channel node - use device ID as before
        this.chooseId = data.deviceId || ''
      }

      // Emit click event for parent components
      if (data.type === 0 && data.id) {
        // For department nodes, emit with department info
        this.$emit('clickEvent', {
          ...data,
          selectedDeptId: data.id,
          selectedDeviceId: data.deviceId
        });
      } else if (data.type === 1) {
        // For channel nodes, emit with leaf flag and gbId (consistent with original logic)
        this.$emit('clickEvent', {
          leaf: true,
          id: data.id, // This should be gbId from the channel data
          deviceId: data.deviceId,
          name: data.name,
          gbId: data.id,
          gbDeviceId: data.deviceId,
          gbName: data.name,
          gbStatus: data.status,
          selectedDeviceId: data.deviceId
        });
      } else {
        // For other nodes without department ID
        this.$emit('clickEvent', {
          ...data,
          selectedDeviceId: data.deviceId
        });
      }
    },
    listClickHandler: function (data) {
      // For department-based search results, ensure proper ID tracking
      // Department search results should use department ID for selection
      if (data.type === 0) {
        // Department item from search results - use department ID
        this.chooseId = data.deviceId || data.id?.toString() || ''
      } else {
        // Channel item - use device ID as before
        this.chooseId = data.deviceId || ''
      }

      // Emit click event with additional department context for consistency
      this.$emit('clickEvent', {
        ...data,
        selectedDeptId: data.type === 0 ? (data.id || data.deviceId) : undefined,
        selectedDeviceId: data.deviceId
      })
    },
    channelLstClickHandler: function (data) {
      // For channel clicks from search results, set chooseId for consistent selection behavior
      this.chooseId = data.deviceId || data.gbDeviceId || ''

      this.$emit('clickEvent', {
        leaf: true,
        id: data.gbId || data.id,
        deviceId: data.deviceId || data.gbDeviceId,
        name: data.name || data.gbName,
        // Include department context if available
        selectedDeviceId: data.deviceId || data.gbDeviceId
      })
    },

    /**
     * Check API availability and update component state
     */
    checkApiAvailability() {
      this.$store.dispatch('video/department/checkApiAvailability')
        .then(availability => {
          console.log('API可用性检查完成:', availability);

          // Update local state based on availability
          const unavailableApis = Object.keys(availability).filter(api => !availability[api]);

          if (unavailableApis.length > 0) {
            console.warn('以下API不可用:', unavailableApis);

            // Show warning to user if critical APIs are unavailable
            if (!availability.getDeptTrees) {
              this.$message({
                message: '部门树服务暂时不可用，将使用缓存数据',
                type: 'warning',
                duration: 5000,
                showClose: true
              });
            }
          }
        })
        .catch(error => {
          console.error('API可用性检查失败:', error);
          // Don't show error to user for availability check failure
        });
    },

    /**
     * Refresh the entire component and clear all error states
     */
    refreshComponent() {
      console.log('刷新部门树组件');
      this.resetToInitialState();

      // Force reload the tree
      if (this.$refs.veTree) {
        this.$refs.veTree.reload();
      }

      // Re-check API availability
      this.checkApiAvailability();
    },

    /**
     * Get component health status for debugging
     */
    getHealthStatus() {
      return {
        errorState: this.errorState,
        loadingStates: this.loadingStates,
        dataState: {
          treeDataLength: this.treeData.length,
          regionListLength: this.regionList.length,
          channelListLength: this.channelList.length,
          searchStr: this.searchStr,
          currentPage: this.currentPage,
          total: this.total
        },
        lastError: this.$store.getters['video/department/lastError'],
        apiAvailability: this.$store.state.video.department.apiAvailability
      };
    }
  }
}
</script>

<style>
.device-tree-main-box {
  text-align: left;
}

.device-online {
  color: #252525;
}

.device-offline {
  color: #727272;
}

.custom-tree-node .el-radio__label {
  padding-left: 4px !important;
}

.tree-scroll {
  width: 200px;
  border: 1px solid #E7E7E7;
  height: 100%;
}

.flow-tree {
  overflow: auto;
  padding-top: 10px;
}

.flow-tree .vue-recycle-scroller__item-wrapper {
  height: 100%;
  overflow-x: auto;
}

.channel-list-li {
  height: 24px;
  align-items: center;
  cursor: pointer;
  display: grid;
  grid-template-columns: 26px 1fr;
  margin-bottom: 18px
}
</style>
