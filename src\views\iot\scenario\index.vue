<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="场景名称" prop="scenarioName">
                <el-input v-model="queryParams.scenarioName" placeholder="请输入场景名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['iot:scenario:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['iot:scenario:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['iot:scenario:remove']">批量删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['iot:scenario:export']">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="el-icon-s-operation" size="mini" @click="handleDeviceAdjust" v-hasPermi="['iot:scenario:edit']">设备调整</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="scenarioList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="场景ID" align="center" prop="scenarioId" />
            <el-table-column label="场景名称" align="center" prop="scenarioName" />
            <el-table-column label="场景类型" align="center" prop="typeName" />
            <!--      <el-table-column label="场景模板ID" align="center" prop="templateId" />-->
            <!--      <el-table-column label="状态" align="center" prop="status" />-->
            <!--      <el-table-column label="图片地址" align="center" prop="imgUrl" />-->
            <el-table-column label="机构" align="center" prop="tenantName" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['iot:scenario:edit']">修改</el-button>
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['iot:scenario:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改场景对话框 -->
        <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="1000px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="150px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="场景名称" prop="scenarioName">
                            <el-input v-model="form.scenarioName" placeholder="请输入场景名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="场景类型" prop="typeId">
                            <treeselect v-model="form.typeId" :options="typeList" :show-count="true" placeholder="请选择场景类型" :disabled="false" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="归属机构" prop="tenantId">
                            <treeselect v-model="form.tenantId" :options="deptOptions" :show-count="true" placeholder="请选择归属机构" :disabled="false" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <!-- <el-form-item label="省、市、区（县）" prop="cityAdress">
                            <el-cascader v-model="form.cityAdress" :options="optionsCity" @change="handleChange" :props="cascaderProps" style="width: 100%" />
                        </el-form-item> -->
                        <el-form-item label="所属区域" prop="cityAdress">
                            <el-cascader v-model="form.cityAdress" :options="optionsCity" @change="handleChange" :props="cascaderProps" style="width: 100%" />
                        </el-form-item>
                    </el-col>
                    <!--          <el-col :span="12">-->
                    <!--            <el-form-item label="图片地址" prop="imgUrl">-->
                    <!--              <el-input v-model="form.imgUrl" placeholder="请输入图片地址" />-->
                    <!--            </el-form-item>-->
                    <!--          </el-col>-->
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="详细地址" prop="address">
                            <el-input v-model="form.address" placeholder="请输入详细地址" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <el-tabs v-model="activeName" style="padding: 10px">
                <el-tab-pane label="场景设备" name="relateDevice">
                    <div style="text-align: right; margin-bottom: 10px" v-if="activeName == 'relateDevice'">
                        <el-button type="" plain size="mini" @click="getDeviceByScenarioId" v-hasPermi="['iot:scenario:list']">刷新</el-button>
                        <!--            <el-button type="primary" plain size="mini" :disabled="selectedDevices.length === 0"-->
                        <!--              @click="handleBatchDeviceAdjust" v-hasPermi="['iot:scenario:edit']">批量设备调整</el-button>-->
                        <!-- <el-button type="" plain size="mini" @click="addDevice" v-hasPermi="['iot:scenario:add']">添加设备</el-button> -->
                    </div>
                    <el-table :data="form.deviceList" border v-loading="deviceLoading" size="mini" @selection-change="handleDeviceSelectionChange">
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column prop="deviceName" align="center" label="设备名称"></el-table-column>
                        <el-table-column prop="serialNumber" align="center" label="设备编号"></el-table-column>
                        <el-table-column prop="productName" align="center" label="产品名称"></el-table-column>
                        <el-table-column label="创建时间" align="center" prop="createTime">
                            <template slot-scope="scope">
                                <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
                            </template>
                        </el-table-column>
                        <!--            <el-table-column label="操作" align="center" width="120">-->
                        <!--&lt;!&ndash;              <template slot-scope="scope">&ndash;&gt;-->
                        <!--&lt;!&ndash;                <el-button size="mini" type="text" icon="el-icon-s-operation"&ndash;&gt;-->
                        <!--&lt;!&ndash;                  @click="handleDeviceAdjustFromEdit(scope.row)" v-hasPermi="['iot:scenario:edit']">设备调整</el-button>&ndash;&gt;-->
                        <!--&lt;!&ndash;              </template>&ndash;&gt;-->
                        <!--            </el-table-column>-->
                    </el-table>
                </el-tab-pane>
                <!--        <el-tab-pane label="场景用户" name="relateUser">-->
                <!--          <div style="text-align: right; margin-bottom: 10px" v-if="activeName == 'relateUser'">-->
                <!--            <el-button type="" plain size="mini" @click="getUserByScenarioId"-->
                <!--              v-hasPermi="['iot:scenario:list']">刷新</el-button>-->
                <!--            <el-button type="" plain size="mini" @click="addUser" v-hasPermi="['iot:scenario:add']">添加用户</el-button>-->
                <!--          </div>-->
                <!--          <el-table :data="form.userList" border v-loading="userLoading" size="mini">-->
                <!--            <el-table-column prop="userName" align="center" label="用户名称"></el-table-column>-->
                <!--            <el-table-column label="状态" align="center" prop="status">-->
                <!--              <template slot-scope="scope">-->
                <!--                <el-tag type="success" size="small" v-if="scope.row.status == '1'">正常</el-tag>-->
                <!--                <el-tag type="danger" size="small" v-if="scope.row.status == '0'">异常</el-tag>-->
                <!--              </template>-->
                <!--            </el-table-column>-->
                <!--            <el-table-column label="操作" align="center" width="120">-->
                <!--              <template slot-scope="scope">-->
                <!--                <el-button size="mini" type="text" icon="el-icon-delete"-->
                <!--                  @click="handleScenarioUserRemove(scope.row)">移除</el-button>-->
                <!--              </template>-->
                <!--            </el-table-column>-->
                <!--          </el-table>-->
                <!--        </el-tab-pane>-->
                <!-- 用于设置间距 -->
                <!-- <el-tab-pane disabled>
                    <span slot="label">
                        <div style="margin-left: 50px"></div>
                    </span>
                </el-tab-pane>
                <el-tab-pane name="deviceButton" disabled v-if="activeName == 'relateDevice'">
                    <span slot="label">
                        <el-button type="" plain size="mini" @click="getDeviceByScenarioId" v-hasPermi="['iot:scenario:list']">刷新</el-button>
                        <el-button type="" plain size="mini" @click="addDevice" v-hasPermi="['iot:scenario:add']">添加设备</el-button>
                    </span>
                </el-tab-pane>
                <el-tab-pane name="userButton" disabled v-else>
                    <span slot="label">
                        <el-button type="" plain size="mini" @click="getUserByScenarioId" v-hasPermi="['iot:scenario:list']">刷新</el-button>
                        <el-button type="" plain size="mini" @click="addUser" v-hasPermi="['iot:scenario:add']">添加用户</el-button>
                    </span>
                </el-tab-pane> -->
            </el-tabs>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
        <!-- 选择设备对话框 -->
        <device-list ref="deviceList" @deviceEvent="getDeviceData($event)" />
        <!-- 选择用户模板 -->
        <user-list ref="userList" @userEvent="getUserData($event)" />

        <!-- 设备调整对话框 -->
        <el-dialog :close-on-click-modal="false" title="设备调整" :visible.sync="deviceAdjustDialog" width="1200px" append-to-body>
            <div class="device-adjust-container">
                <!-- 搜索条件 -->
                <div class="search-section">
                    <el-form ref="adjustQueryForm" :model="adjustQueryParams" size="small" :inline="true">
                        <!--            <el-form-item label="当前场景" prop="currentScenarioId">-->
                        <!--              <el-select v-model="adjustQueryParams.currentScenarioId" placeholder="请选择当前场景" clearable-->
                        <!--                style="width: 200px" @change="handleCurrentScenarioChange">-->
                        <!--                <el-option v-for="scenario in allScenarioList" :key="scenario.scenarioId" :label="scenario.scenarioName"-->
                        <!--                  :value="scenario.scenarioId" />-->
                        <!--              </el-select>-->
                        <!--            </el-form-item>-->
                        <!--            <el-form-item label="设备编号" prop="serialNumber">-->
                        <!--              <el-input v-model="adjustQueryParams.serialNumber" placeholder="请输入设备编号" clearable style="width: 200px"-->
                        <!--                @keyup.enter.native="handleAdjustQuery" />-->
                        <!--            </el-form-item>-->
                        <el-form-item label="所属机构" prop="filterDeptId">
                            <treeselect v-model="adjustQueryParams.filterDeptId" :options="deptOptions" :show-count="true" placeholder="请选择所属机构" style="width: 180px" @select="handleFilterDeptChange" />
                        </el-form-item>
                        <el-form-item label="所属场景" prop="filterScenarioId">
                            <el-select
                                v-model="adjustQueryParams.currentScenarioId"
                                placeholder="请选择所属场景"
                                clearable
                                style="width: 180px"
                                :disabled="!adjustQueryParams.filterDeptId"
                                @change="handleFilterScenarioChange"
                            >
                                <el-option v-for="scenario in allScenarioList" :key="scenario.scenarioId" :label="scenario.scenarioName" :value="scenario.scenarioId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品" prop="productId">
                            <el-select v-model="adjustQueryParams.productId" placeholder="请选择产品" style="width: 180px" filterable clearable>
                                <el-option v-for="item in productList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="设备名称" prop="deviceName">
                            <el-input :placeholder="'请输入设备名称'" clearable v-model="adjustQueryParams.deviceName" style="width: 180px"></el-input>
                        </el-form-item>
                        <el-form-item label="设备编号" prop="serialNumber">
                            <el-input :placeholder="'请输入设备编号'" clearable v-model="adjustQueryParams.serialNumber" style="width: 180px"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleAdjustQuery">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetAdjustQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 设备调整区域 -->
                <div class="adjust-section">
                    <div class="adjust-left">
                        <div class="table-header">
                            <span class="header-title">当前场景设备</span>
                            <span class="header-count">{{ selectedLeftCount }}/{{ currentScenarioDevices.length }}</span>
                        </div>
                        <el-table ref="leftAdjustTable" :data="currentScenarioDevices" style="width: 100%" max-height="400" @selection-change="handleLeftSelectionChange">
                            <template slot="empty">
                                <el-empty :image-size="100" description="暂无数据"></el-empty>
                            </template>
                            <el-table-column type="selection" width="55" :selectable="checkLeftSelectable"></el-table-column>
                            <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="serialNumber" label="设备编号" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="productName" label="产品名称" show-overflow-tooltip></el-table-column>
                        </el-table>
                    </div>

                    <div class="adjust-center">
                        <el-button type="primary" :disabled="moveRightDisabled" @click="moveToRight">
                            <i class="el-icon-arrow-right"></i>
                        </el-button>
                        <el-button type="primary" :disabled="moveLeftDisabled" @click="moveToLeft">
                            <i class="el-icon-arrow-left"></i>
                        </el-button>
                    </div>

                    <div class="adjust-right">
                        <div class="table-header">
                            <span class="header-title">待调整设备</span>
                            <span class="header-count">{{ selectedRightCount }}/{{ targetDevices.length }}</span>
                        </div>
                        <el-table ref="rightAdjustTable" :data="targetDevices" style="width: 100%" max-height="400" @selection-change="handleRightSelectionChange">
                            <template slot="empty">
                                <el-empty :image-size="100" description="暂无数据"></el-empty>
                            </template>
                            <el-table-column type="selection" width="55"></el-table-column>
                            <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="serialNumber" label="设备编号" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="productName" label="产品名称" show-overflow-tooltip></el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="pagination-container">
                    <pagination
                        v-show="total1 > 0"
                        :total="total1"
                        :page.sync="adjustQueryParams.pageNum"
                        :pager-count="5"
                        :limit.sync="adjustQueryParams.pageSize"
                        :pageSizes="[10, 20, 30, 40, 50, 70, 100]"
                        @pagination="handleAdjustQuery"
                    />
                </div>
                <!-- 目标场景选择 -->
                <div class="target-section">
                    <!--          <el-form ref="targetForm" :model="targetForm" :rules="targetRules"  size="small" :inline="true">-->
                    <!--            &lt;!&ndash;            <el-form-item label="目标场景" prop="targetScenarioId">&ndash;&gt;-->
                    <!--            &lt;!&ndash;              <el-select v-model="editTargetForm.targetScenarioId" placeholder="请选择目标场景" clearable style="width: 300px"&ndash;&gt;-->
                    <!--            &lt;!&ndash;                @change="handleEditTargetScenarioChange">&ndash;&gt;-->
                    <!--            &lt;!&ndash;                <el-option v-for="scenario in editFilteredTargetScenarios" :key="scenario.scenarioId"&ndash;&gt;-->
                    <!--            &lt;!&ndash;                  :label="scenario.scenarioName" :value="scenario.scenarioId" />&ndash;&gt;-->
                    <!--            &lt;!&ndash;              </el-select>&ndash;&gt;-->
                    <!--            &lt;!&ndash;            </el-form-item>&ndash;&gt;-->
                    <!--            <el-form-item label="目标所属机构" prop="filterDeptId">-->
                    <!--              <treeselect v-model="targetForm.targetDeptId" :options="deptOptions" :show-count="true"-->
                    <!--                          placeholder="请选择目标所属机构" style="width: 180px" @select="handleTargetDeptChange" />-->
                    <!--            </el-form-item>-->
                    <!--            <el-form-item label="目标场景" prop="targetScenarioId">-->
                    <!--              <el-select v-model="targetForm.targetScenarioId" placeholder="请选择所属场景" clearable style="width: 180px"-->
                    <!--                         :disabled="!targetForm.targetDeptId" @change="handleEditTargetScenarioChange">-->
                    <!--                <el-option v-for="scenario in editFilteredTargetScenarios" :key="scenario.scenarioId"-->
                    <!--                           :label="scenario.scenarioName" :value="scenario.scenarioId" />-->
                    <!--              </el-select>-->
                    <!--            </el-form-item>-->
                    <!--          </el-form>-->
                </div>
                <!-- 目标场景选择 -->
                <div class="target-section">
                    <el-form ref="targetForm" :model="targetForm" :rules="targetRules" size="small" :inline="true">
                        <!--            <el-form-item label="目标场景" prop="targetScenarioId">-->
                        <!--              <el-select v-model="editTargetForm.targetScenarioId" placeholder="请选择目标场景" clearable style="width: 300px"-->
                        <!--                @change="handleEditTargetScenarioChange">-->
                        <!--                <el-option v-for="scenario in editFilteredTargetScenarios" :key="scenario.scenarioId"-->
                        <!--                  :label="scenario.scenarioName" :value="scenario.scenarioId" />-->
                        <!--              </el-select>-->
                        <!--            </el-form-item>-->
                        <el-form-item label="目标所属机构" prop="filterDeptId">
                            <treeselect v-model="targetForm.targetDeptId" :options="deptOptions" :show-count="true" placeholder="请选择目标所属机构" style="width: 180px" @select="handleTargetDeptChange" />
                        </el-form-item>
                        <el-form-item label="目标场景" prop="targetScenarioId">
                            <el-select v-model="targetForm.targetScenarioId" placeholder="请选择所属场景" clearable style="width: 180px" :disabled="!targetForm.targetDeptId" @change="handleEditTargetScenarioChange">
                                <el-option v-for="scenario in editFilteredTargetScenarios" :key="scenario.scenarioId" :label="scenario.scenarioName" :value="scenario.scenarioId" />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="confirmAdjust" :disabled="!canConfirm">确认调整</el-button>
                <el-button @click="cancelAdjust">取 消</el-button>
            </div>
        </el-dialog>

        <!-- 第二层设备调整对话框 -->
        <el-dialog :close-on-click-modal="false" title="设备调整" :visible.sync="deviceAdjustFromEditDialog" width="1200px" append-to-body>
            <div class="device-adjust-container">
                <!-- 搜索条件 -->
                <div class="search-section">
                    <el-form ref="editAdjustQueryForm" :model="editAdjustQueryParams" size="small" :inline="true">
                        <el-form-item label="当前场景" prop="currentScenarioId">
                            <el-select
                                v-model="editAdjustQueryParams.currentScenarioId"
                                placeholder="请选择当前场景"
                                clearable
                                style="width: 200px"
                                @change="handleEditCurrentScenarioChange"
                                :disabled="editAdjustDisabled"
                            >
                                <el-option v-for="scenario in editAllScenarioList" :key="scenario.scenarioId" :label="scenario.scenarioName" :value="scenario.scenarioId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="设备编号" prop="serialNumber">
                            <el-input v-model="editAdjustQueryParams.serialNumber" placeholder="请输入设备编号" clearable style="width: 200px" @keyup.enter.native="handleEditAdjustQuery" :disabled="editAdjustDisabled" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleEditAdjustQuery" :disabled="editAdjustDisabled">搜索</el-button>
                            <el-button icon="el-icon-refresh" size="mini" @click="resetEditAdjustQuery" :disabled="editAdjustDisabled">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 设备调整区域 -->
                <div class="adjust-section">
                    <div class="adjust-left">
                        <div class="table-header">
                            <span class="header-title">当前场景设备</span>
                            <span class="header-count">{{ editSelectedLeftCount }}/{{ editCurrentScenarioDevices.length }}</span>
                        </div>
                        <el-table ref="editLeftAdjustTable" :data="editCurrentScenarioDevices" style="width: 100%" max-height="400" @selection-change="handleEditLeftSelectionChange">
                            <template slot="empty">
                                <el-empty :image-size="100" description="暂无数据"></el-empty>
                            </template>
                            <el-table-column type="selection" width="55" :selectable="checkEditLeftSelectable"></el-table-column>
                            <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="serialNumber" label="设备编号" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="productName" label="产品名称" show-overflow-tooltip></el-table-column>
                        </el-table>
                    </div>

                    <div class="adjust-center">
                        <el-button type="primary" :disabled="editMoveRightDisabled" @click="editMoveToRight">
                            <i class="el-icon-arrow-right"></i>
                        </el-button>
                        <el-button type="primary" :disabled="editMoveLeftDisabled" @click="editMoveToLeft">
                            <i class="el-icon-arrow-left"></i>
                        </el-button>
                    </div>

                    <div class="adjust-right">
                        <div class="table-header">
                            <span class="header-title">待调整设备</span>
                            <span class="header-count">{{ editSelectedRightCount }}/{{ editTargetDevices.length }}</span>
                        </div>
                        <el-table ref="editRightAdjustTable" :data="editTargetDevices" style="width: 100%" max-height="400" @selection-change="handleEditRightSelectionChange">
                            <template slot="empty">
                                <el-empty :image-size="100" description="暂无数据"></el-empty>
                            </template>
                            <el-table-column type="selection" width="55"></el-table-column>
                            <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="serialNumber" label="设备编号" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="productName" label="产品名称" show-overflow-tooltip></el-table-column>
                        </el-table>
                    </div>
                </div>

                <!-- 目标场景选择 -->
                <div class="target-section">
                    <el-form ref="editTargetForm" :model="editTargetForm" :rules="editTargetRules" size="small" :inline="true">
                        <!--            <el-form-item label="目标场景" prop="targetScenarioId">-->
                        <!--              <el-select v-model="editTargetForm.targetScenarioId" placeholder="请选择目标场景" clearable style="width: 300px"-->
                        <!--                @change="handleEditTargetScenarioChange">-->
                        <!--                <el-option v-for="scenario in editFilteredTargetScenarios" :key="scenario.scenarioId"-->
                        <!--                  :label="scenario.scenarioName" :value="scenario.scenarioId" />-->
                        <!--              </el-select>-->
                        <!--            </el-form-item>-->
                        <el-form-item label="目标所属机构" prop="filterDeptId">
                            <treeselect v-model="editTargetForm.targetDeptId" :options="deptOptions" :show-count="true" placeholder="请选择目标所属机构" style="width: 180px" @select="handleTargetDeptChange" />
                        </el-form-item>
                        <el-form-item label="目标场景" prop="filterScenarioId">
                            <el-select
                                v-model="editTargetForm.targetScenarioId"
                                placeholder="请选择所属场景"
                                clearable
                                style="width: 180px"
                                :disabled="!editTargetForm.targetDeptId"
                                @change="handleEditTargetScenarioChange"
                            >
                                <el-option v-for="scenario in editFilteredTargetScenarios" :key="scenario.scenarioId" :label="scenario.scenarioName" :value="scenario.scenarioId" />
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="confirmEditAdjust" :disabled="!editCanConfirm">确认调整</el-button>
                <el-button @click="cancelEditAdjust">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listScenario, getScenario, delScenario, addScenario, updateScenario } from '@/api/iot/scenario';
import { listScenarioDevice, delScenarioDevice } from '@/api/iot/scenarioDevice';
import { listDeviceShort, assignDevicesBySerialNumber } from '@/api/iot/device';
import { listScenarioUser } from '@/api/iot/scenarioUser';
import { deptsTreeSelect } from '@/api/system/user';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { listScenarioTypeTree } from '@/api/iot/scenarioType';
import DeviceList from './device-list';
import UserList from './user-list';
import { listProduct } from '@/api/iot/product';
// 引入 city.js 文件
import { optionsCity } from './city.js'; // 新增：引入 city.js 文件
export default {
    name: 'Scenario',
    components: { DeviceList, UserList, Treeselect },
    data() {
        return {
            productList: [],
            //城市静态数据
            optionsCity: optionsCity,
            cascaderProps: {
                // 关键配置：将 label 和 value 都设置为 label 属性
                label: 'label',
                value: 'label',
                children: 'children',
            },
            // 选中的选项卡
            activeName: 'relateDevice',
            // 设备列表加载
            deviceLoading: false,
            // 用户列表加载
            userLoading: false,
            // 分类机构树选项
            typeList: undefined,
            //判断是否为机构管理员
            isEdit: true,
            //归属机构是否可选
            idEditDept: null,
            // 机构树选项
            deptOptions: undefined,
            // 归属机构
            deptName: undefined,
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 总条数
            total1: 0,
            // 场景表格数据
            scenarioList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                scenarioName: null,
                typeId: null,
                templateId: null,
                status: null,
                imgUrl: null,
                tenantId: null,
            },
            // 表单参数
            form: {
                deviceList: [], // 设备列表
                userList: [], // 用户列表
                cityAdress: [], // 确保初始值为数组
            },
            // 表单校验
            rules: {
                scenarioName: [
                    { required: true, message: '场景名称不能为空', trigger: 'blur' },
                    {
                        pattern: /^.{1,50}$/,
                        message: '场景类型最多不超过50个字符',
                        trigger: 'blur',
                    },
                ],
                cityAdress: [{ required: true, message: '请选择省、市、区（县）', trigger: 'change' }],
                tenantId: [{ required: true, message: '请选择归属机构', trigger: 'change' }],
                address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
            },
            // 设备调整相关数据
            deviceAdjustDialog: false,
            allScenarioList: [],
            currentScenarioDevices: [],
            targetDevices: [],
            selectedLeftDevices: [],
            selectedRightDevices: [],
            adjustQueryParams: {
                filterDeptId: null,
                currentScenarioId: null,
                productId: '',
                serialNumber: '',
                deviceName: '',
                pageNum: 1,
                pageSize: 10,
            },
            targetForm: {
                targetScenarioId: null,
                targetDeptId: null,
            },
            targetRules: {
                targetScenarioId: [{ required: true, message: '目标场景不能为空', trigger: 'change' }],
            },
            // 第二层设备调整相关数据
            deviceAdjustFromEditDialog: false,
            editAllScenarioList: [],
            editCurrentScenarioDevices: [],
            editTargetDevices: [],
            editSelectedLeftDevices: [],
            editSelectedRightDevices: [],
            editAdjustQueryParams: {
                filterDeptId: null,
                currentScenarioId: null,
                productId: '',
                serialNumber: '',
                deviceName: '',
                pageNum: 1,
                pageSize: 10,
            },
            editTargetForm: {
                targetScenarioId: null,
                targetDeptId: null,
            },
            editTargetRules: {
                targetScenarioId: [{ required: true, message: '目标场景不能为空', trigger: 'change' }],
            },
            // 场景设备选中的设备列表
            selectedDevices: [],
            // 编辑页面设备调整是否禁用搜索功能
            editAdjustDisabled: false,
        };
    },
    computed: {
        // 左侧选中设备数量
        selectedLeftCount() {
            return this.selectedLeftDevices.length;
        },
        // 右侧选中设备数量
        selectedRightCount() {
            return this.selectedRightDevices.length;
        },
        // 右移按钮是否禁用
        moveRightDisabled() {
            return this.selectedLeftDevices.length === 0;
        },
        // 左移按钮是否禁用
        moveLeftDisabled() {
            return this.selectedRightDevices.length === 0;
        },
        // 过滤后的目标场景列表（排除当前场景）
        filteredTargetScenarios() {
            return this.allScenarioList.filter((scenario) => scenario.scenarioId !== this.adjustQueryParams.currentScenarioId);
        },
        // 是否可以确认调整
        canConfirm() {
            return this.targetDevices.length > 0 && this.targetForm.targetScenarioId;
        },
        // 第二层设备调整计算属性
        // 左侧选中设备数量
        editSelectedLeftCount() {
            return this.editSelectedLeftDevices.length;
        },
        // 右侧选中设备数量
        editSelectedRightCount() {
            return this.editSelectedRightDevices.length;
        },
        // 右移按钮是否禁用
        editMoveRightDisabled() {
            return this.editSelectedLeftDevices.length === 0;
        },
        // 左移按钮是否禁用
        editMoveLeftDisabled() {
            return this.editSelectedRightDevices.length === 0;
        },
        // 过滤后的目标场景列表（排除当前场景）
        editFilteredTargetScenarios() {
            return this.editAllScenarioList.filter((scenario) => scenario.scenarioId !== this.editAdjustQueryParams.currentScenarioId);
        },
        // 是否可以确认调整
        editCanConfirm() {
            return this.editTargetDevices.length > 0 && this.editTargetForm.targetScenarioId;
        },
    },
    created() {
        this.getList();
        this.getProductList();
        this.getDeptTree();
        // 获取分类信息
        this.getTypeTree();
    },
    methods: {
        //选择省、市、区
        handleChange(value) {
            if (value && value.length === 3) {
                this.form.province = value[0]; // 省
                this.form.city = value[1]; // 市
                this.form.district = value[2]; // 区
            } else {
                this.form.province = '';
                this.form.city = '';
                this.form.district = '';
            }
            console.log(this.form);
        },
        /** 查询产品列表 */
        getProductList() {
            this.loading = true;
            const params = {
                pageSize: 999,
            };
            listProduct(params).then((response) => {
                this.productList = response.rows.map((item) => {
                    return { value: item.productId, label: item.productName };
                });
            });
        },
        /** 筛选场景选择改变 */
        handleFilterScenarioChange(value) {
            // // 触发目标机构验证
            // if (this.allotForm.scenarioId) {
            //   this.$nextTick(() => {
            //     this.$refs.allotForm.validateField('scenarioId');
            //   });
            // }
            // 重新查询设备列表
            this.handleQuery();
        },
        /**获取设备数据*/
        getDeviceData(data) {
            // 使用Vue.set或创建新数组确保响应性
            this.$set(this.form, 'deviceList', [...data]);
            console.log(this.form.deviceList, '确定按钮');
        },
        /**获取场景设备列表*/
        getDeviceByScenarioId() {
            console.log('getDeviceByScenarioId');
            if (this.form.scenarioId) {
                this.deviceLoading = true;
                listScenarioDevice({ scenarioId: this.form.scenarioId }).then((response) => {
                    this.form.deviceList = response.rows;
                    this.deviceLoading = false;
                });
            }
        },
        /** 筛选机构选择改变 */
        handleFilterDeptChange(node) {
            // 清空筛选场景选择
            this.queryParams.currentScenarioId = null;
            this.allScenarioList = [];

            if (node && node.id) {
                // 根据选中的机构获取对应的场景列表
                this.getFilterScenarioList(node.id);
            }

            // 重新查询设备列表
            this.handleQuery();
        },
        /** 筛选机构选择改变 */
        handleTargetDeptChange(node) {
            // 清空筛选场景选择
            this.editTargetForm.targetScenarioId = null;
            this.editAllScenarioList = [];

            if (node && node.id) {
                this.editTargetForm.targetDeptId = node.id;
                // 根据选中的机构获取对应的场景列表
                this.getEditAllScenarios(node.id);
            }

            // 重新查询设备列表
            this.handleQuery();
        },
        /**添加场景设备*/
        addDevice() {
            this.$refs.deviceList.open = true;
            if (this.form.deviceList) {
                let deviceList = JSON.parse(JSON.stringify(this.form.deviceList));
                this.$refs.deviceList.selects = deviceList;
                this.$refs.deviceList.ids = deviceList.map((item) => item.serialNumber);
                this.$refs.deviceList.queryParams.deptId = this.form.tenantId;
            }
            this.$refs.deviceList.getList();

            // Add this to refresh after device selection
            // this.$refs.deviceList.$once('device-selected', () => {
            //     if (this.form.scenarioId) {
            //         this.getDeviceByScenarioId();
            //     }
            // });
            this.getDeviceByScenarioId();
        },
        /** 移除场景设备项*/
        handleScenarioDeviceRemove(row) {
            for (let i = 0; i < this.form.deviceList.length; i++) {
                if (row.serialNumber == this.form.deviceList[i].serialNumber) {
                    this.form.deviceList.splice(i, 1);
                }
            }
        },
        /**获取用户数据*/
        getUserData(data) {
            // 使用Vue.set或创建新数组确保响应性
            this.$set(this.form, 'userList', [...data]);
            // console.log(this.form.deviceList, '确定按钮');
            // this.form.userList = data;
        },
        /**获取场景用户列表*/
        getUserByScenarioId() {
            if (this.form.scenarioId) {
                this.userLoading = true;
                listScenarioUser({ scenarioId: this.form.scenarioId }).then((response) => {
                    this.form.userList = response.rows;
                    this.userLoading = false;
                });
            }
        },
        /**添加场景用户*/
        addUser() {
            this.$refs.userList.open = true;
            if (this.form.userList) {
                let userList = JSON.parse(JSON.stringify(this.form.userList));
                this.$refs.userList.selects = userList;
                this.$refs.userList.ids = userList.map((item) => item.scenarioId);
                this.$refs.userList.queryParams.deptId = this.form.tenantId;
            }
            this.$refs.userList.getList();
        },
        /** 移除场景用户项*/
        handleScenarioUserRemove(row) {
            for (let i = 0; i < this.form.userList.length; i++) {
                if (row.userId == this.form.userList[i].userId) {
                    this.form.userList.splice(i, 1);
                }
            }
        },

        getTypeTree() {
            listScenarioTypeTree().then((response) => {
                this.typeList = response.data;
            });
        },
        // // 获取简短分类列表
        // getType() {
        //   listAll().then(response => {
        //     this.typeList = response.data;
        //   })
        // },
        // /** 选择分类 */
        // selectType(val) {
        //   for (var i = 0; i < this.typeList.length; i++) {
        //     if (this.typeList[i].typeId == val) {
        //       this.form.typeName = this.typeList[i].typeName;
        //       return;
        //     }
        //   }
        // },
        /** 查询场景列表 */
        getList() {
            this.loading = true;
            listScenario(this.queryParams).then((response) => {
                this.scenarioList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                scenarioId: null,
                scenarioName: null,
                typeId: null,
                templateId: null,
                status: null,
                imgUrl: null,
                tenantId: null,
                delFlag: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                province: null, // 省
                city: null, // 市
                district: null, // 区
                address: null, // 详细地址
                remark: null,
                cityAdress: [],
            };
            this.activeName = 'relateDevice';
            this.resetForm('form');
        },
        /** 查询机构下拉树结构 */
        getDeptTree() {
            deptsTreeSelect().then((response) => {
                this.deptOptions = response.data;
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.scenarioId);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = '添加场景';
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const scenarioId = row.scenarioId || this.ids;
            getScenario(scenarioId).then((response) => {
                this.form = response.data;
                // 新增：将 province、city 和 district 还原为 form.cityAdress
                const { province, city, district } = this.form;
                if (province && city && district) this.form.cityAdress = [province, city, district].filter(Boolean); // 过滤掉空值
                this.open = true;
                this.title = '修改场景';
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs['form'].validate((valid) => {
                if (valid) {
                    console.log('submitForm++++++', this.form);
                    if (this.form.deviceList && this.form.deviceList.length > 0) {
                        for (let i = 0; i < this.form.deviceList.length; i++) {
                            this.form.deviceList[i].createTime = null;
                        }
                    }

                    if (this.form.scenarioId != null) {
                        updateScenario(this.form).then((response) => {
                            this.$modal.msgSuccess('修改成功');
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addScenario(this.form).then((response) => {
                            this.$modal.msgSuccess('新增成功');
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const scenarioIds = row.scenarioId || this.ids;
            this.$modal
                .confirm('是否确认删除场景编号为"' + scenarioIds + '"的数据项？')
                .then(function () {
                    return delScenario(scenarioIds);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download(
                'iot/scenario/export',
                {
                    ...this.queryParams,
                },
                `scenario_${new Date().getTime()}.xlsx`
            );
        },
        /** 设备调整按钮操作 */
        handleDeviceAdjust() {
            this.deviceAdjustDialog = true;
            this.handleAdjustQuery();
            this.resetAdjustData();
        },
        /** 获取所有场景列表 */
        getAllScenarios() {
            listScenario({ pageSize: 999 }).then((response) => {
                this.allScenarioList = response.rows || [];
            });
        },
        /** 获取筛选场景列表 */
        getFilterScenarioList(tenantId) {
            const params = {
                tenantId: tenantId,
            };
            listScenario(params).then((response) => {
                this.allScenarioList = response.rows || response.data || [];
            });
        },
        /** 重置调整数据 */
        resetAdjustData() {
            this.adjustQueryParams = {
                filterDeptId: null,
                currentScenarioId: null,
                productId: '',
                serialNumber: '',
                deviceName: '',
                pageNum: 1,
                pageSize: 10,
            };
            this.targetForm = {
                targetScenarioId: null,
                targetDeptId: null,
            };
            this.currentScenarioDevices = [];
            this.targetDevices = [];
            this.selectedLeftDevices = [];
            this.selectedRightDevices = [];
        },
        /** 当前场景改变 */
        handleCurrentScenarioChange(scenarioId) {
            if (scenarioId) {
                this.getCurrentScenarioDevices(scenarioId);
            } else {
                this.currentScenarioDevices = [];
            }
        },
        /** 获取当前场景的设备列表 */
        getCurrentScenarioDevices(query) {
            let prarm = {};
            prarm.deptId = query.filterDeptId;
            prarm.scenarioId = query.currentScenarioId;
            prarm.productId = query.productId;
            prarm.serialNumber = query.serialNumber;
            prarm.deviceName = query.deviceName;
            prarm.deptId = query.filterDeptId;
            prarm.pageNum = query.pageNum;
            prarm.pageSize = query.pageSize;
            listScenarioDevice(prarm).then((response) => {
                this.total1 = response.total;
                this.currentScenarioDevices = (response.rows || []).map((device) => ({
                    ...device,
                    isSelected: false,
                }));
                this.filterCurrentDevices();
            });
        },
        /** 过滤当前场景设备 */
        filterCurrentDevices() {
            let filteredDevices = [...this.currentScenarioDevices];

            // 根据设备编号过滤
            if (this.adjustQueryParams.serialNumber) {
                filteredDevices = filteredDevices.filter((device) => device.serialNumber.includes(this.adjustQueryParams.serialNumber));
            }

            // 排除已经在右侧的设备
            const targetSerialNumbers = this.targetDevices.map((device) => device.serialNumber);
            filteredDevices = filteredDevices.filter((device) => !targetSerialNumbers.includes(device.serialNumber));

            this.currentScenarioDevices = filteredDevices;
        },
        /** 调整查询 */
        handleAdjustQuery() {
            this.getCurrentScenarioDevices(this.adjustQueryParams);

            // if (this.adjustQueryParams.currentScenarioId) {
            //   this.getCurrentScenarioDevices(this.adjustQueryParams.currentScenarioId);
            // }
        },
        /** 重置调整查询 */
        resetAdjustQuery() {
            this.adjustQueryParams = {
                filterDeptId: null,
                currentScenarioId: null,
                productId: '',
                serialNumber: '',
                deviceName: '',
                pageNum: 1,
                pageSize: 10,
            };
            this.handleAdjustQuery();
        },
        /** 左侧表格选择改变 */
        handleLeftSelectionChange(selection) {
            this.selectedLeftDevices = selection;
        },
        /** 右侧表格选择改变 */
        handleRightSelectionChange(selection) {
            this.selectedRightDevices = selection;
        },
        /** 检查左侧设备是否可选 */
        checkLeftSelectable(row) {
            return !row.isSelected;
        },
        /** 移动到右侧 */
        moveToRight() {
            if (this.selectedLeftDevices.length === 0) return;

            // 将选中的设备移动到右侧
            this.selectedLeftDevices.forEach((device) => {
                device.isSelected = true;
                this.targetDevices.push({ ...device });
            });

            // 从左侧移除已选设备
            this.currentScenarioDevices = this.currentScenarioDevices.filter((device) => !this.selectedLeftDevices.some((selected) => selected.serialNumber === device.serialNumber));

            // 清空选择
            this.selectedLeftDevices = [];
            this.$refs.leftAdjustTable.clearSelection();
        },
        /** 移动到左侧 */
        moveToLeft() {
            if (this.selectedRightDevices.length === 0) return;

            // 将选中的设备移动回左侧
            this.selectedRightDevices.forEach((device) => {
                device.isSelected = false;
                this.currentScenarioDevices.push({ ...device });
            });

            // 从右侧移除已选设备
            this.targetDevices = this.targetDevices.filter((device) => !this.selectedRightDevices.some((selected) => selected.serialNumber === device.serialNumber));

            // 清空选择
            this.selectedRightDevices = [];
            this.$refs.rightAdjustTable.clearSelection();
        },
        /** 目标场景改变 */
        handleTargetScenarioChange(scenarioId) {
            if (scenarioId) {
                // 找到选中的场景，获取其机构ID
                const selectedScenario = this.editFilteredTargetScenarios.find((scenario) => scenario.scenarioId === scenarioId);
                if (selectedScenario) {
                    this.targetForm.targetDeptId = selectedScenario.tenantId;
                }
            } else {
                this.targetForm.targetDeptId = null;
            }
        },
        /** 确认调整 */
        confirmAdjust() {
            if (this.targetDevices.length === 0) {
                this.$modal.msgWarning('请选择要调整的设备');
                return;
            }

            if (!this.targetForm.targetScenarioId) {
                this.$modal.msgWarning('请选择目标场景');
                return;
            }

            const serialNumbers = this.targetDevices.map((device) => device.serialNumber).join(',');
            const data = {
                deptId: this.targetForm.targetDeptId,
                scenarioId: this.targetForm.targetScenarioId,
                serialNumbers: serialNumbers,
            };

            assignDevicesBySerialNumber(data)
                .then((response) => {
                    this.$modal.msgSuccess('设备调整成功');
                    this.cancelAdjust();
                })
                .catch((error) => {
                    this.$modal.msgError('设备调整失败：' + (error.message || '未知错误'));
                });
        },
        /** 取消调整 */
        cancelAdjust() {
            this.deviceAdjustDialog = false;
            this.resetAdjustData();
        },
        /** 场景设备选择变化 */
        handleDeviceSelectionChange(selection) {
            this.selectedDevices = selection;
        },
        /** 批量设备调整 */
        handleBatchDeviceAdjust() {
            if (this.selectedDevices.length === 0) {
                this.$modal.msgWarning('请选择要调整的设备');
                return;
            }

            this.deviceAdjustFromEditDialog = true;
            this.getEditAllScenarios();
            this.resetEditAdjustData();
            this.editAdjustDisabled = true; // 在重置后再禁用搜索功能

            // 默认选中当前场景
            this.$nextTick(() => {
                this.editAdjustQueryParams.currentScenarioId = this.form.scenarioId;
                this.handleEditCurrentScenarioChange(this.form.scenarioId);

                // 将选中的设备默认移动到右侧待调整区域
                this.$nextTick(() => {
                    this.editTargetDevices = [...this.selectedDevices];
                    // 从左侧移除这些设备
                    const selectedSerialNumbers = this.selectedDevices.map((d) => d.serialNumber);
                    this.editCurrentScenarioDevices = this.editCurrentScenarioDevices.filter((d) => !selectedSerialNumbers.includes(d.serialNumber));
                });
            });
        },
        /** 从编辑页面触发设备调整 */
        handleDeviceAdjustFromEdit(device) {
            this.deviceAdjustFromEditDialog = true;
            this.getEditAllScenarios();
            this.resetEditAdjustData();
            this.editAdjustDisabled = true; // 在重置后再禁用搜索功能

            // 默认选中当前场景
            this.$nextTick(() => {
                this.editAdjustQueryParams.currentScenarioId = this.form.scenarioId;
                this.handleEditCurrentScenarioChange(this.form.scenarioId);

                // 将当前设备默认移动到右侧待调整区域
                this.$nextTick(() => {
                    this.editTargetDevices = [{ ...device }];
                    // 从左侧移除该设备
                    this.editCurrentScenarioDevices = this.editCurrentScenarioDevices.filter((d) => d.serialNumber !== device.serialNumber);
                });
            });
        },
        /** 获取编辑页面所有场景列表 */
        getEditAllScenarios(tenantId) {
            const params = {
                tenantId: tenantId,
            };
            listScenario(params).then((response) => {
                this.editAllScenarioList = response.rows || [];
            });
        },
        /** 重置编辑页面调整数据 */
        resetEditAdjustData() {
            this.editAdjustQueryParams = {
                filterDeptId: null,
                currentScenarioId: null,
                productId: '',
                serialNumber: '',
                deviceName: '',
                pageNum: 1,
                pageSize: 10,
            };
            this.editTargetForm = {
                targetScenarioId: null,
                targetDeptId: null,
            };
            this.editCurrentScenarioDevices = [];
            this.editTargetDevices = [];
            this.editSelectedLeftDevices = [];
            this.editSelectedRightDevices = [];
            // 不在这里重置 editAdjustDisabled，由调用方管理
        },
        /** 编辑页面当前场景改变 */
        handleEditCurrentScenarioChange(scenarioId) {
            if (scenarioId) {
                this.getEditCurrentScenarioDevices(scenarioId);
            } else {
                this.editCurrentScenarioDevices = [];
            }
        },
        /** 获取编辑页面当前场景的设备列表 */
        getEditCurrentScenarioDevices(scenarioId) {
            listScenarioDevice({ scenarioId }).then((response) => {
                this.editCurrentScenarioDevices = (response.rows || []).map((device) => ({
                    ...device,
                    isSelected: false,
                }));
                this.filterEditCurrentDevices();
            });
        },
        /** 过滤编辑页面当前场景设备 */
        filterEditCurrentDevices() {
            let filteredDevices = [...this.editCurrentScenarioDevices];

            // 根据设备编号过滤
            if (this.editAdjustQueryParams.serialNumber) {
                filteredDevices = filteredDevices.filter((device) => device.serialNumber.includes(this.editAdjustQueryParams.serialNumber));
            }

            // 排除已经在右侧的设备
            const targetSerialNumbers = this.editTargetDevices.map((device) => device.serialNumber);
            filteredDevices = filteredDevices.filter((device) => !targetSerialNumbers.includes(device.serialNumber));

            this.editCurrentScenarioDevices = filteredDevices;
        },
        /** 编辑页面调整查询 */
        handleEditAdjustQuery() {
            if (this.editAdjustQueryParams.currentScenarioId) {
                this.getEditCurrentScenarioDevices(this.editAdjustQueryParams.currentScenarioId);
            }
        },
        /** 重置编辑页面调整查询 */
        resetEditAdjustQuery() {
            this.editAdjustQueryParams.serialNumber = '';
            this.handleEditAdjustQuery();
        },
        /** 编辑页面左侧表格选择改变 */
        handleEditLeftSelectionChange(selection) {
            this.editSelectedLeftDevices = selection;
        },
        /** 编辑页面右侧表格选择改变 */
        handleEditRightSelectionChange(selection) {
            this.editSelectedRightDevices = selection;
        },
        /** 检查编辑页面左侧设备是否可选 */
        checkEditLeftSelectable(row) {
            return !row.isSelected;
        },
        /** 编辑页面移动到右侧 */
        editMoveToRight() {
            if (this.editSelectedLeftDevices.length === 0) return;

            // 将选中的设备移动到右侧
            this.editSelectedLeftDevices.forEach((device) => {
                device.isSelected = true;
                this.editTargetDevices.push({ ...device });
            });

            // 从左侧移除已选设备
            this.editCurrentScenarioDevices = this.editCurrentScenarioDevices.filter((device) => !this.editSelectedLeftDevices.some((selected) => selected.serialNumber === device.serialNumber));

            // 清空选择
            this.editSelectedLeftDevices = [];
            this.$refs.editLeftAdjustTable.clearSelection();
        },
        /** 编辑页面移动到左侧 */
        editMoveToLeft() {
            if (this.editSelectedRightDevices.length === 0) return;

            // 将选中的设备移动回左侧
            this.editSelectedRightDevices.forEach((device) => {
                device.isSelected = false;
                this.editCurrentScenarioDevices.push({ ...device });
            });

            // 从右侧移除已选设备
            this.editTargetDevices = this.editTargetDevices.filter((device) => !this.editSelectedRightDevices.some((selected) => selected.serialNumber === device.serialNumber));

            // 清空选择
            this.editSelectedRightDevices = [];
            this.$refs.editRightAdjustTable.clearSelection();
        },
        /** 编辑页面目标场景改变 */
        handleEditTargetScenarioChange(scenarioId) {
            // if (scenarioId) {
            //   // 找到选中的场景，获取其机构ID
            //   const selectedScenario = this.editAllScenarioList.find(scenario => scenario.scenarioId === scenarioId);
            //   if (selectedScenario) {
            //     this.editTargetForm.targetDeptId = selectedScenario.tenantId;
            //   }
            // } else {
            //   this.editTargetForm.targetDeptId = null;
            // }
        },
        /** 编辑页面确认调整 */
        confirmEditAdjust() {
            if (this.editTargetDevices.length === 0) {
                this.$modal.msgWarning('请选择要调整的设备');
                return;
            }

            if (!this.editTargetForm.targetScenarioId) {
                this.$modal.msgWarning('请选择目标场景');
                return;
            }

            const serialNumbers = this.editTargetDevices.map((device) => device.serialNumber).join(',');
            const data = {
                deptId: this.editTargetForm.targetDeptId,
                scenarioId: this.editTargetForm.targetScenarioId,
                serialNumbers: serialNumbers,
            };

            assignDevicesBySerialNumber(data)
                .then((response) => {
                    this.$modal.msgSuccess('设备调整成功');
                    this.cancelEditAdjust();
                    // 刷新当前场景的设备列表
                    this.getDeviceByScenarioId();
                    // 清空选中的设备
                    this.selectedDevices = [];
                })
                .catch((error) => {
                    this.$modal.msgError('设备调整失败：' + (error.message || '未知错误'));
                });
        },
        /** 编辑页面取消调整 */
        cancelEditAdjust() {
            this.deviceAdjustFromEditDialog = false;
            this.resetEditAdjustData();
            this.editAdjustDisabled = false; // 重置禁用状态
        },
    },
};
</script>

<style lang="scss" scoped>
.device-adjust-container {
    .search-section {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .adjust-section {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;

        .adjust-left,
        .adjust-right {
            width: 45%;

            .table-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 4px 4px 0 0;

                .header-title {
                    font-size: 15px;
                    font-weight: bold;
                }

                .header-count {
                    font-size: 15px;
                    font-weight: bold;
                }
            }
        }

        .adjust-center {
            width: 10%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px 0;

            .el-button {
                margin: 5px 0;
                width: 40px;
                height: 40px;
                border-radius: 50%;

                i {
                    font-size: 16px;
                }
            }
        }
    }

    .target-section {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
}

::v-deep .el-dialog__body {
    padding: 20px;
}

.pagination-container {
    text-align: left;
    width: 45%;
    margin-left: 200px;
    margin-top: 20px;
}
</style>
