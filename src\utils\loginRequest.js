import axios from 'axios';

// 专门用于登录的简单请求工具，不包含任何拦截器
const loginService = axios.create({
    baseURL: '/video-api', // 使用代理配置
    withCredentials: false, // 跨域请求不发送cookies
    timeout: 30000,
});

// 添加简单的响应拦截器
loginService.interceptors.response.use(
    (response) => {
        console.log('登录接口响应:', response.data);
        return response.data; // 直接返回data部分
    },
    (error) => {
        console.error('登录接口错误:', error);
        return Promise.reject(error);
    }
);

export default loginService;
