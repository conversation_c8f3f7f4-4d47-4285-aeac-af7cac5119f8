import axios from 'axios';

// 专门用于登录的简单请求工具，不包含任何拦截器
const loginService = axios.create({
    baseURL: '/video-api', // 使用代理配置
    withCredentials: false, // 跨域请求不发送cookies
    timeout: 30000,
});

// 添加请求拦截器用于调试
loginService.interceptors.request.use(
    (config) => {
        console.log('视频登录请求配置:', {
            url: config.url,
            baseURL: config.baseURL,
            fullURL: config.baseURL + config.url,
            method: config.method,
            params: config.params,
        });
        return config;
    },
    (error) => {
        console.error('视频登录请求错误:', error);
        return Promise.reject(error);
    }
);

// 添加简单的响应拦截器
loginService.interceptors.response.use(
    (response) => {
        console.log('视频登录接口响应成功:', {
            status: response.status,
            data: response.data,
            headers: response.headers,
        });
        return response.data; // 直接返回data部分
    },
    (error) => {
        console.error('视频登录接口错误详情:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            config: {
                url: error.config?.url,
                baseURL: error.config?.baseURL,
                method: error.config?.method,
                params: error.config?.params,
            },
        });
        return Promise.reject(error);
    }
);

export default loginService;
