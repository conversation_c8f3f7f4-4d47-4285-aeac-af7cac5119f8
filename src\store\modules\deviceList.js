// store/modules/deviceList.js
const state = {
    queryParams: {
        pageNum: 1,
        pageSize: 12,
        showChild: true,
        deviceName: null,
        productId: null,
        serialNumber: null,
        status: null,
        scenarioId: null,
        deptId: null,
    },
    deviceList: [],
    total: 0,
    showType: 'card',
    selectedDeviceId: [],
    deptOptions: [],
    scenarioOptions: [],
    productList: [],
};

const mutations = {
    SET_QUERY_PARAMS(state, params) {
        state.queryParams = { ...state.queryParams, ...params };
    },
    SET_DEVICE_LIST(state, payload) {
        state.deviceList = payload.list;
        state.total = payload.total;
    },
    SET_SHOW_TYPE(state, type) {
        state.showType = type;
    },
    SET_SELECTED_DEVICE_ID(state, ids) {
        state.selectedDeviceId = ids;
    },
    SET_DEPT_OPTIONS(state, options) {
        state.deptOptions = options;
    },
    SET_SCENARIO_OPTIONS(state, options) {
        state.scenarioOptions = options;
    },
    SET_PRODUCT_LIST(state, list) {
        state.productList = list;
    },
    RESET_STATE(state) {
        Object.assign(state, {
            queryParams: {
                pageNum: 1,
                pageSize: 12,
                showChild: true,
                deviceName: null,
                productId: null,
                serialNumber: null,
                status: null,
                scenarioId: null,
                deptId: null,
            },
            deviceList: [],
            total: 0,
            showType: 'card',
            selectedDeviceId: [],
            deptOptions: [],
            scenarioOptions: [],
            productList: [],
        });
    },
};

export default {
    namespaced: true,
    state,
    mutations,
};
