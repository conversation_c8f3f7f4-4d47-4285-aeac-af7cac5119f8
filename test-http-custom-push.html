<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Http自定义推送功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
        }
        .test-data {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 3px;
            overflow-x: auto;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
        }
        .success {
            color: #67c23a;
        }
        .error {
            color: #f56c6c;
        }
        .warning {
            color: #e6a23c;
        }
        .info {
            color: #409eff;
        }
    </style>
</head>
<body>
    <h1>Http自定义推送功能测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">1. 功能概述</h2>
        <p>本测试页面用于验证规则引擎元数据管理中新增的"Http自定义推送"功能。</p>
        <ul>
            <li><span class="success">✓</span> 支持动态配置HTTP请求参数</li>
            <li><span class="success">✓</span> 支持协议前缀选择（http://、https://）</li>
            <li><span class="success">✓</span> 支持动态键值对配置</li>
            <li><span class="success">✓</span> 支持JSON格式请求体</li>
            <li><span class="success">✓</span> 支持计算公式配置</li>
            <li><span class="success">✓</span> 支持占位符功能</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">2. 测试数据示例</h2>
        <h3>输入数据：</h3>
        <div class="test-data">
            <pre>{
  "name": "设备数据推送测试",
  "type": 7,
  "httpProtocol": "http://",
  "tokenProtocol": "http://",
  "requestHeadersList": [
    {"key": "Authorization", "value": "Bearer token123"},
    {"key": "Content-Type", "value": "application/json"}
  ],
  "requestQueryList": [
    {"key": "version", "value": "1.0"},
    {"key": "format", "value": "json"}
  ],
  "requestConfigList": [
    {"key": "username", "value": "admin"},
    {"key": "password", "value": "seefy@2024!"}
  ],
  "formulaList": [
    {"key": "batteryVoltage", "value": "%s%100.00"},
    {"key": "temperature", "value": "%s*10"}
  ],
  "dataJson": {
    "hostUrl": "localhost:8081/iot/tool/testPost",
    "method": "POST",
    "tokenUrl": "*************/dev-api/loginWithoutCode",
    "requestBody": "{\"serviceId\": 1, \"deviceId\": \"&{deviceId}\", \"payload\": {\"temperature\": \"&{temperature}\"}}"
  }
}</pre>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">3. 预期输出数据</h2>
        <div class="test-data">
            <pre>{
  "requestHeaders": {
    "Authorization": "Bearer token123",
    "Content-Type": "application/json"
  },
  "tokenUrl": "http://*************/dev-api/loginWithoutCode",
  "requestConfig": {
    "username": "admin",
    "password": "seefy@2024!"
  },
  "formula": {
    "batteryVoltage": "%s%100.00",
    "temperature": "%s*10"
  },
  "method": "POST",
  "hostUrl": "http://localhost:8081/iot/tool/testPost",
  "requestQuery": {
    "version": "1.0",
    "format": "json"
  },
  "requestBody": {
    "serviceId": 1,
    "deviceId": "&{deviceId}",
    "payload": {
      "temperature": "&{temperature}"
    }
  }
}</pre>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">4. 测试检查点</h2>
        <h3>前端功能测试：</h3>
        <ul>
            <li><span class="info">□</span> 类型选择下拉框包含"Http自定义推送"选项</li>
            <li><span class="info">□</span> 选择type=7后显示相应的表单项</li>
            <li><span class="info">□</span> 协议前缀选择器正常工作</li>
            <li><span class="info">□</span> 动态添加/删除键值对功能正常</li>
            <li><span class="info">□</span> 表单验证规则生效</li>
            <li><span class="info">□</span> 提示信息正确显示</li>
            <li><span class="info">□</span> 数据提交和回显功能正常</li>
        </ul>

        <h3>数据转换测试：</h3>
        <ul>
            <li><span class="info">□</span> 动态列表转换为对象格式</li>
            <li><span class="info">□</span> URL前缀正确拼接</li>
            <li><span class="info">□</span> JSON字符串解析正确</li>
            <li><span class="info">□</span> 编辑时数据正确回显</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">5. 使用说明</h2>
        <ol>
            <li><span class="warning">注意：</span>需要先在系统字典管理中添加type=7的字典项</li>
            <li>进入规则引擎元数据管理页面</li>
            <li>点击"新增"按钮</li>
            <li>选择类型为"Http自定义推送"</li>
            <li>按照界面提示填写各项配置</li>
            <li>测试保存和编辑功能</li>
        </ol>
    </div>

    <div class="test-section">
        <h2 class="test-title">6. 故障排除</h2>
        <h3>常见问题：</h3>
        <ul>
            <li><span class="error">问题：</span>下拉框中没有"Http自定义推送"选项<br>
                <span class="success">解决：</span>需要在字典管理中添加rule_script_action类型的字典项，值为7</li>
            <li><span class="error">问题：</span>请求体格式验证失败<br>
                <span class="success">解决：</span>确保输入的是有效的JSON格式</li>
            <li><span class="error">问题：</span>数据保存后回显异常<br>
                <span class="success">解决：</span>检查数据转换逻辑，确保对象和列表转换正确</li>
        </ul>
    </div>

    <script>
        // 简单的测试数据验证函数
        function validateTestData() {
            console.log('Http自定义推送功能测试页面已加载');
            console.log('请按照测试检查点逐项验证功能');
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', validateTestData);
    </script>
</body>
</html>
