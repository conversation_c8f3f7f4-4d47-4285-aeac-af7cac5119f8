# 视频监控项目前端菜单功能说明

本文档旨在说明视频监控项目前端各菜单页的功能和内容。

## 菜单结构

- **控制台 (Dashboard)**: 系统主页，展示系统概览、设备状态、流量信息等。
- **分屏监控 (Live)**: 提供多画面实时视频监控功能，支持1、4、6、9分屏模式。
- **通道列表 (Channel)**: 统一展示所有类型的视频通道，包括国标设备、推流、拉流代理等。
- **设备接入 (Device)**:
    - **国标设备 (GB Device)**: 管理符合GB/T 28181标准的设备，包括设备增删改查、状态监控、通道管理等。
    - **推流列表 (Push List)**: 管理推流设备和地址，支持将非国标设备或文件以推流方式接入平台。
    - **拉流代理 (Proxy)**: 管理拉流代理，支持将其他视频源通过代理方式接入平台。
- **组织结构 (Organization)**:
    - **行政区划 (Region)**: 以行政区划（如省、市、区）为单位对设备进行分组和管理。
    - **业务分组 (Group)**: 以业务需求（如部门、用途）为单位对设备进行虚拟分组和管理。
- **录制计划 (Record Plan)**: 配置设备的录像计划，包括录像时间、存储位置等。
- **云端录像 (Cloud Record)**: 查看和管理存储在云端的录像文件，支持按时间、设备等条件检索和回放。
- **媒体节点 (Media Server)**: 管理流媒体服务器节点，包括节点的增删改查、状态监控、负载均衡等。
- **国标级联 (Platform)**: 管理与其他GB/T 28181平台的级联，实现跨平台视频共享。
- **用户管理 (User)**: 管理系统用户，包括用户的增删改查、权限分配等。
- **运维中心 (Operations)**:
    - **平台信息 (System Info)**: 展示平台的版本、运行状态、服务器信息等。
    - **历史日志 (History Log)**: 查看和检索系统的历史操作日志和错误日志。
    - **实时日志 (Real Log)**: 实时显示系统运行日志，方便问题排查。

## 页面功能详解

### 控制台 (Dashboard)
- **文件路径**: `src/views/dashboard/index.vue`
- **功能**:
    - **系统信息概览**: 以图表形式展示CPU、内存、网络、磁盘等系统资源使用情况。
    - **节点负载**: 展示各个媒体服务器节点的负载情况，包括推流、拉流、国标收发流等。
    - **资源统计**: 统计设备、通道、推流、拉流代理的总数和在线数。

### 分屏监控 (Live)
- **文件路径**: `src/views/live/index.vue`
- **功能**:
    - **多画面预览**: 支持1、4、6、9多种分屏布局，同时预览多个视频通道。
    - **云台控制**: 对支持云台的设备进行方向、变倍、聚焦、光圈等控制。
    - **预置点/巡航/扫描**: 调用设备的预置点、巡航组和自动扫描功能。
    - **截图/录像**: 对实时视频进行截图和本地录像。
    - **语音对讲**: 与支持语音对讲的设备进行实时通话。

### 通道列表 (Channel)
- **文件路径**: `src/views/channel/index.vue`
- **功能**:
    - **统一列表**: 集中展示所有国标设备、推流、拉流代理通道。
    - **状态筛选**: 可根据在线/离线状态、通道类型进行筛选。
    - **快速操作**: 支持快速播放、停止、编辑通道信息。

### 设备接入 (Device)

#### 国标设备 (GB Device)
- **文件路径**: `src/views/device/index.vue`, `src/views/device/list.vue`, `src/views/device/channel/index.vue`
- **功能**:
    - **设备管理**: 添加、编辑、删除国标设备。
    - **设备同步**: 与设备同步通道列表。
    - **状态监控**: 查看设备的在线状态、注册信息、心跳时间。
    - **通道管理**: 查看设备下的通道列表，对通道进行播放、录像、编辑等操作。
    - **订阅管理**: 管理目录和位置信息的订阅。

#### 推流列表 (Push List)
- **文件路径**: `src/views/streamPush/index.vue`
- **功能**:
    - **推流管理**: 添加、编辑、删除推流通道。
    - **地址生成**: 生成RTMP、RTSP、WebRTC等协议的推流地址。
    - **状态监控**: 查看推流通道的在线状态。
    - **国标关联**: 可将推流通道关联到国标平台，实现非国标源的国标共享。

#### 拉流代理 (Proxy)
- **文件路径**: `src/views/streamProxy/index.vue`
- **功能**:
    - **代理管理**: 添加、编辑、删除拉流代理任务。
    - **协议支持**: 支持RTSP、RTMP、HLS等多种协议的拉流。
    - **状态监控**: 查看拉流代理任务的状态。
    - **按需拉流**: 可配置为无人观看时自动停止拉流，节省资源。

### 组织结构 (Organization)

#### 行政区划 (Region)
- **文件路径**: `src/views/channel/region/index.vue`
- **功能**:
    - **树状结构**: 以树状结构展示行政区划。
    - **节点管理**: 添加、编辑、删除行政区划节点。
    - **通道挂载**: 将设备通道挂载到对应的行政区划节点下。

#### 业务分组 (Group)
- **文件路径**: `src/views/channel/group/index.vue`
- **功能**:
    - **树状结构**: 以树状结构展示业务分组。
    - **节点管理**: 添加、编辑、删除业务分组节点。
    - **通道挂载**: 将设备通道挂载到对应的业务分组节点下。

### 录制计划 (Record Plan)
- **文件路径**: `src/views/recordPlan/index.vue`
- **功能**:
    - **计划管理**: 添加、编辑、删除录制计划。
    - **时间模板**: 提供周一至周日的时间模板，方便快速配置。
    - **通道关联**: 将录制计划关联到指定的通道。

### 云端录像 (Cloud Record)
- **文件路径**: `src/views/cloudRecord/index.vue`, `src/views/cloudRecord/detail.vue`
- **功能**:
    - **录像检索**: 按应用名、流ID、时间范围、媒体节点等条件检索云端录像。
    - **录像回放**: 提供带时间轴的录像回放播放器。
    - **播放控制**: 支持播放、暂停、停止、快进、快退、倍速播放。
    - **录像下载**: 下载指定的录像文件。

### 媒体节点 (Media Server)
- **文件路径**: `src/views/mediaServer/index.vue`
- **功能**:
    - **节点管理**: 添加、编辑、删除流媒体服务器节点。
    - **状态监控**: 查看节点的在线状态和基本信息。
    - **自动配置**: 支持自动配置ZLM/ABL等流媒体服务。

### 国标级联 (Platform)
- **文件路径**: `src/views/platform/index.vue`
- **功能**:
    - **平台管理**: 添加、编辑、删除上级国标平台。
    - **状态监控**: 查看与上级平台的连接状态。
    - **通道共享**: 选择要共享给上级平台的通道。
    - **目录推送**: 将本地通道目录推送到上级平台。

### 用户管理 (User)
- **文件路径**: `src/views/user/index.vue`
- **功能**:
    - **用户管理**: 添加、编辑、删除用户。
    - **密码管理**: 修改用户密码。
    - **PushKey管理**: 修改用户的推流鉴权Key。
    - **API Key管理**: 为用户生成和管理API Key，用于第三方应用集成。

### 运维中心 (Operations)

#### 平台信息 (System Info)
- **文件路径**: `src/views/operations/systemInfo.vue`
- **功能**: 展示WVP项目的版本信息、项目地址、技术支持等。

#### 历史日志 (History Log)
- **文件路径**: `src/views/operations/historyLog.vue`
- **功能**:
    - **日志查看**: 查看存储在服务器上的历史日志文件。
    - **日志筛选**: 按时间、关键字进行筛选。
    - **日志下载**: 下载指定的日志文件。

#### 实时日志 (Real Log)
- **文件路径**: `src/views/operations/realLog.vue`
- **功能**:
    - **实时显示**: 通过WebSocket实时显示系统日志。
    - **日志过滤**: 输入关键字对实时日志进行过滤。
