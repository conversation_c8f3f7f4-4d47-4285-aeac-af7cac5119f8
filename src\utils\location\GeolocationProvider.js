import LocationErrorHandler from './LocationErrorHandler.js';

/**
 * GeolocationProvider - Wrapper for browser geolocation API
 * Provides promisified access to browser geolocation with error handling
 */
class GeolocationProvider {
    // Track permission denial to avoid repeated prompts in same session
    static _permissionDenied = false;
    static _errorHandler = new LocationErrorHandler({ logLevel: 'info' });
    /**
     * Check if geolocation is available in the current browser
     * @returns {boolean} True if geolocation is supported
     */
    static isAvailable() {
        return 'geolocation' in navigator && !!navigator.geolocation;
    }

    /**
     * Get current position using browser geolocation API
     * @param {Object} options - Geolocation options
     * @param {number} options.timeout - Timeout in milliseconds (default: 10000)
     * @param {boolean} options.enableHighAccuracy - Enable high accuracy (default: false)
     * @param {number} options.maximumAge - Maximum age of cached position (default: 0)
     * @param {boolean} options.skipIfDenied - Skip request if permission was previously denied (default: true)
     * @returns {Promise<Object>} Promise resolving to position data
     */
    static async getCurrentPosition(options = {}) {
        if (!this.isAvailable()) {
            throw new Error('Geolocation is not supported by this browser');
        }

        // Check if permission was previously denied in this session
        if (options.skipIfDenied !== false && this._permissionDenied) {
            throw {
                type: 'PERMISSION_DENIED',
                message: 'Location permission was previously denied in this session',
                originalError: null,
            };
        }

        const defaultOptions = {
            timeout: 10000,
            enableHighAccuracy: false,
            maximumAge: 0,
        };

        const geolocationOptions = { ...defaultOptions, ...options };

        return new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const locationData = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        timestamp: position.timestamp,
                    };

                    this._errorHandler.log('debug', 'GPS position acquired successfully', {
                        accuracy: locationData.accuracy,
                        timestamp: locationData.timestamp,
                    });

                    resolve(locationData);
                },
                (error) => {
                    const locationError = this.handleGeolocationError(error);

                    // Track permission denial to avoid repeated prompts
                    if (error.code === 1) {
                        // PERMISSION_DENIED
                        this._permissionDenied = true;
                        this._errorHandler.log('info', 'User denied geolocation permission', {
                            code: error.code,
                            sessionDenied: true,
                        });
                    } else {
                        this._errorHandler.log('warn', 'Geolocation API error', {
                            code: error.code,
                            type: locationError.type,
                            message: locationError.message,
                        });
                    }

                    reject(locationError);
                },
                geolocationOptions
            );
        });
    }

    /**
     * Handle geolocation API errors and convert to standardized error format
     * @param {GeolocationPositionError} error - Browser geolocation error
     * @returns {Object} Standardized error object
     */
    static handleGeolocationError(error) {
        let errorType;
        let errorMessage;

        switch (error.code) {
            case 1: // PERMISSION_DENIED
                errorType = 'PERMISSION_DENIED';
                errorMessage = 'User denied the request for geolocation';
                break;
            case 2: // POSITION_UNAVAILABLE
                errorType = 'POSITION_UNAVAILABLE';
                errorMessage = 'Location information is unavailable';
                break;
            case 3: // TIMEOUT
                errorType = 'TIMEOUT';
                errorMessage = 'The request to get user location timed out';
                break;
            default:
                errorType = 'UNKNOWN_ERROR';
                errorMessage = 'An unknown error occurred while retrieving location';
                break;
        }

        return {
            type: errorType,
            message: errorMessage,
            originalError: error,
        };
    }

    /**
     * Check if user has previously granted location permission
     * @returns {Promise<string>} Permission state: 'granted', 'denied', or 'prompt'
     */
    static async checkPermission() {
        if (!navigator.permissions) {
            this._errorHandler.log('debug', 'Permissions API not available', {});
            return 'unknown';
        }

        try {
            const permission = await navigator.permissions.query({ name: 'geolocation' });
            this._errorHandler.log('debug', 'Permission state checked', { state: permission.state });
            return permission.state;
        } catch (error) {
            const errorResult = this._errorHandler.handle(error, 'permission-check');
            this._errorHandler.log('warn', 'Failed to check geolocation permission', errorResult);
            return 'unknown';
        }
    }

    /**
     * Reset the permission denial state (useful for testing or new sessions)
     */
    static resetPermissionState() {
        this._permissionDenied = false;
    }

    /**
     * Check if permission was denied in current session
     * @returns {boolean} True if permission was denied
     */
    static isPermissionDenied() {
        return this._permissionDenied;
    }
}

export default GeolocationProvider;
