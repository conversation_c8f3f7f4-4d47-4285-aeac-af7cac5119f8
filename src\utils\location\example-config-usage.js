/**
 * Example Usage of Location Detection Configuration System
 * This file demonstrates how to use the configuration and feature flags
 */

import LocationConfig from './LocationConfig.js';
import FeatureFlags from './FeatureFlags.js';
import LocationDetectionService from './LocationDetectionService.js';
import ConfigValidator from './ConfigValidator.js';
import ConfigTest from './ConfigTest.js';

/**
 * Example 1: Basic Configuration Usage
 */
export function basicConfigurationExample() {
    console.log('=== Basic Configuration Example ===');

    // Get current configuration
    const config = LocationConfig.getConfig();
    console.log('Location detection enabled:', config.enabled);
    console.log('Timeout setting:', config.timeout);
    console.log('Default center:', config.defaultCenter);

    // Check specific settings
    console.log('Cache enabled:', LocationConfig.isCacheEnabled());
    console.log('IP fallback enabled:', LocationConfig.isIPFallbackEnabled());
    console.log('GPS timeout:', LocationConfig.getTimeout('gps'));
}

/**
 * Example 2: Feature Flags Usage
 */
export function featureFlagsExample() {
    console.log('=== Feature Flags Example ===');

    // Check if location detection is available
    if (!FeatureFlags.isLocationDetectionEnabled()) {
        console.log('Location detection is disabled');
        return;
    }

    // Check capabilities
    const canUseGPS = FeatureFlags.canUseGPSDetection();
    const canUseIP = FeatureFlags.canUseIPDetection();

    console.log('GPS detection available:', canUseGPS);
    console.log('IP detection available:', canUseIP);

    if (!canUseGPS && !canUseIP) {
        console.log('No location detection methods available');
        return;
    }

    // Get runtime configuration
    const runtimeConfig = FeatureFlags.getRuntimeConfig();
    console.log('Runtime config:', runtimeConfig);
}

/**
 * Example 3: Service Initialization with Configuration
 */
export function serviceInitializationExample() {
    console.log('=== Service Initialization Example ===');

    // Check availability first
    const availability = new LocationDetectionService().checkAvailability();

    if (!availability.available) {
        console.log('Location detection not available:', availability.reasons);
        return;
    }

    // Initialize service with default configuration
    const locationService = new LocationDetectionService();

    // Or initialize with custom options (overrides config)
    const customLocationService = new LocationDetectionService({
        timeout: 5000,
        enableIPFallback: false,
        enableCache: true,
    });

    console.log('Service initialized with configuration');
    console.log('Default service config:', locationService.getConfiguration());
    console.log('Custom service config:', customLocationService.getConfiguration());
}

/**
 * Example 4: Configuration Validation
 */
export function configurationValidationExample() {
    console.log('=== Configuration Validation Example ===');

    const config = LocationConfig.getConfig();
    const validation = ConfigValidator.validateConfiguration(config);

    if (validation.isValid) {
        console.log('✅ Configuration is valid');
    } else {
        console.log('❌ Configuration has errors:');
        validation.errors.forEach((error) => console.log('  -', error));
    }

    if (validation.warnings.length > 0) {
        console.log('⚠️ Configuration warnings:');
        validation.warnings.forEach((warning) => console.log('  -', warning));
    }

    if (validation.recommendations.length > 0) {
        console.log('💡 Recommendations:');
        validation.recommendations.forEach((rec) => console.log('  -', rec));
    }

    // Generate full report
    const report = ConfigValidator.generateReport(config);
    console.log('\n' + report);
}

/**
 * Example 5: Configuration Testing
 */
export async function configurationTestingExample() {
    console.log('=== Configuration Testing Example ===');

    // Quick configuration check
    ConfigTest.quickCheck();

    // Run comprehensive tests
    const testResults = await ConfigTest.runConfigurationTest();

    console.log('Test Results Summary:');
    console.log(`Passed: ${testResults.summary.passed}`);
    console.log(`Failed: ${testResults.summary.failed}`);
    console.log(`Warnings: ${testResults.summary.warnings}`);

    // Generate test report
    const report = ConfigTest.generateTestReport(testResults);
    console.log('\n' + report);
}

/**
 * Example 6: Environment-Specific Configuration
 */
export function environmentSpecificExample() {
    console.log('=== Environment-Specific Configuration Example ===');

    const config = LocationConfig.getConfig();
    const env = process.env.NODE_ENV || 'development';

    console.log(`Current environment: ${env}`);

    switch (env) {
        case 'development':
            console.log('Development settings:');
            console.log('- Debug logging:', config.enableDebugLogging);
            console.log('- Performance monitoring:', config.enablePerformanceMonitoring);
            console.log('- Timeout:', config.timeout);
            break;

        case 'production':
            console.log('Production settings:');
            console.log('- Location detection:', config.enabled);
            console.log('- Cache enabled:', config.enableCache);
            console.log('- Timeout (optimized):', config.timeout);
            break;

        case 'test':
            console.log('Test settings:');
            console.log('- Location detection likely disabled for consistent testing');
            console.log('- Cache disabled for test isolation');
            break;

        default:
            console.log('Unknown environment, using default settings');
    }
}

/**
 * Example 7: Dynamic Configuration Updates
 */
export function dynamicConfigurationExample() {
    console.log('=== Dynamic Configuration Example ===');

    const locationService = new LocationDetectionService();

    // Get initial configuration
    console.log('Initial config:', locationService.getConfiguration());

    // Update service configuration at runtime
    locationService.updateConfig({
        timeout: 15000,
        enableIPFallback: true,
        enableCache: false,
    });

    console.log('Updated config:', locationService.getConfiguration());

    // Reload configuration from environment (if env vars changed)
    locationService.reloadConfiguration();
    console.log('Reloaded config:', locationService.getConfiguration());
}

/**
 * Example 8: Error Handling with Configuration
 */
export async function errorHandlingExample() {
    console.log('=== Error Handling Example ===');

    const locationService = new LocationDetectionService();

    try {
        // Attempt location detection
        const location = await locationService.detectUserLocation();
        console.log('Location detected:', location);
    } catch (error) {
        // Handle errors gracefully
        const friendlyMessage = locationService.getUserFriendlyErrorMessage(error);
        console.log('User-friendly error:', friendlyMessage);

        // Get error statistics
        const errorStats = locationService.getErrorStats();
        console.log('Error statistics:', errorStats);
    }
}

/**
 * Run all examples
 */
export async function runAllExamples() {
    console.log('🚀 Running Location Detection Configuration Examples\n');

    try {
        basicConfigurationExample();
        console.log('\n');

        featureFlagsExample();
        console.log('\n');

        serviceInitializationExample();
        console.log('\n');

        configurationValidationExample();
        console.log('\n');

        await configurationTestingExample();
        console.log('\n');

        environmentSpecificExample();
        console.log('\n');

        dynamicConfigurationExample();
        console.log('\n');

        await errorHandlingExample();
    } catch (error) {
        console.error('Example execution failed:', error);
    }

    console.log('\n✅ All examples completed');
}

// Export individual examples for selective usage
export default {
    basicConfigurationExample,
    featureFlagsExample,
    serviceInitializationExample,
    configurationValidationExample,
    configurationTestingExample,
    environmentSpecificExample,
    dynamicConfigurationExample,
    errorHandlingExample,
    runAllExamples,
};
