import axios from 'axios';
import { Message } from 'element-ui';
import { getVideoToken, videoAutoLogin, clearVideoAuth } from '@/utils/videoAuth';

// create an axios instance
const service = axios.create({
    baseURL: '/video-api', // 使用代理配置
    withCredentials: false, // 跨域请求不发送cookies
    timeout: 30000, // request timeout
});

// 请求队列，用于处理并发请求时的token获取
let isRefreshing = false;
let requestQueue = [];

// request interceptor
service.interceptors.request.use(
    async (config) => {
        // 登录接口白名单，不需要token验证
        console.log('config--->', config.url);
        const whiteList = ['/api/user/login'];
        const isWhiteListUrl = whiteList.some((url) => config.url.indexOf(url) >= 0);

        if (isWhiteListUrl) {
            console.log('登录接口，跳过token验证:', config.url);
            return config;
        }

        // 获取视频模块token
        let videoToken = getVideoToken();

        // 如果没有token，进行自动登录
        if (!videoToken) {
            if (isRefreshing) {
                // 如果正在刷新token，将请求加入队列
                return new Promise((resolve) => {
                    requestQueue.push(() => {
                        config.headers['access-token'] = getVideoToken();
                        resolve(config);
                    });
                });
            }

            isRefreshing = true;
            try {
                console.log('没有token，开始自动登录...');
                videoToken = await videoAutoLogin();
                // 处理队列中的请求
                requestQueue.forEach((callback) => callback());
                requestQueue = [];
            } catch (error) {
                console.error('视频模块自动登录失败:', error);
                requestQueue = [];
                return Promise.reject(error);
            } finally {
                isRefreshing = false;
            }
        }

        // 设置token
        if (videoToken) {
            config.headers['access-token'] = videoToken;
            console.log('添加access-token到请求头:', config.url);
        }

        return config;
    },
    (error) => {
        // do something with request error
        console.log(error); // for debug
        return Promise.reject(error);
    }
);

// response interceptor
service.interceptors.response.use(
    (response) => {
        if (response.config.url.indexOf('/api/user/logout') >= 0) {
            return;
        }
        const res = response.data;
        if (res.code && res.code !== 0) {
            // 如果是401认证失败，清除token并重新登录
            if (res.code === 401) {
                clearVideoAuth();
                console.log('视频模块token已过期，将在下次请求时自动重新登录');
                return Promise.reject(new Error(res.msg || '认证失败'));
            }
            Message({
                message: res.msg,
                type: 'error',
                duration: 5 * 1000,
            });
            return Promise.reject(new Error(res.msg));
        } else {
            return res;
        }
    },
    async (error) => {
        console.log('视频模块请求错误:', error);

        // 处理HTTP状态码401或者响应中的401错误
        if (error.response && (error.response.status === 401 || (error.response.data && error.response.data.code === 401))) {
            console.log('视频模块认证失败，清除token并准备重新登录');
            clearVideoAuth();

            // 如果不是登录接口，尝试重新登录后重试请求
            if (error.config && error.config.url.indexOf('/api/user/login') < 0) {
                try {
                    console.log('尝试自动重新登录...');
                    const newToken = await videoAutoLogin();

                    // 重试原请求
                    error.config.headers['access-token'] = newToken;
                    return service.request(error.config);
                } catch (loginError) {
                    console.error('自动重新登录失败:', loginError);
                    Message({
                        message: '视频模块认证失败，请检查服务器连接',
                        type: 'error',
                        duration: 5 * 1000,
                    });
                    return Promise.reject(loginError);
                }
            }
        } else {
            Message({
                message: error.message || '请求失败',
                type: 'error',
                duration: 5 * 1000,
            });
        }

        return Promise.reject(error);
    }
);

export default service;
