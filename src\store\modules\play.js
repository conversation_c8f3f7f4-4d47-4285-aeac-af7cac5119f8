import { broadcastStart, broadcastStop, play, stop } from '@/api/video/play';

const state = {
    activeStreams: [],
    playingChannels: [],
};

const mutations = {
    SET_ACTIVE_STREAMS(state, streams) {
        state.activeStreams = streams;
    },
    ADD_PLAYING_CHANNEL(state, channel) {
        state.playingChannels.push(channel);
    },
    REMOVE_PLAYING_CHANNEL(state, channelId) {
        state.playingChannels = state.playingChannels.filter((ch) => ch.id !== channelId);
    },
};

const actions = {
    // 播放
    play({ commit }, [deviceId, channelId]) {
        return new Promise((resolve, reject) => {
            play(deviceId, channelId)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 停止播放
    stop({ commit }, [deviceId, channelId]) {
        return new Promise((resolve, reject) => {
            stop(deviceId, channelId)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 开始广播
    broadcastStart({ commit }, [deviceId, channelId, broadcastMode]) {
        return new Promise((resolve, reject) => {
            broadcastStart(deviceId, channelId, broadcastMode)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 停止广播
    broadcastStop({ commit }, [deviceId, channelId]) {
        return new Promise((resolve, reject) => {
            broadcastStop(deviceId, channelId)
                .then((response) => {
                    const { data } = response;
                    resolve(data);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    },

    // 获取活跃流
    getActiveStreams({ commit }, params) {
        return new Promise((resolve, reject) => {
            console.log('获取活跃流:', params);

            const mockData = {
                list: [],
                total: 0,
            };

            commit('SET_ACTIVE_STREAMS', mockData.list);
            resolve(mockData);
        });
    },
};

const getters = {
    activeStreams: (state) => state.activeStreams,
    playingChannels: (state) => state.playingChannels,
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
};
